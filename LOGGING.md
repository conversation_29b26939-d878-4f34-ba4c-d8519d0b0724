# Custom Logging System Documentation

This guide provides an overview of the custom logging system in the PeepsAPI project, including configuration, usage, and best practices for contextual and colored logging.

## Table of Contents

1. [Overview](#1-overview)
2. [Configuration](#2-configuration)
   - [Log Levels](#log-levels)
   - [Log Formats](#log-formats)
   - [Log Level Filtering](#log-level-filtering)
   - [Handlers & Loggers](#handlers--loggers)
   - [Colored Output](#colored-output)
3. [Usage](#3-usage)
   - [ContextualLogger](#contextuallogger)
   - [Adding Context](#adding-context)
   - [Emoji Prefixes](#emoji-prefixes)
4. [Command-Line & Environment Options](#4-command-line--environment-options)
5. [Local Development Logging](#5-local-development-logging)
6. [Server Logging](#6-server-logging)
7. [Best Practices](#7-best-practices)
8. [Troubleshooting](#8-troubleshooting)
9. [Related Fixes](#9-related-fixes)
10. [Reserved LogRecord Attributes](#10-reserved-logrecord-attributes)

## 1. Overview

The PeepsAPI custom logging system is designed for flexibility, clarity, and rich context. It supports multiple log formats, colored output, and automatic inclusion of request/user context. Logging is configured via JSON files and a helper script, and can be controlled at runtime via command-line arguments or environment variables.

## 2. Configuration

### Log Levels

- **DEBUG**: Detailed information for debugging (`DBG` if short log enabled)
- **INFO**: General operational events (`INF` if short log enabled)
- **WARNING**: Unexpected or concerning events (`WRN` if short log enabled)
- **ERROR**: Errors that prevent some functionality (`ERR` if short log enabled)
- **CRITICAL**: Severe errors causing program failure (`CRT` if short log enabled)

Log levels can be set globally or per-logger (e.g., `peepsapi`, `uvicorn`, Azure SDK).

### Log shrinking

You can enable short log level names by setting `SHORT_LOG_LEVEL_ENABLED=true` in your environment.

**Examples:**

With short log level names enabled:

```text
2025-07-16 14:23:01 |DBG| peepsapi.auth | 🔧 Debugging details
2025-07-16 14:23:01 |INF| peepsapi.auth | ✅ User authenticated
2025-07-16 14:23:02 |WRN| peepsapi.auth | ⚠️ Rate limit threshold reached
2025-07-16 14:23:02 |ERR| peepsapi.auth | ❌ Payment failed
2025-07-16 14:23:02 |CRT| peepsapi.auth | ❌ Critical failure
```

### Log Formats

**Minimal**: Level, logger name, message

```text
INFO | peepsapi.auth | ✅ User authenticated
ERROR | peepsapi.auth | ❌ Payment failed
```

**Standard**: Timestamp, level, logger name, message

```text
2025-07-16 14:23:01 | INFO | peepsapi.auth | ✅ User authenticated
2025-07-16 14:23:02 | ERROR | peepsapi.auth | ❌ Payment failed
```

**Detailed**: Timestamp, level, logger name, line number, message

```text
2025-07-16 14:23:01 | INFO | peepsapi.auth:42 | ✅ User authenticated
2025-07-16 14:23:02 | ERROR | peepsapi.auth:87 | ❌ Payment failed
```

**Contextual**: Detailed + context info

```text
2025-07-16 14:23:01 | INFO | peepsapi.auth:42 | ✅ User authenticated | CONTEXT: {'path': '/login', 'method': 'POST', 'client_ip': '127.0.0.1'}
2025-07-16 14:23:02 | ERROR | peepsapi.auth:87 | ❌ Payment failed | CONTEXT: {'person_id': '123', 'payment_id': 'abc123'}
```

**Link**: Timestamp, Pathname, line number, message

```text
2025-07-16 14:23:01 | INFO | '/Users/<USER>/projects/peepsAPI/peepsapi/auth.py':42 | ✅ User authenticated
2025-07-16 14:23:02 | ERROR | '/Users/<USER>/projects/peepsAPI/peepsapi/auth.py':87 | ❌ Payment failed
```

**Full**: Timestamp, Pathname, line number, message, context

```text
2025-07-16 14:23:01 | INFO | /Users/<USER>/projects/peepsAPI/peepsapi/auth.py:42 | ✅ User authenticated | CONTEXT: {'path': '/login', 'method': 'POST', 'client_ip': '127.0.0.1'}
2025-07-16 14:23:02 | ERROR | /Users/<USER>/projects/peepsAPI/peepsapi/auth.py:87 | ❌ Payment failed | CONTEXT: {'person_id': '123', 'payment_id': 'abc123'}
```

Choose format via script or config file.

### Log Level Filtering

When you set a specific log level (e.g., `make start debug ERROR detailed INFO`), all loggers in the application will be configured to that level. This means:

1. For `ERROR` level, only ERROR and CRITICAL logs will be displayed
2. For `WARNING` level, only WARNING, ERROR, and CRITICAL logs will be displayed
3. For `INFO` level, only INFO, WARNING, ERROR, and CRITICAL logs will be displayed
4. For `DEBUG` level, all logs will be displayed

This filtering applies to all loggers in the application, including:

- All peepsapi modules
- Uvicorn server logs
- Azure SDK logs (controlled by AZURE_LOG_LEVEL)
- Root logger

For example, running `make start debug ERROR detailed INFO` will:

- Only show ERROR and CRITICAL logs from all sources
- Use the detailed format that includes line numbers
- Set Azure SDK logs to INFO level
- Filter out all DEBUG, INFO, and WARNING logs from the application

### Handlers & Loggers

- Handlers route logs to the console with the chosen format.
- Loggers are defined for each module (e.g., `peepsapi.auth`, `uvicorn`, `azure.core.pipeline.policies.http_logging_policy`).
- The root logger controls fallback behavior.

### Colored Output

- Enable colored log levels and context by setting the `COLOR_ENABLED=true` environment variable.
- Colors improve readability in the console. Short log level names are also colored if enabled.

## 3. Usage

### ContextualLogger


Use the `ContextualLogger` to automatically include request and user context in logs. Log output will be compact and colored if enabled:

```python
from peepsapi.utils.logging import get_logger
logger = get_logger(__name__)

logger.info("✅ User authenticated")
logger.info("Profile updated", person_id=person.id)
logger.error(
    "❌ Payment failed", request=request, person_id=person.id, extra={"payment_id": payment.id}
)
```

#### Context Fields (auto-included when available):

- **Request path**: The URL path of the request
- **Request method**: The HTTP method (GET, POST, etc.)
- **Client IP**: The IP address of the client
- **User agent**: The client's user agent string
- **Person ID**: The ID of the authenticated person
- **Authentication source**: The source of authentication (jwt or azure_ad)
- **For Azure AD auth**: User email, name, and any override person ID

### Adding Context

- Use `extra={...}` to add custom fields
- Avoid reserved LogRecord attribute names (see below)

### Emoji Prefixes

To improve log readability and make it easier to scan logs for specific types of information, we use a standardized set of emoji prefixes for all log messages.

#### Emoji Prefix Table

| Emoji | Meaning / When to Use                    |
| ----- | ---------------------------------------- |
| ✅    | Success / operation completed            |
| ❌    | Error / failure                          |
| ⚠️    | Warning / degraded behavior              |
| 🛠️    | Setup / configuration steps              |
| 🔧    | Debug logs / detailed internals          |
| 🎯    | Starting a specific task / step          |
| 📦    | Package or dependency loading            |
| 🚀    | App or server start                      |
| 🧪    | Validation / test / checks               |
| 📝    | General info / notes                     |
| 🔐    | Authentication / passkey / security logs |
| ⏳    | Waiting / in-progress status             |
| 🧠    | AI features / GPT / reasoning logs       |
| 🧹    | Cleanup / teardown                       |
| 📤    | Sending data / request outbound          |
| 📥    | Receiving data / response inbound        |

#### Usage Guidelines

1. **Always include an emoji prefix** at the beginning of each log message
2. **Choose the most appropriate emoji** based on the message content and context
3. **Be consistent** with emoji usage across similar log messages
4. **Avoid duplicating emojis** if one already exists in the message
5. **Use only one emoji** per log message (the most relevant one)
6. **For structured logs** (JSON format), add a `tag` or `emoji` field instead

#### Examples

```python
# Success logs
logger.info("✅ User successfully authenticated")
logger.info("✅ Payment processed successfully")

# Error logs
logger.error("❌ Failed to connect to database")
logger.error("❌ Payment processing failed: Invalid card")

# Warning logs
logger.warning("⚠️ Rate limit threshold reached")
logger.warning("⚠️ Database connection pool running low")

# Debug logs
logger.debug("🔧 Processing request parameters")
logger.debug("🔧 Query execution plan: SELECT * FROM users")

# Starting tasks
logger.info("🎯 Starting data migration process")
logger.info("🎯 Processing user input: 'Hello world'")

# Authentication logs
logger.info("🔐 New passkey registered for user")
logger.info("🔐 Token refreshed for session")

# AI-related logs
logger.info("🧠 Parsing user intent with GPT")
logger.debug("🧠 Raw GPT response: {...}")

# Data transfer logs
logger.info("📤 Sending request to payment gateway")
logger.info("📥 Received response from external API")
```

## 4. Command-Line & Environment Options

### Script Usage

Configure logging via the `configure_logging.py` script:

```bash
python scripts/configure_logging.py [LOG_LEVEL] [LOG_FORMAT] [MODE] [LOG_LEVEL_AZURE] [COLOR]
```

- `LOG_LEVEL`: DEBUG, INFO, WARNING, ERROR, CRITICAL
- `LOG_FORMAT`: Minimal, Standard, Detailed, Contextual, Link, Full
- `MODE`: debug, start (affects defaults)
- `LOG_LEVEL_AZURE`: Azure SDK log level
- `COLOR`: Enable colored output

### Makefile Shortcuts

- `make start` (standard logging)
- `make start debug` (detailed logging)
- `make start debug ERROR Detailed` (custom level/format)

### Environment Variables

- `LOG_LEVEL=DEBUG make start`
- `COLOR=COLORED make debug`

Command-line arguments override environment variables.

## 5. Local Development Logging

#### Configuring Log Format

You can configure the log format in two ways:

1. **Using the `debug` parameter with other parameters**:

   ```bash
   # For standard format
   make start debug DEBUG Standard INFO COLORED
   ```

2. **Manually editing the configuration file**:
   1. Open the `local-log-config.json` file
   2. Find the "loggers" section
   3. For each logger (peepsapi, peepsapi.auth, etc.), change the "handlers" array:
      - For standard format: `"handlers": ["console"]`
      - For detailed format: `"handlers": ["detailed_console"]`

Example for standard format:

```json
"peepsapi": {
    "handlers": ["console"],
    "level": "DEBUG",
    "propagate": false
}
```

Example for link format:

```json
"peepsapi": {
    "handlers": ["link_console"],
    "level": "DEBUG",
    "propagate": false
}
```

### Additional Environment Variables

You can also control logging through environment variables:

```bash
# Set the log level for the entire application (HTTPS mode)
LOG_LEVEL=DEBUG make start

# Enable audit logging to the database (HTTPS debug mode)
ENABLE_AUDIT_LOGGING=true make start debug

# Combine multiple environment variables with specific log level and format
ENABLE_AUDIT_LOGGING=true make start debug ERROR Detailed
```

**Note**: When using both environment variables and command-line arguments, the command-line arguments will take precedence for configuring the log level and format in the logging configuration file.

## 6. Server Logging

The Docker image now includes a basic logging configuration. When the container starts, Uvicorn loads `docker-log-config.json`, which sets the default log level to `INFO` for all application loggers. You can override this level by providing the `LOG_LEVEL` environment variable when deploying the container or by setting the `logLevel` parameter in the Bicep templates:

```bash
az containerapp update \
  --name  \
  --resource-group  \
  --set-env-vars LOG_LEVEL=DEBUG

# Or using the Makefile wrapper
LOG_LEVEL=DEBUG make deploy-service ENV=dev
```

This allows `logger.info` and `logger.debug` statements to appear in the Azure Container App logs.

## 7. Best Practices

### Using Proper Log Levels

Choose the appropriate log level based on the importance and purpose of the message:

- **DEBUG**: Detailed information, typically useful only for diagnosing problems

  ```python
  logger.debug(f"🔧 Processing request with parameters: {params}")
  ```

- **INFO**: Confirmation that things are working as expected

  ```python
  logger.info(f"✅ User {user_id} successfully authenticated")
  ```

- **WARNING**: An indication that something unexpected happened, or may happen in the near future

  ```python
  logger.warning(f"⚠️ Rate limit threshold reached for user {user_id}")
  ```

- **ERROR**: Due to a more serious problem, the software has not been able to perform some function

  ```python
  logger.error(f"❌ Failed to process payment: {str(error)}")
  ```

- **CRITICAL**: A serious error, indicating that the program itself may be unable to continue running
  ```python
  logger.critical(f"❌ Database connection failed: {str(error)}")
  ```

### Adding Context Information

Always include relevant context information in your log messages:

```python
# Good: Includes context information and emoji
logger.info(
    f"✅ Connection status updated",
    extra={
        "connection_id": connection_id,
        "old_status": old_status,
        "new_status": new_status,
    }
)

# Bad: Missing context and emoji
logger.info("Connection status updated")
```

When logging errors, include details about the error:

```python
try:
    # Some operation
    result = process_payment(payment_id)
except Exception as e:
    logger.error(
        f"❌ Payment processing failed: {str(e)}",
        extra={
            "payment_id": payment_id,
            "error_type": type(e).__name__,
        }
    )
```

### Avoiding Print Statements

Always use the logger instead of print statements:

```python
# Good: Uses logger with emoji
logger.debug(f"🔐 Token generated: {token[:5]}...")

# Bad: Uses print without emoji
print(f"Token generated: {token[:5]}...")
```

**Benefits of using the logger over print statements:**

1. Respects log level configuration (only shows logs at or above the configured level)
2. Includes contextual information (timestamps, module names, etc.)
3. Can be filtered and redirected to different outputs
4. Consistent formatting across the application

### Error Logging

When logging exceptions, follow these best practices:

1. **Log at the appropriate level** (usually ERROR or CRITICAL)
2. **Include the exception message** using `str(e)`
3. **Add context about where the error occurred**
4. **Include relevant IDs and parameters** (but be careful with sensitive data)
5. **Consider including the exception type** using `type(e).__name__`
6. **For critical errors, include the traceback** using `traceback.format_exc()`

Example:

```python
try:
    # Some operation that might fail
    result = process_data(data_id)
except Exception as e:
    logger.error(
        f"❌ Failed to process data: {str(e)}",
        extra={
            "data_id": data_id,
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc() if is_critical else None,
        }
    )
```

## 8. Troubleshooting

### Troubleshooting Authentication Issues

When debugging authentication issues, pay attention to:

1. Look for log messages from the `peepsapi.auth` module
2. Check for any error messages related to token validation
3. Look for messages containing "JWT token valid" or "Azure AD token valid"
4. Check the request state in middleware logs

### Common Logging Problems

- **Missing logs**: Ensure your log level is appropriate (e.g., DEBUG for detailed logs)
- **Too many logs**: Use a higher log level (e.g., ERROR) to reduce verbosity
- **Azure SDK logs**: These are set to WARNING by default but follow the ERROR/CRITICAL setting
- **"Attempt to overwrite 'name' in LogRecord" error**: Avoid using 'name' as a key in the `extra` dictionary when logging, as it conflicts with a built-in LogRecord attribute. Use a more specific key like 'user_name', 'search_name', etc. instead.
- **"Attempt to overwrite 'module' in LogRecord" error**: Avoid using 'module' as a key in the `extra` dictionary when logging, as it conflicts with a built-in LogRecord attribute. Use a more specific key like 'func_module', 'source_module', etc. instead.

## 9. Related Fixes

### Error Handling Decorator Fix

The `handle_exceptions` decorator in `peepsapi/utils/decorators.py` has been updated to support both synchronous and asynchronous functions. This fixes errors like:

```
Error in get_person: object dict can't be used in 'await' expression
```

The decorator now automatically detects whether a function is a coroutine function (defined with `async def`) and handles it appropriately. This allows you to use the decorator with both types of functions without errors.

### LogRecord Reserved Attributes Fix

The `handle_exceptions` decorator in `peepsapi/utils/decorators.py` has been updated to avoid conflicts with Python's built-in LogRecord attributes. Specifically:

1. Changed `module` to `func_module` in the `extra` dictionary passed to logger calls
2. Updated the corresponding field in the ServerError details dictionary

This fixes errors like:

```
KeyError: "Attempt to overwrite 'module' in LogRecord"
```

## 10. Reserved LogRecord Attributes

Python's LogRecord class has several reserved attribute names that should not be used in the `extra` dictionary:

- `name`
- `level`
- `pathname`
- `lineno`
- `msg`
- `args`
- `exc_info`
- `func`
- `sinfo`
- `module`

When logging with the `extra` parameter, always use unique keys that don't conflict with these built-in attributes.

For more details, see the code in `peepsapi/utils/logging.py`, `template-log-config.json`, and `scripts/configure_logging.py`.
