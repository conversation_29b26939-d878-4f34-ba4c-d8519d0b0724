.PHONY: azcheck azdcheck build start debug test clean format format-check lint \
commit docker-prune docker-build docker-clean docker-run docker-stop docker-logs \
generate-certs deploy-infra deploy-service

build:
	touch requirements.txt
	make venv

venv: requirements.txt requirements-peepsapi.txt
	@echo 🔨 Building PeepsAPI...
	@echo 🔑 Setting executable permissions on scripts...
	@chmod +x scripts/*.py
	@echo 🪣 Setting up Python virtual environment...
	@python3 -m venv venv
	@echo 📦 Upgrading PIP...
	@venv/bin/python -m pip install --upgrade pip
	@echo 📦 Installing dependencies...
	@venv/bin/pip install -q -r requirements.txt
	@echo 🔗 Setting up pre-commit hooks...
	@venv/bin/pre-commit install
	@touch venv
	@echo 🌐 Checking hosts file for local.peepsapp.ai entry...
	@if grep -q "127.0.0.1[[:space:]]*local.peepsapp.ai" /etc/hosts; then \
		echo "✅ local.peepsapp.ai entry already exists in hosts file"; \
	else \
		sudo sh -c 'echo "127.0.0.1 local.peepsapp.ai" >> /etc/hosts' && \
		echo "✅ Successfully added local.peepsapp.ai to hosts file" || \
		echo "❌ Failed to add entry. Please run this manually: sudo sh -c 'echo \"127.0.0.1 local.peepsapp.ai\" >> /etc/hosts'"; \
	fi
	@echo ✅ Build complete!

azcheck:
	@echo 🔑 Checking Azure CLI credentials...
	@echo "Signed-in-user: $(shell az account show --query "{Email:user.name}" --output tsv)"
	@echo "Tenant: $(shell az rest --method GET --url https://graph.microsoft.com/v1.0/organization --query "value[0].displayName" --output tsv)"
	@echo ✅ Azure CLI credentials check complete!

azdcheck:
	@echo 🔑 Checking Azure Developer CLI credentials...
	@azd auth login --check-status --output json | grep -q "success" || \
		{ echo "❌ User is not logged in, run 'azd auth login'"; exit 1; }
	@echo ✅ Azure Developer CLI credentials check complete!

generate-certs: certs/cert.pem certs/key.pem

certs/cert.pem certs/key.pem: venv
	@echo "Generating trusted certificates using mkcert..."
	@echo "Note: mkcert must be installed for this to work."
	@echo "If not installed, please install it first:"
	@echo "  macOS: brew install mkcert nss"
	@echo "  Ubuntu/Debian: sudo apt install libnss3-tools mkcert"
	@echo ""
	. venv/bin/activate && python scripts/generate_certs.py

# Start with HTTPS with configurable logging
# All configuration is handled via environment variables.
# See LOGGING.md for more details.
start: azcheck certs/cert.pem certs/key.pem venv
	@echo "🔄 Stopping any existing server on port 8443..."
	@-lsof -ti:8443 | xargs kill -9 2>/dev/null || true
	@echo "🔒 Starting server with HTTPS at: https://local.peepsapp.ai:8443"
	@echo "🚀 Starting server at: https://local.peepsapp.ai:8443"
	@. venv/bin/activate && \
		watchmedo auto-restart --pattern '.env;local-override.env' -- \
			python3 peepsapi/config.py \
				uvicorn peepsapi.main:app \
					--reload \
					--env-file .env \
					--ssl-keyfile=certs/key.pem \
					--ssl-certfile=certs/cert.pem \
					--port=8443 \
					--log-config local-log-config.json

test: azcheck venv
	@echo "Running all tests..."; \
	. venv/bin/activate && pytest tests/; \

# Clean Cache
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info
	find . -type d -name "__pycache__" -exec rm -r {} +
	find . -type f -name "*.pyc" -delete

# Code quality commands
format: venv
	. venv/bin/activate && black .
	. venv/bin/activate && isort .

# Check formatting without making changes
format-check: venv
	. venv/bin/activate && black --check .
	. venv/bin/activate && isort --check .

# Run linting
lint: venv
	. venv/bin/activate && flake8 .

# Run all checks and commit changes
commit: venv
	. venv/bin/activate && python scripts/commit.py

# Deploy infrastructure using Bicep
# Usage: make deploy-infra ENV=dev
# Note: When ENV=local, this will only run the local Azure AD configuration script
deploy-infra: azcheck
	@if [ "$(ENV)" = "local" ]; then \
		$(MAKE) deploy-infra-local; \
	else \
		$(MAKE) deploy-infra-standard; \
	fi

# Internal target for local Azure AD configuration
deploy-infra-local:
	@echo "🔑 Configuring Azure AD for local development..."
	@chmod +x scripts/configure_azure_ad_local.sh
	@scripts/configure_azure_ad_local.sh

# Internal target for standard infrastructure deployment
deploy-infra-standard:
	@echo "🔧 Deploying infrastructure for environment: $(ENV)"
	@echo "🔄 Installing required Azure CLI extensions..."
	@az extension add --name containerapp --only-show-errors || true

	@echo "🔍 Checking resource group..."
	@RESOURCE_GROUP=$${RESOURCE_GROUP:-peepsapp-$(ENV)-rg}; \
	if ! az group show --name $$RESOURCE_GROUP --query name -o tsv 2>/dev/null; then \
		echo "⚙️ Creating resource group $$RESOURCE_GROUP..."; \
		az group create --name $$RESOURCE_GROUP --location westus --output none; \
		echo "⏳ Waiting for resource group creation to propagate..."; \
		sleep 10; \
		if ! az group show --name $$RESOURCE_GROUP --query name -o tsv 2>/dev/null; then \
			echo "❌ Resource group creation failed or has not propagated yet."; \
			exit 1; \
		fi; \
		echo "✅ Resource group created successfully."; \
	else \
		echo "✅ Resource group $$RESOURCE_GROUP already exists."; \
	fi

	@echo "🚀 Deploying Azure resources..."
	@RESOURCE_GROUP=$${RESOURCE_GROUP:-peepsapp-$(ENV)-rg}; \
	az deployment group create \
		--resource-group $$RESOURCE_GROUP \
		--template-file Infra/main.bicep \
		--parameters @Infra/main-parameters-$(ENV).json location=westus \
		--output json \
		--no-wait; \
	echo "⏳ Waiting for deployment to start..."; \
	sleep 10; \
	DEPLOYMENT_NAME=$$(az deployment group list \
		--resource-group $$RESOURCE_GROUP \
		--query "[0].name" -o tsv); \
	if [ -z "$$DEPLOYMENT_NAME" ]; then \
		echo "❌ Deployment did not start properly."; \
		exit 1; \
	fi; \
	echo "⏳ Deployment '$$DEPLOYMENT_NAME' started. Waiting for it to complete..."; \
	DEPLOYMENT_RESULT=$$(az deployment group wait \
		--resource-group $$RESOURCE_GROUP \
		--name $$DEPLOYMENT_NAME \
		--created \
		--interval 10 \
		--timeout 600 \
		--output json 2>&1); \
	DEPLOYMENT_RESULT=$$(az deployment group show \
		--resource-group $$RESOURCE_GROUP \
		--name $$DEPLOYMENT_NAME \
		--output json 2>&1); \
	if [ $$? -ne 0 ]; then \
		echo "❌ Failed to get deployment result:"; \
		echo "$$DEPLOYMENT_RESULT"; \
		exit 1; \
	fi; \
	if [ $$? -ne 0 ]; then \
		echo "❌ Deployment failed with error:"; \
		echo "$$DEPLOYMENT_RESULT"; \
		exit 1; \
	fi; \
	DEPLOYMENT_OUTPUT=$$(echo "$$DEPLOYMENT_RESULT" | jq -r '.properties.outputs'); \
	if [ $$? -ne 0 ]; then \
		echo "❌ Failed to parse deployment output:"; \
		echo "$$DEPLOYMENT_RESULT"; \
		exit 1; \
	fi; \
	\
	echo "📋 Extracting resource information..."; \
	KEY_VAULT_NAME="peepsapp-vault-$(ENV)"; \
	KEY_VAULT_URI=$$(az keyvault show --name $$KEY_VAULT_NAME --resource-group $$RESOURCE_GROUP --query properties.vaultUri -o tsv 2>/dev/null || echo ""); \
	if [ -z "$$KEY_VAULT_URI" ]; then \
		echo "⚠️ Could not retrieve Key Vault URI. Using default format."; \
		KEY_VAULT_URI="https://$$KEY_VAULT_NAME.vault.azure.net/"; \
	fi; \
	COSMOS_DB_NAME="peepsapp-cosmos-db-$(ENV)"; \
	COSMOS_DB_ACCOUNT_NAME="peepsappcosmos$(ENV)"; \
	COSMOS_DB_ENDPOINT="https://$$COSMOS_DB_ACCOUNT_NAME.documents.azure.com:443/"; \
	CONTAINER_APP_NAME="peepsapp-aca-$(ENV)"; \
	CONTAINER_APP_FQDN=$$(az containerapp show --name $$CONTAINER_APP_NAME --resource-group $$RESOURCE_GROUP --query properties.configuration.ingress.fqdn -o tsv 2>/dev/null || echo ""); \
	if [ -z "$$CONTAINER_APP_FQDN" ]; then \
		echo "⚠️ Could not retrieve Container App FQDN."; \
		CONTAINER_APP_FQDN="$$CONTAINER_APP_NAME.azurecontainerapps.io"; \
	fi; \
	MANAGED_IDENTITY_NAME="peepsapp-mid-$(ENV)"; \
	MANAGED_IDENTITY_PRINCIPAL_ID=$$(az identity show --name $$MANAGED_IDENTITY_NAME --resource-group $$RESOURCE_GROUP --query principalId -o tsv 2>/dev/null || echo ""); \
	if [ -z "$$MANAGED_IDENTITY_PRINCIPAL_ID" ]; then \
		echo "⚠️ Could not retrieve Managed Identity Principal ID."; \
	fi; \
        DEPLOY_OPENAI=$$(jq -r '.parameters.deployOpenAi.value // "true"' Infra/main-parameters-$(ENV).json 2>/dev/null); \
        if [ "$$DEPLOY_OPENAI" = "true" ] || [ "$$DEPLOY_OPENAI" = "True" ]; then \
                OPENAI_NAME="peepsapp-openai-$(ENV)"; \
                echo "🔍 Checking OpenAI resource: $$OPENAI_NAME"; \
                OPENAI_RESOURCE_EXISTS=$$(az cognitiveservices account list --resource-group $$RESOURCE_GROUP --query "[?name=='$$OPENAI_NAME'].name" -o tsv 2>/dev/null || echo ""); \
                if [ -z "$$OPENAI_RESOURCE_EXISTS" ]; then \
                        echo "⚠️ OpenAI resource not found in resource group. This could be because:"; \
                        echo "  - Your subscription doesn't have access to Azure OpenAI"; \
                        echo "  - The deployment failed to create the OpenAI resource"; \
                        echo "  - The OpenAI resource was created in a different resource group"; \
                        echo "ℹ️ You can request OpenAI access at https://aka.ms/oai/access"; \
                        OPENAI_ENDPOINT=""; \
                else \
                        echo "✅ OpenAI resource exists in resource group"; \
                        OPENAI_ENDPOINT=$$(az cognitiveservices account show --name $$OPENAI_NAME --resource-group $$RESOURCE_GROUP --query properties.endpoint -o tsv 2>/dev/null || echo ""); \
                        if [ -z "$$OPENAI_ENDPOINT" ]; then \
                                echo "⚠️ Could not retrieve OpenAI endpoint. The resource might not be fully provisioned yet."; \
                                echo "ℹ️ Waiting 30 more seconds for the resource to be fully provisioned..."; \
                                sleep 30; \
                                OPENAI_ENDPOINT=$$(az cognitiveservices account show --name $$OPENAI_NAME --resource-group $$RESOURCE_GROUP --query properties.endpoint -o tsv 2>/dev/null || echo ""); \
                                if [ -z "$$OPENAI_ENDPOINT" ]; then \
                                        echo "⚠️ Still could not retrieve OpenAI endpoint after waiting."; \
                                fi; \
                        fi; \
                        if [ -n "$$OPENAI_ENDPOINT" ]; then \
                                echo "✅ OpenAI endpoint: $$OPENAI_ENDPOINT"; \
                        fi; \
                fi; \
        else \
                echo "ℹ️ deployOpenAi is false - skipping OpenAI resource checks."; \
                OPENAI_NAME=""; \
                OPENAI_ENDPOINT=""; \
        fi; \
	\
	if [ -z "$$KEY_VAULT_NAME" ]; then \
		echo "⚠️ Could not extract Key Vault name. Using default naming convention."; \
		KEY_VAULT_NAME="peepsapp-vault-$(ENV)"; \
		KEY_VAULT_URI="https://$$KEY_VAULT_NAME.vault.azure.net/"; \
		echo "  - Key Vault Name: $$KEY_VAULT_NAME"; \
		echo "  - Key Vault URI: $$KEY_VAULT_URI"; \
	fi; \
	\
	if [ -z "$$COSMOS_DB_ENDPOINT" ]; then \
		echo "⚠️ Could not extract Cosmos DB endpoint. Using default naming convention."; \
		COSMOS_DB_ACCOUNT_NAME="peepsappcosmos$(ENV)"; \
		COSMOS_DB_NAME="peepsapp-cosmos-db-$(ENV)"; \
		COSMOS_DB_ENDPOINT="https://$$COSMOS_DB_ACCOUNT_NAME.documents.azure.com:443/"; \
		echo "  - Cosmos DB Account: $$COSMOS_DB_ACCOUNT_NAME"; \
		echo "  - Cosmos DB Name: $$COSMOS_DB_NAME"; \
		echo "  - Cosmos DB Endpoint: $$COSMOS_DB_ENDPOINT"; \
	fi; \
	\
	echo "🔐 Setting up Key Vault access..."; \
	USER_OBJECT_ID=$$(az ad signed-in-user show --query id -o tsv); \
	. scripts/azure_utils.sh; \
	AZURE_UTILS_SOURCED=true; \
	\
	create_role_assignment \
		"$$USER_OBJECT_ID" \
		"Key Vault Secrets Officer" \
		"/subscriptions/$$(az account show --query id -o tsv)/resourceGroups/$$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$$KEY_VAULT_NAME" \
		"User access to Key Vault: $$KEY_VAULT_NAME"; \
	\
	wait_for_role_propagation \
		"$$USER_OBJECT_ID" \
		"Key Vault Secrets Officer" \
		"/subscriptions/$$(az account show --query id -o tsv)/resourceGroups/$$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$$KEY_VAULT_NAME" \
		5 30; \
	\
	echo "🔑 Retrieving Cosmos DB key..."; \
	RESOURCE_GROUP=$${RESOURCE_GROUP:-peepsapp-$(ENV)-rg}; \
	COSMOS_DB_KEY=$$(az cosmosdb keys list \
		--name $$COSMOS_DB_ACCOUNT_NAME \
		--resource-group $$RESOURCE_GROUP \
		--query primaryMasterKey -o tsv 2>/dev/null || echo ""); \
	if [ -z "$$COSMOS_DB_KEY" ]; then \
		echo "⚠️ Could not retrieve Cosmos DB key."; \
	fi; \
	\
	echo "🔑 Retrieving Azure Blob Storage connectionString..."; \
	RESOURCE_GROUP=$${RESOURCE_GROUP:-peepsapp-$(ENV)-rg}; \
	BLOB_STORAGE_ACCOUNT="peepsappblob$(ENV)"; \
	BLOB_CONNECTION_STRING=$$(az storage account show-connection-string --name $$BLOB_STORAGE_ACCOUNT --resource-group $$RESOURCE_GROUP --query connectionString -o tsv); \
	if [ -z "$$BLOB_STORAGE_ACCOUNT" ] && [ -z "$$BLOB_CONNECTION_STRING" ]; then \
		echo "⚠️ Could not retrieve Azore Blob connection string."; \
	fi; \
	\
	if [ -n "$$OPENAI_NAME" ] && [ "$$OPENAI_NAME" != "null" ] && [ "$$OPENAI_NAME" != "" ]; then \
		echo "🔑 Retrieving OpenAI API key..."; \
		RESOURCE_GROUP=$${RESOURCE_GROUP:-peepsapp-$(ENV)-rg}; \
		OPENAI_API_KEY=$$(az cognitiveservices account keys list \
			--name $$OPENAI_NAME \
			--resource-group $$RESOURCE_GROUP \
			--query key1 -o tsv 2>/dev/null || echo ""); \
	else \
		echo "ℹ️ OpenAI not deployed, skipping API key retrieval."; \
		OPENAI_API_KEY=""; \
	fi; \
	\
	echo "📝 Setting up Key Vault secrets..."; \
	if [ -n "$$KEY_VAULT_NAME" ] && [ -n "$$KEY_VAULT_URI" ]; then \
		. scripts/azure_utils.sh; \
		\
		set_keyvault_secret "$$KEY_VAULT_NAME" "AZURE-KEY-VAULT-URL" "$$KEY_VAULT_URI"; \
		set_keyvault_secret "$$KEY_VAULT_NAME" "COSMOS-ENDPOINT" "$$COSMOS_DB_ENDPOINT"; \
		\
		if [ -n "$$COSMOS_DB_KEY" ]; then \
			set_keyvault_secret "$$KEY_VAULT_NAME" "COSMOS-KEY" "$$COSMOS_DB_KEY"; \
		fi; \
		\
		set_keyvault_secret "$$KEY_VAULT_NAME" "COSMOS-DB" "$$COSMOS_DB_NAME"; \
		\
		set_keyvault_secret "$$KEY_VAULT_NAME" "AZURE-BLOB-CONNECTION-STRING" "$$BLOB_CONNECTION_STRING"; \
	else \
		echo "⚠️ Key Vault name or URI is empty. Skipping secret configuration."; \
	fi; \
	\
	if [ -n "$$OPENAI_NAME" ] && [ "$$OPENAI_NAME" != "null" ] && [ "$$OPENAI_NAME" != "" ] && [ -n "$$OPENAI_ENDPOINT" ]; then \
		echo "📝 Setting up OpenAI secrets in Key Vault..."; \
		\
		echo "🔍 Checking for OpenAI model deployments..."; \
		OPENAI_DEPLOYMENTS=$$(az cognitiveservices account deployment list \
			--name $$OPENAI_NAME \
			--resource-group $$RESOURCE_GROUP \
			--output json 2>/dev/null || echo "[]"); \
		\
		if [ "$$OPENAI_DEPLOYMENTS" = "[]" ]; then \
			echo "⚠️ No OpenAI model deployments found. This could be because:"; \
			echo "  - The model deployment is still in progress (it can take several minutes)"; \
			echo "  - The model deployment failed during infrastructure deployment"; \
			echo "  - The model name or version is not available in your region"; \
			\
			echo "ℹ️ Checking available models in the region..."; \
			AVAILABLE_MODELS=$$(az cognitiveservices account list-models --name $$OPENAI_NAME --resource-group $$RESOURCE_GROUP --query "[?name=='gpt-4o']" -o json 2>/dev/null || echo "[]"); \
			\
			if [ "$$AVAILABLE_MODELS" = "[]" ]; then \
				echo "⚠️ GPT-4o model is not available in this region or for your subscription."; \
			else \
				echo "✅ GPT-4o model is available. Available versions:"; \
				echo "$$AVAILABLE_MODELS" | jq -r '.[].version'; \
				\
				LATEST_VERSION=$$(echo "$$AVAILABLE_MODELS" | jq -r '.[] | select(.isDefaultVersion==true) | .version'); \
				if [ -z "$$LATEST_VERSION" ]; then \
					LATEST_VERSION=$$(echo "$$AVAILABLE_MODELS" | jq -r '.[0].version'); \
				fi; \
				\
				echo "🔄 Attempting to deploy the model manually..."; \
				echo "  - Using version: $$LATEST_VERSION"; \
				\
				az cognitiveservices account deployment create \
					--name $$OPENAI_NAME \
					--resource-group $$RESOURCE_GROUP \
					--deployment-name gpt-4o \
					--model-name gpt-4o \
					--model-version $$LATEST_VERSION \
					--model-format OpenAI \
					--sku-name Standard \
					--capacity 10; \
				\
				if [ $$? -eq 0 ]; then \
					echo "✅ Successfully deployed GPT-4o model manually."; \
					echo "⏳ Waiting for deployment to complete (this may take a few minutes)..."; \
					sleep 60; \
					\
					OPENAI_DEPLOYMENTS=$$(az cognitiveservices account deployment list \
						--name $$OPENAI_NAME \
						--resource-group $$RESOURCE_GROUP \
						--output json 2>/dev/null || echo "[]"); \
				else \
					echo "⚠️ Manual deployment attempt failed."; \
					echo "ℹ️ The model deployment may still be in progress from the Bicep deployment."; \
					echo "ℹ️ You can check the status later with:"; \
					echo "  az cognitiveservices account deployment list --name $$OPENAI_NAME --resource-group $$RESOURCE_GROUP"; \
				fi; \
			fi; \
			\
			OPENAI_DEPLOYMENT_NAME="gpt-4o"; \
			OPENAI_MODEL_NAME="gpt-4o"; \
		else \
			OPENAI_DEPLOYMENT_NAME=$$(echo "$$OPENAI_DEPLOYMENTS" | jq -r '.[0].name'); \
			echo "✅ Found OpenAI model deployment: $$OPENAI_DEPLOYMENT_NAME"; \
			OPENAI_MODEL_NAME=$$OPENAI_DEPLOYMENT_NAME; \
			\
			DEPLOYMENT_DETAILS=$$(echo "$$OPENAI_DEPLOYMENTS" | jq -r '.[0]'); \
			DEPLOYMENT_MODEL=$$(echo "$$DEPLOYMENT_DETAILS" | jq -r '.properties.model.name'); \
			DEPLOYMENT_VERSION=$$(echo "$$DEPLOYMENT_DETAILS" | jq -r '.properties.model.version'); \
			echo "  - Model: $$DEPLOYMENT_MODEL"; \
			echo "  - Version: $$DEPLOYMENT_VERSION"; \
		fi; \
		\
		if [ -n "$$KEY_VAULT_NAME" ] && [ -n "$$KEY_VAULT_URI" ]; then \
			[ -z "$${AZURE_UTILS_SOURCED}" ] && . scripts/azure_utils.sh && AZURE_UTILS_SOURCED=true; \
			\
			if [ -n "$$OPENAI_API_KEY" ]; then \
				set_keyvault_secret "$$KEY_VAULT_NAME" "OPENAI-API-KEY" "$$OPENAI_API_KEY"; \
			fi; \
			\
			set_keyvault_secret "$$KEY_VAULT_NAME" "OPENAI-ENDPOINT" "$$OPENAI_ENDPOINT"; \
			set_keyvault_secret "$$KEY_VAULT_NAME" "OPENAI-API-VERSION" "$$DEPLOYMENT_VERSION"; \
			\
			if [ -n "$$OPENAI_MODEL_NAME" ]; then \
				set_keyvault_secret "$$KEY_VAULT_NAME" "OPENAI-MODEL-NAME" "$$OPENAI_MODEL_NAME"; \
			fi; \
			\
			if [ -n "$$OPENAI_DEPLOYMENT_NAME" ]; then \
				set_keyvault_secret "$$KEY_VAULT_NAME" "OPENAI-DEPLOYMENT" "$$OPENAI_DEPLOYMENT_NAME"; \
			fi; \
		else \
			echo "⚠️ Key Vault name or URI is empty. Skipping OpenAI secret configuration."; \
		fi; \
	else \
		echo "ℹ️ OpenAI not deployed, skipping OpenAI secret configuration."; \
	fi; \
	if [ -n "$$KEY_VAULT_NAME" ] && [ -n "$$KEY_VAULT_URI" ]; then \
		echo "📝 Setting up WebAuthn secrets in Key Vault..."; \
		echo "📝 WebAuthn configuration:"; \
		echo "  - RP ID: $(ENV).peepsapp.ai"; \
		echo "  - RP Name: Peeps App $(ENV)"; \
		echo "  - RP Origin: https://$(ENV).peepsapp.ai"; \
		\
		[ -z "$${AZURE_UTILS_SOURCED}" ] && . scripts/azure_utils.sh && AZURE_UTILS_SOURCED=true; \
		\
		set_keyvault_secret "$$KEY_VAULT_NAME" "WEBAUTHN-RP-ID" "$(ENV).peepsapp.ai"; \
		set_keyvault_secret "$$KEY_VAULT_NAME" "WEBAUTHN-RP-NAME" "Peeps App $(ENV)"; \
		set_keyvault_secret "$$KEY_VAULT_NAME" "WEBAUTHN-RP-ORIGIN" "https://$(ENV).peepsapp.ai"; \
	else \
		echo "⚠️ Key Vault name or URI is empty. Skipping WebAuthn secret configuration."; \
	fi; \
	\
	if [ -n "$$KEY_VAULT_NAME" ] && [ -n "$$KEY_VAULT_URI" ]; then \
		echo "🔑 Generating and setting JWT secret in Key Vault..."; \
		JWT_SECRET=$$(openssl rand -hex 32); \
		[ -z "$${AZURE_UTILS_SOURCED}" ] && . scripts/azure_utils.sh && AZURE_UTILS_SOURCED=true; \
		set_keyvault_secret "$$KEY_VAULT_NAME" "JWT-SECRET" "$$JWT_SECRET"; \
	else \
		echo "⚠️ Key Vault name or URI is empty. Skipping JWT secret configuration."; \
	fi; \
	\
	echo "🔒 Granting Container App access to Key Vault..."; \
	if [ -n "$$MANAGED_IDENTITY_PRINCIPAL_ID" ] && [ -n "$$KEY_VAULT_NAME" ]; then \
		[ -z "$${AZURE_UTILS_SOURCED}" ] && . scripts/azure_utils.sh && AZURE_UTILS_SOURCED=true; \
		\
		create_role_assignment \
			"$$MANAGED_IDENTITY_PRINCIPAL_ID" \
			"Key Vault Secrets User" \
			"/subscriptions/$$(az account show --query id -o tsv)/resourceGroups/$$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$$KEY_VAULT_NAME" \
			"Container App access to Key Vault: $$KEY_VAULT_NAME"; \
	else \
		echo "⚠️ Managed Identity Principal ID or Key Vault name is empty. Skipping Key Vault role assignment."; \
	fi; \
	\
	echo "🔒 Granting Container App access to Container Registry..."; \
	if [ -n "$$MANAGED_IDENTITY_PRINCIPAL_ID" ]; then \
		ACR_NAME="peepsappcr$(ENV)"; \
		[ -z "$${AZURE_UTILS_SOURCED}" ] && . scripts/azure_utils.sh && AZURE_UTILS_SOURCED=true; \
		\
		create_role_assignment \
			"$$MANAGED_IDENTITY_PRINCIPAL_ID" \
			"AcrPull" \
			"/subscriptions/$$(az account show --query id -o tsv)/resourceGroups/$$RESOURCE_GROUP/providers/Microsoft.ContainerRegistry/registries/$$ACR_NAME" \
			"Container App access to Container Registry: $$ACR_NAME"; \
	else \
		echo "⚠️ Managed Identity Principal ID is empty. Skipping Container Registry role assignment."; \
	fi; \
	\
	echo "📋 Setting up Azure AD SSO configuration"; \
	chmod +x scripts/configure_azure_ad.sh; \
	scripts/configure_azure_ad.sh $(ENV);
	echo "✅ Infrastructure deployment fully executed."; \

# Clean	up dangling Docker images, volumes, and build cache
docker-prune:
	@echo ♻️ Removing dangling Docker images, volumes, and build cache...
	docker image prune --force
	docker volume prune --force
	docker builder prune --force
	@echo ✅ Dangling artifacts removed successfully.

# Build Docker image
docker-build: clean
	@echo 🐳 Building Docker image...
	docker build --tag peepsapi:latest .
	make docker-prune
	@docker image ls peepsapi
	@echo ✅ PeepsAPI Docker image built successfully.

# Stop and remove Docker container
docker-stop:
	@echo 🧹 Stopping PeepsAPI Docker container \(timeout: 5s\)
	@-docker stop --time 5 peepsapi
	@echo 🗑️ Removing PeepsAPI Docker container.
	@docker rm --force --volumes peepsapi
	@echo ✅ PeepsAPI Docker container stopped and removed.

# (Re-)create and run Docker container
#
# Azure authentication is attempted via multiple methods in the following order:
#  - Environment variables
#    - List: https://learn.microsoft.com/en-us/python/api/azure-identity/azure.identity.environmentcredential?view=azure-python
#  - Managed Identity (if running in Azure)
#  - Azure CLI (if logged in)
docker-run: azdcheck docker-build docker-stop
	@echo 🚀 Starting PeepsAPI Docker container...
	@docker run --detach --publish 8000:8000 --name peepsapi \
		$(if ${AZURE_TENANT_ID},--env AZURE_TENANT_ID=${AZURE_TENANT_ID}) \
		$(if ${AZURE_CLIENT_ID},--env AZURE_CLIENT_ID=${AZURE_CLIENT_ID}) \
		$(if ${AZURE_CLIENT_SECRET},--env AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}) \
		$(if ${AZURE_AUTHORITY_HOST},--env AZURE_AUTHORITY_HOST=${AZURE_AUTHORITY_HOST}) \
		$(if ${AZURE_CLIENT_CERTIFICATE_PATH},--env AZURE_CLIENT_CERTIFICATE_PATH=${AZURE_CLIENT_CERTIFICATE_PATH}) \
		$(if ${AZURE_CLIENT_CERTIFICATE_PASSWORD},--env AZURE_CLIENT_CERTIFICATE_PASSWORD=${AZURE_CLIENT_CERTIFICATE_PASSWORD}) \
		$(if ${AZURE_CLIENT_SEND_CERTIFICATE_CHAIN},--env AZURE_CLIENT_SEND_CERTIFICATE_CHAIN=${AZURE_CLIENT_SEND_CERTIFICATE_CHAIN}) \
		$(if ${AZURE_AUTHORITY_HOST},--env AZURE_AUTHORITY_HOST=${AZURE_AUTHORITY_HOST}) \
		--volume ~/.azd:/root/.azd \
		peepsapi
	make docker-prune
	@echo ✅ PeepsAPI is running in a Docker container.
	@echo ℹ️ To stop the container, run: make docker-stop

# Print Docker container logs
docker-logs:
	docker logs --follow --tail 200 --timestamps peepsapi

# Remove Docker image
docker-clean: docker-stop
	@echo 🗑️ Removing PeepsAPI Docker image...
	docker rmi --force peepsapi
	@echo ✅ Dangling artifacts and PeepsAPI Docker image removed successfully.
	@echo ℹ️ To remove the last Docker image build cache run: make docker-prune

# Deploy service to Azure Container Apps
# Usage: make deploy-service ENV=dev [LOG_LEVEL=DEBUG]
deploy-service: azcheck
	@echo "🚀 Deploying service to Azure Container Apps for environment: $(ENV)"
	@DATE_TAG=$$(date +%Y%m%d-%H%M); \
	RESOURCE_GROUP="peepsapp-$(ENV)-rg"; \
	ACR_NAME=$$(az acr list --resource-group $$RESOURCE_GROUP --query "[0].name" -o tsv); \
	if [ -z "$$ACR_NAME" ]; then \
		echo "❌ No Container Registry found in resource group $$RESOURCE_GROUP."; \
		exit 1; \
	fi; \
	TAG="peepsapi:$(ENV)-$$DATE_TAG"; \
	echo "🔨 Building Docker image: $$TAG"; \
	az acr build \
		--registry $$ACR_NAME \
		--image $$TAG \
		--file Dockerfile \
		. || exit 1; \
	CONTAINER_APP_NAME=$$(az containerapp list --resource-group $$RESOURCE_GROUP --query "[0].name" -o tsv); \
	if [ -z "$$CONTAINER_APP_NAME" ]; then \
		echo "❌ No Container App found."; \
		exit 1; \
	fi; \
	echo "📦 Deploying image: $$ACR_NAME.azurecr.io/$$TAG"; \
        az containerapp update \
                --name $$CONTAINER_APP_NAME \
                --resource-group $$RESOURCE_GROUP \
                --image $$ACR_NAME.azurecr.io/$$TAG \
                --set-env-vars LOG_LEVEL=$${LOG_LEVEL:-INFO} \
                --output none || { echo "❌ Failed to update container app"; exit 1; }
	echo "✅ Deployed $$TAG"; \
	echo "🌐 https://$(ENV).peepsapp.ai"
