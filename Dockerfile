# Stage 1: Build image for dependencies installation
FROM python:3.12 AS builder

# Install system dependencies
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --profile minimal
ENV PATH="/root/.cargo/bin:${PATH}"

# Install Python dependencies
WORKDIR /app
COPY requirements-peepsapi.txt /app/
RUN pip install --user --no-cache-dir -r requirements-peepsapi.txt

# Stage 2: Build image for running the project with dependencies copied from the builder image
FROM python:3.12-slim

WORKDIR /app

# Install Azure Developer CLI (azd) using Microsoft's installation script
RUN apt-get update && apt-get install --no-install-recommends curl -y && apt-get clean && \
	curl -fsSL https://aka.ms/install-azd.sh | bash && \
	apt-get remove --purge curl -y && apt-get autoremove -y

# Copy Python dependencies from the builder image
COPY --from=builder /root/.local /root/.local
ENV PATH="/root/.local/bin:${PATH}"

# Copy the application code
COPY peepsapi /app/peepsapi
COPY .env /app/
COPY static /app/static
COPY scripts /app/scripts
COPY docker-log-config.json /app/

EXPOSE 8000

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV LOG_LEVEL=INFO

# Create startup script to load secrets from Key Vault
RUN echo '#!/bin/bash\n\
	set -e\n\
	echo "🚀 Starting application initialization..."\n\
	exec uvicorn peepsapi.main:app --env-file .env --host 0.0.0.0 --port 8000 --log-config docker-log-config.json\n\
	' > /app/start.sh && chmod +x /app/start.sh

CMD ["/app/start.sh"]
