# Contributing to <PERSON>eeps<PERSON><PERSON>

Thank you for your interest in contributing to PeepsAPI! This document provides guidelines and instructions for contributing to this project.

## Code Style and Formatting

We use several tools to maintain code quality and consistency:

### Case Conversion Standards

We follow these case conversion standards:

1. **Internal Python Code**: Use PEP8 snake_case for all variable names, function names, method names, and attribute names
2. **API Responses and Requests**: Expose camelCase for frontend compatibility

This is implemented using Pydantic's `alias_generator` in our base model:

```python
# In models/base.py
def to_camel_case(snake_str: str) -> str:
    """Convert a snake_case string to camelCase."""
    components = snake_str.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

class BaseModelWithExtra(BaseModel):
    model_config = {
        "extra": "allow",
        "alias_generator": to_camel_case,  # Convert snake_case to camelCase for API
        "populate_by_name": True,  # Allow populating by field name or alias
    }
```

When defining models, always use snake_case for field names:

```python
class Person(BaseModelWithExtra):
    id: str
    first_name: str  # Will be exposed as "firstName" in API
    last_name: str   # Will be exposed as "lastName" in API
    email_address: str  # Will be exposed as "emailAddress" in API
```

For more details, see the [Case Conversion Standards](docs/case_conversion_standards.md) documentation.

### Black

We use [Black](https://black.readthedocs.io/) for code formatting with a line length of 88 characters. Black is an opinionated formatter that reformats your code in a consistent way.

```bash
# Format all files
make format
```

### isort

We use [isort](https://pycqa.github.io/isort/) to sort and organize imports. Our isort configuration is compatible with Black.

```bash
# Sort imports in all files
make format
```

### Flake8

We use [Flake8](https://flake8.pycqa.org/) for linting to catch common errors and enforce style guidelines.

```bash
# Run linting
make lint
```

## Pre-commit Hooks

We use [pre-commit](https://pre-commit.com/) to run code quality checks automatically before each commit. This ensures that all committed code meets our standards.

Pre-commit hooks are automatically set up when you run `make build`. If you need to set them up manually:

```bash
pip install pre-commit
pre-commit install
```

Once installed, pre-commit will automatically run on `git commit`. If any checks fail, the commit will be aborted, and you'll need to fix the issues before committing again.

### Standardized Commit Process

We provide a standardized way to prepare your code for committing:

```bash
# Run all checks and commit your changes
make commit
```

This command will:
1. Format your code with Black and isort
2. Run linting with Flake8
3. Execute all tests
4. Stage all changes with `git add .`
5. Guide you through creating a standardized commit message
6. Commit your changes

The interactive commit process helps you create well-formatted commit messages following the conventional commits format. Using `make commit` ensures that all developers follow the same process before committing code, which helps maintain code quality and consistency.

## Path Setup

When importing modules from the project root, use the `path_setup.py` module:

```python
# Import path_setup to add project root to Python path
import scripts.path_setup  # noqa: F401 - imported for side effects

# Now you can import modules from the project root
from peepsapi.some_module import SomeClass
```

## Configuration

Configuration is read from environment variables. Environment variables that are not set at application startup will be loaded from the `.env` file, with a fallback to Azure Key Vault.

> **Note:** All sensitive information must be stored in Azure Key Vault only.

> **Note:** Never hardcode sensitive information, such as API keys or database credentials.

> **Note:** All configuration keys must be listed in the `.env` file.

### Adding a New Configuration Variable

To add a new **secret** configuration value, create a new uppercase variable in the `.env` file with an empty value:
```
# in .env

NEW_SECRET_VAR=
```

Then, add the corresponding `NEW-SECRET-VAR` secret to Azure Key Vault.

To add a non-secret configuration value, create a new uppercase variable in the `.env` file with a default value:
```
# in .env

NEW_CONFIG_VAR=default_value
```

### Accessing Configuration Variables

To access configuration variables, use the `config.py` module:
```python
from peepsapi import config

# Retrieve the value of the configuration variable
value = config.get("NEW_CONFIG_VAR")
```

## Pull Request Process

1. Fork the repository and create a new branch for your feature or bugfix
2. Make your changes, following the code style guidelines
3. Run `make commit` to format your code, run linting, and execute tests
4. Stage and commit your changes
5. Submit a pull request with a clear description of the changes
6. Address any feedback from code reviews

## Commit Messages

Write clear, concise commit messages that explain the purpose of your changes. Follow the conventional commits format:

```
feat: add new feature
fix: fix a bug
docs: update documentation
style: formatting changes
refactor: code refactoring without changing functionality
test: add or update tests
chore: maintenance tasks
```

## Questions?

If you have any questions or need help, please open an issue or contact the project maintainers.

Thank you for contributing to PeepsAPI!
