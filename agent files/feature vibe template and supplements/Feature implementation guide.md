### Product Requirements Document - PRD

- Paste [PRD Template](<Feature PRD template.md>) as your prompt and start your iteratation by describing your feature at a very high level.
- Iterate over this PRD and engage your agent by answering questions, if you agent needs more conext and is not embedded in code base (such as chatGPT) use supplement files in this folder and/or enrich them by adding more supplement files or adding into existing files.
- Once satisfied with state of PRD, check it in your branch and share with the team for review.

### Phased Implementation Plan - PIP
- Use the following prompt and give your PRD to your agent
  ```plaintext
  I am going to use {codex/cursor/...} to implement this feature and I need you to take this PRD and propose a high level phased implementation plan. Once we agreed on it we can proceed to produce the plan.
  ````
- Iterate over the plan till it makes sense, you don't have repeat all the details of PRD in this document. Eah phase needs to be standalone code produced that you can send a PR on and merge and or deploy to test.

### Individual Phase Plan
- Use PRD and PIP produced files with following prompt:
```plaintext
I want your output to be valid md.
Use phases we defined, exactly matching to {feature_PIP}.md. Take relevant details from {feature_PRD}.md in order to create {Codex}-friendly work units (e.g., prompt-ready feature slices)
Give me 1 md file for each phase at a time in order of feature listed in PIP which includes Codex-friendly tasks, once reviewed and ready we move to next phase.
```
- For each phase: pass the instruction to your preferred coding agent, review the changes and commit to your working branch.

### Final Review
- Depending on your speed of progress, send each Phase as a PR or send the whole feature as one to merge into main.
