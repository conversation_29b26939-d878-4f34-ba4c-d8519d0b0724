The codebase uses a consistent design system for error handling, logging, and API responses. Here are the key components:
1. Error Handling System:
```python
# peepsapi/utils/error_handling.py
class ServerError(Exception):
    def __init__(
        self,
        message: str,
        error_code: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500,
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
        super().__init__(message)

class ResourceNotFoundError(ServerError):
    def __init__(
        self,
        message: str,
        error_code: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            message=message,
            error_code=error_code,
            details=details,
            status_code=404,
        )
```

2.Logging System:
```python
# peepsapi/utils/logging.py
def get_logger(name: str) -> Logger:
    """Get a logger with consistent formatting."""
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # Add emoji-based log levels
    logging.addLevelName(logging.INFO, "✅ INFO")
    logging.addLevelName(logging.WARNING, "⚠️ WARNING")
    logging.addLevelName(logging.ERROR, "❌ ERROR")
    logging.addLevelName(logging.DEBUG, "🔍 DEBUG")

    return logger
```

3.API Response Format:
```python
# Standard response format
{
    "data": T,  # The actual response data
    "error": {  # Only present if there's an error
        "message": str,
        "code": str,
        "details": Dict[str, Any]
    }
}
```

4.Decorator System:
```python
# peepsapi/utils/decorators.py
def handle_exceptions(error_code_prefix: str):
    """Decorator for consistent error handling."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ServerError as e:
                raise
            except Exception as e:
                logger.error(f"❌ Unexpected error in {func.__name__}", exc_info=True)
                raise ServerError(
                    message=str(e),
                    error_code=f"{error_code_prefix}_UNEXPECTED_ERROR",
                )
        return wrapper
    return decorator
```

5.Database Access Pattern:
```python
# peepsapi/services/cosmos_db.py
class CosmosContainer:
    """Consistent database access pattern."""
    def create_model(self, model: BaseModel) -> Dict[str, Any]:
        """Create a model with validation."""
        return self.container.create_item(body=model.model_dump())

    def read_model(self, id: UUID, partition_key: UUID, model_class: Type[T]) -> T:
        """Read a model with type safety."""
        data = self.container.read_item(
            item=str(id),
            partition_key=str(partition_key)
        )
        return model_class.model_validate(data)
```

6.Project Structure
```plaintext
infra/                  # Iac files
peepsapi/               # main source code
├── ai/                 # AI/ML related functionality
├── auth/               # Authentication and authorization
├── crud/               # Core CRUD operations
│   ├── models/         # Data models
│   ├── routes/         # API endpoints
│   ├── services/       # Business logic
│   └── utils/          # Constants and utility functions
├── dashboard/          # Dashboard functionality
├── models/             # Shared models
├── services/           # Shared services
├── utils/              # Utility functions
├── config.py           # Application configuration
└── main.py             # main app
scripts/                # scripts
tests/                  # tests
makefile                # makefile
```

7.Infrastructure as Code (IaC)
```plaintext
infra/
├── main.bicep                 # Main infrastructure template
├── azure-ad.bicep            # Azure AD configuration
├── azure-blob.bicep          # Blob storage setup
├── container-app.bicep       # Container app configuration
├── container.bicep           # Container definitions
├── key-vault.bicep           # Key Vault setup
├── managed-identity.bicep    # Managed identities
├── openai.bicep             # OpenAI integration
└── parameters/               # Environment-specific parameters
    ├── main-parameters-dev.json
    ├── main-parameters-test.json
    └── main-parameters-stage.json
```

8.Dependency Injection:
```python
# peepsapi/crud/core/containers.py
def get_people_container():
    """Get the people container instance."""
    return people_container

@router.get("/")
def endpoint(container: CosmosContainer = Depends(get_people_container)):
    # Use container
    pass
```

9.Service Layer Pattern:
```python
# peepsapi/crud/services/people_services.py
class PeopleService:
    """Service layer for people operations."""
    def create_person(self, person: Person) -> Person:
        # Business logic
        pass
```

10.Repository Pattern:
```python
# peepsapi/services/cosmos_db.py
class CosmosContainer:
    """Repository pattern for database operations."""
    def create_model(self, model: BaseModel) -> Dict[str, Any]:
        return self.container.create_item(body=model.model_dump())
```
