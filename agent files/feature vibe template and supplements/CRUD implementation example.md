Typical CRUD implementation from the codebase, using the Person entity as an example:

```python
# 1. Model Definition (peepsapi/crud/models/person.py)
class Person(BaseModel):
    id: UUID
    name: str
    email: str
    phone: Optional[str]
    # ... other fields

# 2. Service Layer (peepsapi/crud/services/people_services.py)
class PeopleService:
    def create_person(self, person: Person) -> Person:
        logger.info(f"🎯 Creating person {person.id}")
        people_container = get_people_container()
        try:
            people_container.create_model(person)
            logger.info(f"✅ Successfully created person {person.id}")
            return person
        except Exception as e:
            logger.error(f"❌ Error creating person {person.id}", extra={"error": str(e)})
            raise ServerError(
                message="Error creating person",
                error_code="CREATE_ERROR",
                details={"error": str(e)},
            )

# 3. API Routes (peepsapi/crud/routes/person.py)
@router.get("/", response_model=List[Person])
@handle_exceptions(error_code_prefix="PERSON")
def list_people(container: CosmosContainer = Depends(get_people_container)):
    """Get all people."""
    try:
        people = list(container.read_all_items())
        logger.info(f"✅ Listed {len(people)} people")
        return people
    except Exception as e:
        logger.error("❌ Error listing people", extra={"error": str(e)})
        raise ServerError(
            message="Error listing people",
            error_code="LIST_ERROR",
            details={"error": str(e)},
        )

@router.post("/", response_model=Person)
@handle_exceptions(error_code_prefix="PERSON")
def create_person(person: Person):
    """Create a new person."""
    return people_service.create_person(person)

@router.put("/{person_id}", response_model=Person)
@handle_exceptions(error_code_prefix="PERSON")
def update_person(
    person_id: UUID,
    person: Person,
    container: CosmosContainer = Depends(get_people_container)
):
    """Update an existing person."""
    try:
        container.read_item(item=person_id, partition_key=person_id)
        container.replace_model(model=person)
        return person
    except CosmosResourceNotFoundError:
        raise ResourceNotFoundError(
            message="Person not found",
            error_code="PERSON_NOT_FOUND",
        )

@router.delete("/{person_id}")
@handle_exceptions(error_code_prefix="PERSON")
def delete_person(person_id: UUID, container=Depends(get_people_container)):
    """Delete a person."""
    try:
        container.delete_item(item=person_id, partition_key=person_id)
        return {"ok": True}
    except CosmosResourceNotFoundError:
        raise ResourceNotFoundError(
            message="Person not found",
            error_code="PERSON_NOT_FOUND",
        )
```
