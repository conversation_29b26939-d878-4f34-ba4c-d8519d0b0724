# Agent Task: Refactor LOGGING.md for PeepsAPI

## Objective
Refactor the `LOGGING.md` documentation in the root directory to align with the current code implementation. Ensure clarity, accuracy, and actionable guidance for developers with examples. Most of it is already implemented, but you need to check what information is irrelevant and what is lacking.

## Context & References
- **Code files to reference:**
  - `peepsapi/config.py`
  - `peepsapi/utils/logging.py`
  - `template-log-config.json`
  - `.env` (log configuration section)
- **Current LOGGING.md**: Review for outdated sections, unclear usage, and old config references.

## Requirements

### 1. Update Startup and Local Override Instructions
- Add or update a snippet showing how to use `local-override` and the new `make start` (replace/remove old startup instructions).

### 2. Naming and Context Fields
- Standardize terminology: use `context` and `extra` consistently.
- Update the documentation to reflect that context fields are now passed as `context` (not `extra`), and clarify the difference.
- List the new/updated context fields (see `peepsapi/utils/logging.py`).

### 3. Usage Clarity
- Provide clear, modern usage examples for logging, including how to add context and extra fields.
- Show how to log errors with tracebacks using `exc_info=True` (see below).
- Remove or update any confusing or outdated usage patterns.

### 4. Edit Old Configs
- Update or remove references to old config files or environment variables that are no longer used.
- Ensure all config examples match the current implementation (`template-log-config.json`, `.env`).

### 5. Traceback Logging
- Add a section explaining how to include tracebacks in logs using `exc_info=True`:
  ```python
  logger.error(
      f"❌ Error adding post {post_id} to feed for person {feed_owner_person_id}: {e}",
      extra={
          "person_id": feed_owner_person_id,
          "post_id": post_id,
          "error": str(e),
      },
      exc_info=True,
  )
  ```
- Clarify when and why to use `exc_info=True`.

### 6. Environment Variables
- Update the list of environment variables for logging, based on `.env` and `template-log-config.json`.
- Clearly document each variable, its purpose, and possible values.

### 7. Reserved LogRecord Attributes
- Reiterate the importance of not using reserved LogRecord attribute names in `extra` or `context`.
- Provide a reference list (see `peepsapi/utils/logging.py` and current LOGGING.md).

## Deliverables
- A fully revised `LOGGING.md` that:
  - Is accurate and up-to-date with the codebase
  - Follows the current document format
  - Is clear, concise, and actionable for developers
- Remove outdated or confusing sections
- Add new sections as needed for clarity and completeness

## Best Practices
- Use clear, direct language and actionable steps
- Prefer code snippets and tables for configuration and usage
- Ensure all examples are copy-paste ready and reflect the current implementation
- Highlight common pitfalls and troubleshooting tips
- Take the current log document as a basis and expand/refactor from that
---

**References:**
- `peepsapi/config.py`, `peepsapi/utils/logging.py`, `template-log-config.json`, `.env`
- [PeepsAPI internal logging standards]