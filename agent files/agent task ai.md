# GPT Action Router Enhancement Task

! note: not in use !

## What I Need You To Do

I need you to enhance the AI system by creating a robust, maintainable, and scalable GPT Action Router that integrates with our existing CosmosDB database. This document outlines the requirements and implementation plan.

**Your task is to:**

1. **Follow this document's structure precisely** - Process each section in order, starting with understanding the core flow, then implementing each component according to the principles provided.
2. **Integrate with existing code** - Use the existing CRUD operations in `peepsapi/crud/api/` for database access rather than creating new ones.
3. **Maintain project structure** - Follow our existing project structure, with tests under the main `peepsapi` directory, not under `ai`.
4. **Execute each phase completely** - Implement each phase fully before moving to the next one.
5. **Run tests after each phase** - Execute tests to verify your implementation works correctly.
6. **Start the server for validation** - Run the server using `make start` so I can validate the implementation.
7. **Wait for confirmation** - Only proceed to the next phase after I confirm the current phase is complete.

## Core Flow

The system follows this detailed flow:

1. **Data Context → System Prompt**:

   - Retrieve data from CosmosDB using CRUD operations
   - Clean sensitive information from the data
   - Format the cleaned data as context for the system prompt
   - Provide the system prompt with relevant user context

2. **User Input → Intent Parser**:

   - Process natural language input from the user
   - Classify the intent (search_network, chat, send_message)
   - Extract parameters from the input

3. **Intent Parser → Context Validation**:

   - Each intent has required context parameters (e.g., search_network requires a query)
   - Validate that all required parameters are present
   - Identify missing parameters that need to be requested from the user

4. **Context Validation → Entity Suggestion**:

   - Match extracted entities (e.g., community names) with actual entities in the database using classical search and similarity matching
   - Provide entity IDs along with matched entities
   - Use confidence scoring to rank potential matches
   - Implement a tiered approach to entity matching:
     - High confidence matches: Proceed automatically without confirmation
     - Medium confidence matches: Proceed but include a confirmation in the response
     - Low confidence matches: Inform the user that no suitable match was found
   - Provide clear feedback to users about entity resolution decisions

5. **Entity Suggestion → User Edit**:

   - Present the intended action for user validation and modification
   - Show what action will be executed with which parameters
   - Allow the user to edit the message content before execution (for send_message intent only)
   - User edit is limited - recipients would be selected from a dropdown, not typed freely
   - Message content can be freely edited by the user without content validation
   - User edit should only be implemented for send_message and similar intents
   - Enable user to confirm the action after making any desired edits

6. **User Edit → Action Validation**:

   - System receives the user-edited content
   - System validates only the structural parameters (e.g., recipient ID exists)
   - No validation is performed on the user-edited message content
   - Ensure all required parameters are present
   - User-edited content is treated as valid by definition

7. **Action Validation → Action Dispatch**:

   - Route the validated action to the appropriate handler based on intent
   - Prepare parameters for execution

8. **Action Dispatch → Action Execution**:

   - Execute the action using CRUD operations
   - Handle any errors that occur during execution
   - Process the results

9. **Action Execution → Confirmation**:
   - Provide clear confirmation of the executed action
   - Include relevant details of what was done
   - Handle any follow-up actions if needed

## Core Components

### 1. Database Integration Layer

This layer connects the AI system to the CosmosDB database through existing CRUD operations.

#### Data Retrieval

- Use existing CRUD endpoints in `peepsapi/crud/api/` for all database operations
- Assume CRUD operations already handle user permission filtering
- Implement context retrieval system for all tables with appropriate filtering:

  - **Person data** (`people` container):

    - Include: names, profile pictures, public profile information, emails, social links, user IDs
    - Exclude: private settings, authentication details, sensitive personal information
    - Retrieve with appropriate pagination (limit to most relevant contacts)

  - **Community data** (`communities` container):

    - Include: community names, descriptions, member counts, public information
    - Exclude: private settings, admin-only information, internal metadata
    - Prioritize communities the user is active in

  - **Conversation history** (`conversations` and `messages` containers):

    - Include: conversation IDs, participant names, recent conversation summaries
    - Exclude: full message history, private messages, deleted content
    - Limit to recent and relevant conversations

  - **Posts and comments** (`posts` and `comments` containers):

    - Include: recent post titles, authors, publication dates, engagement metrics
    - Exclude: private posts, deleted content, internal moderation flags
    - Focus on posts from communities the user is active in

  - **Events** (`events` container):
    - Include: upcoming event titles, dates, locations, organizers
    - Exclude: private events, attendee lists, internal event management data
    - Prioritize upcoming events relevant to the user

#### Data Sanitization

- Implement thorough data sanitization to remove all sensitive information:
  - Strip out personal identifiable information not needed for context
  - Remove internal system identifiers and metadata
  - Sanitize any potentially sensitive content from messages and posts
  - Ensure compliance with privacy requirements

#### Context Formatting

- Format cleaned data as structured context for the system prompt:
  - Organize by entity type (people, communities, etc.)
  - Include entity IDs to enable accurate reference and action
  - Structure data hierarchically for efficient processing
  - Prioritize most relevant information first
  - Include relationship data between entities (e.g., user's relationship to communities)

### 2. Intent Parser

This component analyzes user input to determine the user's intent and extract relevant parameters.

#### Intent Classification

- Focus on handling only "search_network", "chat" and "send_message" intents for now
- Use OpenAI's language model to classify user input into one of the supported intents
- Implement robust classification with high accuracy
- Handle edge cases and ambiguous inputs gracefully
- Default to "chat" intent when no clear intent is detected

#### Parameter Extraction

- Extract all relevant parameters from user input:
  - For "search_network": search query, filters
  - For "send_message": recipient, content
  - For "chat": query or conversation topic
- Identify entity references (e.g., community names) for later resolution
- Handle variations in parameter formats and expressions
- Extract implicit parameters from context when possible

#### Intent Model

- Implement ParsedIntent Pydantic model with:
  - Intent classification (string)
  - Parameters dictionary (extracted from input)
  - Required context list (parameters needed from user)
  - Confidence scores for classification and parameter extraction
  - Validation requirements for each parameter
  - Entity references that need resolution

#### Fallback Mechanisms

- Implement graceful fallbacks when intent classification fails
- Provide helpful responses when parameters cannot be extracted
- Allow for conversation repair when misunderstandings occur
- Default to safer intents when ambiguity exists

#### Context Awareness

- Add conversation context awareness to improve intent understanding
- Maintain conversation state to resolve references (e.g., "post to that group")
- Use previous interactions to inform current intent classification
- Ensure parser has access to cleaned data context for entity resolution

#### Intent Normalization

- Normalize variations of the same intent (e.g., "search", "find", "look for")
- Standardize parameter formats for consistent processing
- Handle synonyms and alternative expressions for intents and parameters
- Ensure consistent output format regardless of input variations

### 3. Validation Pipeline

This component ensures that all required information is present and valid before executing actions.

#### Components

##### Context Validator

- Validates that all required parameters for an intent are available
- Identifies missing parameters that need to be requested from the user
- Prioritizes required parameters based on importance
- Provides clear prompts for missing information
- Handles partial context scenarios gracefully

##### Entity Validator

- Validates that referenced entities (e.g., communities, people) exist in the database
- Implements intelligent entity matching for partial or ambiguous references
- Provides entity IDs along with matched entities for accurate reference
- Uses a configurable confidence scoring system to evaluate potential matches
- Implements a tiered matching approach based on confidence levels:
  - High confidence: Select automatically without confirmation
  - Medium confidence: Proceed with confirmation in the response
  - Low confidence: Inform user that no suitable match was found
- Ensures suggested entities are accessible to the user based on permissions
- Provides clear, user-friendly feedback about entity resolution decisions

##### Action Validator

- Validates only structural parameters (e.g., recipient ID exists)
- No validation is performed on user-edited message content
- Verifies that the user has permission to perform the action
- Ensures the action can be executed with the provided parameters
- Validates that required parameters are present
- User-edited content is treated as valid by definition
- Provides detailed feedback on validation failures for structural issues
- Implements intent-specific validation rules for different action types

##### Schema Validator

- Validates data against Pydantic models for type safety
- Ensures data conforms to expected schemas
- Performs data transformation when needed
- Handles validation errors gracefully with informative messages
- Provides detailed error information for debugging

#### Requirements

##### Progressive Validation

- Implement step-by-step validation to identify all issues
- Validate in order of importance to fail fast on critical issues
- Provide cumulative validation results
- Allow for partial validation success with clear indication of remaining issues

##### Clear Feedback

- Support partial validations with clear, actionable feedback
- Provide user-friendly error messages that explain the issue
- Offer suggestions for resolving validation problems
- Use consistent error message formatting

##### Automatic Entity Resolution

- Implement a robust entity resolution system with configurable confidence thresholds
- Use a tiered approach to handle different confidence levels:
  - High confidence matches: Process automatically without user confirmation
  - Medium confidence matches: Process with explicit confirmation in the response
  - Low confidence matches: Return appropriate error message
- Provide informative feedback about entity resolution decisions
- Use consistent, user-friendly messaging for all resolution outcomes
- Ensure the system degrades gracefully when perfect matches aren't available

##### Suggestion Mechanism

- Suggest corrections based on available data
- Include entity IDs in suggestions for accurate reference
- Format suggestions in a user-friendly way
- Provide context for why a suggestion is being made

### 4. Error Handling

This component provides a robust system for handling errors at all levels of the application.

#### Error Hierarchy

##### Base AI Errors

- Create a base AIError class that all AI-specific errors inherit from
- Include common error properties like error codes, messages, and severity levels
- Implement proper error serialization for API responses
- Ensure errors can be logged consistently
- Add debugging information when in development mode

##### Context Errors

- Implement specific error types for context-related issues
- Handle missing context parameters with clear error messages
- Create errors for invalid context data
- Provide recovery suggestions for context errors
- Include information about which context is missing or invalid

##### Validation Errors

- Create detailed validation error types
- Include field-specific validation errors
- Implement entity resolution errors
- Handle schema validation failures
- Provide clear messages about validation requirements

##### Database Errors

- Implement error handling for database connection issues
- Create specific errors for entity not found scenarios
- Handle permission-related database errors
- Manage transaction failures gracefully
- Provide retry mechanisms for transient database errors

#### Recovery Mechanisms

##### Graceful Degradation

- Implement fallback behaviors when primary functions fail
- Provide partial results when complete results aren't available
- Degrade functionality gracefully rather than failing completely
- Maintain core functionality even when peripheral systems fail

##### Retry Logic

- Implement intelligent retry mechanisms for transient errors
- Use exponential backoff for retries
- Set appropriate timeout limits
- Track retry attempts and provide feedback

##### Error Logging

- Log all errors with appropriate severity levels
- Include contextual information in error logs
- Implement structured logging for easier analysis
- Create alerts for critical errors

##### User Feedback

- Translate technical errors into user-friendly messages
- Provide actionable feedback when errors occur
- Maintain consistent error message formatting
- Include next steps or recovery options when possible

### 5. Configuration

This component provides a flexible, hierarchical configuration system for the AI components.

#### Configuration Options

##### Validation Rules

- Configure validation thresholds and rules
- Set confidence thresholds for entity matching
- Define required parameters for each intent
- Configure validation error messages
- Set up validation behavior (strict vs. lenient)

##### Prompt Generation

- Define prompt templates for different intents (e.g., "search_network", "chat", "send_message")
- Configure fallback behavior for prompt generation

## Implementation Phases

### Phase 1: Database Integration

This phase focuses on connecting the AI system to the CosmosDB database and retrieving context.

1. **Update Context Service**

   - Modify `context_service.py` to retrieve data from CosmosDB using existing CRUD operations
   - Implement methods to fetch data from all relevant containers (people, communities, etc.)
   - Create caching mechanisms for frequently accessed data
   - Add user context awareness to data retrieval

2. **Implement Data Sanitization**

   - Create sanitization functions for each data type
   - Remove sensitive information from retrieved data
   - Implement field-level filtering based on data sensitivity
   - Add logging for sanitization operations
   - Create unit tests for sanitization functions

3. **Format Context for System Prompts**

   - Design structured context format for system prompts
   - Implement context formatting functions
   - Optimize context size to stay within token limits
   - Prioritize most relevant information
   - Create helper functions for context manipulation

4. **Test Context Retrieval**
   - Create comprehensive test suite for context retrieval
   - Test with various user scenarios and permissions
   - Verify sanitization is working correctly
   - Measure performance and optimize as needed
   - Document context retrieval patterns

### Phase 2: Intent Processing and User Edit

This phase focuses on understanding user intent, extracting parameters, and enabling user edits.

1. **Enhance Intent Parser**

   - Improve intent classification for "search_network", "chat", and "send_message" intents
   - Implement robust parameter extraction
   - Create ParsedIntent model with all required fields
   - Add confidence scoring for intent classification
   - Implement unit tests for intent parsing

2. **Add Context Detection**

   - Create context detection logic to identify missing parameters
   - Implement required parameter validation
   - Add context-aware parameter extraction
   - Create prompts for missing context
   - Test context detection with various inputs

3. **Implement Entity Suggestion**

   - Develop intelligent entity matching algorithms
   - Implement a configurable confidence scoring system
   - Create a tiered approach to entity resolution based on confidence levels
   - Design clear, user-friendly feedback for all resolution outcomes
   - Implement graceful degradation for imperfect matches
   - Ensure consistent messaging across all confidence levels
   - Test entity suggestion with diverse inputs including edge cases

4. **Implement User Edit**

   - Create a user interface for editing proposed actions (for send_message intent only)
   - Allow users to modify message content before sending
   - Implement a dropdown selection for recipients (not free text entry)
   - Present the intended action in a clear, user-friendly format
   - Show what action will be executed with which parameters
   - Enable users to confirm the action after making any desired edits
   - User edit is not implemented for search_network or other intents

5. **Enhance Action Validation**

   - Implement validation of structural parameters only
   - No validation is performed on user-edited message content
   - Verify that all required parameters are present
   - Validate that the user has permission to perform the action
   - Create clear error messages for validation failures
   - Test action validation with various scenarios
   - Treat user-edited content as valid by definition

6. **Create Validation Pipeline**
   - Implement progressive validation system
   - Create validators for context, entities, actions, and schemas
   - Add clear error messages for validation failures
   - Implement validation result formatting
   - Test validation pipeline with various scenarios

### Phase 3: Action Handling

This phase focuses on executing actions based on validated intents.

1. **Improve Action Dispatcher**

   - Enhance action routing based on intent
   - Implement handlers for "search_network", "chat", and "send_message" intents
   - Add parameter preparation for action execution
   - Create consistent response formatting
   - Test action dispatcher with various intents

2. **Add Error Handling**

   - Implement comprehensive error handling system
   - Create error hierarchy with specific error types
   - Add error serialization for API responses
   - Implement error logging
   - Test error handling with various failure scenarios

3. **Implement Recovery Mechanisms**

   - Add graceful degradation for partial failures
   - Implement retry logic for transient errors
   - Create fallback behaviors
   - Add user feedback for recovery actions
   - Test recovery mechanisms

4. **Add Logging and Monitoring**
   - Implement structured logging throughout the system
   - Add performance monitoring
   - Create usage metrics collection
   - Implement diagnostic logging
   - Set up alerting for critical issues

### Phase 4: Testing & Documentation

This phase focuses on ensuring quality and usability of the system.

1. **Create Test Cases**

   - Develop comprehensive test suite for all components
   - Create integration tests for end-to-end flows
   - Implement performance tests
   - Add edge case testing
   - Create test data and fixtures

2. **Test with Curl Requests**

   - Create curl examples for all supported intents
   - Test API endpoints with various parameters
   - Document expected responses
   - Create troubleshooting guide
   - Verify error handling with invalid requests

3. **Generate Documentation**

   - Create detailed API documentation
   - Document system architecture
   - Add component interaction diagrams
   - Create configuration guide
   - Document error codes and messages

4. **Create Usage Examples**
   - Develop example applications using the API
   - Create tutorials for common use cases
   - Add code snippets for integration
   - Document best practices
   - Create user guides for different personas

## Example Flows and Test Cases

### Use Case 1: Search Network Flow

**Input**: "Search for people who know about machine learning"

1. System Context:

   - System has access to cleaned data context including people, communities, conversations, etc.
   - People table contains profiles with skills and interests

2. Intent Parsing:

   - Parse the input to derive intent (`search_network`)
   - Extract parameters (`query: "machine learning"`)
   - Determine no additional context is needed from the user

3. Entity Suggestion:

   - System identifies this is a general search query, not requiring specific entity matching
   - System prepares to search across the network for relevant profiles
   - System informs the user: "Searching for people with machine learning expertise"

4. Action Preparation:

   - System prepares to execute the search with the parsed query: "machine learning"
   - No user edit is required for search_network intent
   - System proceeds directly to validation and execution

5. Action Validation:

   - Validate all parameters are present
   - Verify the search query is valid and specific enough
   - Check if user has permission to search the network
   - Validate any filters for proper format and applicability
   - Ensure the query meets minimum length requirements

6. Action Execution:
   - Execute search across people profiles
   - Return success response with search results: "Found 5 people with machine learning expertise"

### Use Case 2: Context Needed Flow

**Input**: "Search for people"

1. System Context:

   - System has access to cleaned data context including people, communities, conversations, etc.
   - People table contains profiles with various attributes

2. Intent Parsing:

   - Parse the input to derive intent (`search_network`)
   - Extract parameters (none specific provided)
   - Determine additional context is needed (`query` is missing or too general)

3. Context Validation:

   - Detect missing required parameter (`query`)
   - Request additional information from user: "What specific skills, interests, or attributes would you like to search for?"

4. User Response:

   - User provides the missing content: "who work in finance"
   - System continues with processing

5. Action Preparation:

   - System prepares to execute the search with the updated query: "finance"
   - No user edit is required for search_network intent
   - System proceeds directly to validation and execution

6. Action Validation:

   - Validate all parameters are present
   - Verify the search query is valid and specific enough
   - Check if user has permission to search the network
   - Ensure the query meets minimum length requirements

7. Action Execution:
   - Execute search across people profiles with the query "finance"
   - Return success response with search results: "Found 8 people who work in finance"

### Use Case 3: Low Confidence Match Flow

**Input**: "Send a message to Dan about the project"

1. System Context:

   - System has access to cleaned data context including people, communities, conversations, etc.
   - People table contains multiple users with similar names: "Daniel Smith", "Dan Johnson", "Dana Lee"

2. Intent Parsing:

   - Parse the input to derive intent (`send_message`)
   - Extract parameters (`content: "about the project"`, `recipient: "Dan"`)
   - Determine no additional context is needed from the user

3. Entity Suggestion:

   - System identifies potential matches for "Dan" using entity matching algorithms
   - System determines this is a medium confidence match scenario
   - System decides to proceed with the best match while including confirmation
   - System prepares appropriate user feedback for this confidence level

4. User Edit with Medium Confidence:

   - System presents the action for user validation: "I'll send a message to Daniel Smith about the project"
   - System highlights the entity resolution decision: "Did you mean Daniel Smith?"
   - Allow user to edit only the message content before sending
   - Recipient is presented in a dropdown menu (not editable as free text)
   - User confirms the action after making any desired edits to the message
   - System captures the final user-approved message
   - The interface includes both the entity match confirmation and editable message content

5. Action Validation:

   - No validation is performed on the user-edited message content
   - Verify only that the user has permission to message the recipient
   - Validate that the recipient ID exists and is valid
   - User-edited message content is treated as valid by definition

6. Action Execution:

   - Create a new message in the `messages` container with Daniel Smith as the recipient
   - Update the conversation in the `conversations` container
   - Return success response with confirmation: "Message sent to Daniel Smith"

### Use Case 4: No Match Found Flow

**Input**: "Send a message to Xylophone about the meeting"

1. System Context:

   - System has access to cleaned data context including people, communities, conversations, etc.
   - People table contains various users but none with a name remotely similar to "Xylophone"

2. Intent Parsing:

   - Parse the input to derive intent (`send_message`)
   - Extract parameters (`content: "about the meeting"`, `recipient: "Xylophone"`)
   - Determine no additional context is needed from the user

3. Entity Suggestion:

   - System attempts to identify potential matches for "Xylophone"
   - System determines this is a low confidence match scenario
   - No viable matches are found that meet minimum confidence requirements

4. User Edit with Error:

   - System presents a clear error message: "I couldn't find anyone named 'Xylophone' in your network. Please check the name and try again."
   - The interface shows the intent and parameters that were successfully parsed
   - System does not proceed to the editing stage due to the missing required entity
   - User is prompted to provide a new request with a valid recipient name

5. User Response:

   - User would need to provide a new request with a valid recipient name
   - For example: "Send a message to John about the meeting"

### Testing with curl

To test the API, use curl commands like:

```bash
# Test search_network intent (complete parameters)
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "Search for people who know about machine learning"}'

# Test search_network intent (missing query)
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "Search for people"}'

# Test chat intent
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "What is the weather like today?"}'

# Test send_message intent
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "Send a message to Ilya saying I found an iOS developer"}'

# Test send_message intent with low confidence entity
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "Send a message to Dan about the project"}'

# Execute an edited action (after user edit)
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "", "execute_now": true, "action_id": "person1_action123", "content": "Hello, this is my edited message", "recipient_id": "person1"}'

# Test another chat example
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "Tell me about the latest technology trends"}'

# Test with invalid intent (should fall back to chat)
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "Create an event called Team Lunch for next Friday at 12pm at Central Park"}'

# Test send_message with no matching entity
curl -X POST "http://localhost:8000/ai/process" \
     -H "Content-Type: application/json" \
     -H "X-User-ID: b80f3bcd-7472-46b7-95c9-8918a723b937" \
     -d '{"text": "Send a message to Xylophone about the meeting"}'
```

## Database Integration

The system should integrate with the following CosmosDB containers using the existing CRUD operations in `peepsapi/crud/api/`:

1. `people` - Contains person data (id, name, profile picture, etc.)

   - Include in system prompt: name, surname, profile picture URL, public profile information, emails, social links, user ID
   - Exclude: private settings, authentication details, information not shown for public

2. `communities` - Contains community data (id, name, members, etc.)

   - Include in system prompt: name, description, public information
   - Exclude: private settings, admin-only information

3. `conversations` - Contains conversation metadata (id, participants, etc.)

   - Include in system prompt: conversation IDs, participant names
   - Exclude: private messages, deleted content

4. `messages` - Contains individual messages (id, conversation_id, content, etc.)

   - Include in system prompt: recent message summaries for context
   - Exclude: full message history, private or sensitive content

5. `posts` - Contains posts (id, content, author, community_id, etc.)

   - Include in system prompt: recent post titles, authors
   - Exclude: private posts, deleted content

6. `comments` - Contains comments (id, content, author, target_id, etc.)

   - Include in system prompt: comment counts, recent comment authors
   - Exclude: private comments, deleted content

7. `events` - Contains events (id, title, description, start_time, etc.)
   - Include in system prompt: upcoming event titles, dates, locations
   - Exclude: private events, attendee lists

## Important Implementation Boundaries

### Scope Limitations

- All changes must be contained within the `peepsapi/ai/` directory, except for:
  - `peepsapi/main.py`, edit it if there are changes to the ai/process endpoint
  - `peepsapi/tests/ai/` for tests
- Existing components must remain untouched:
  - CRUD operations (`peepsapi/crud/*`)
  - API endpoints (`peepsapi/api/*`)
  - Data models (`peepsapi/models/*`)
  - Database core functionality
  - Main FastAPI application setup

### Integration Approach

- Use existing CRUD operations through their public interfaces
- Utilize existing Pydantic models for data validation
- Access database only through established CRUD endpoints
- Any new models or types should be AI-specific and kept in the AI package
