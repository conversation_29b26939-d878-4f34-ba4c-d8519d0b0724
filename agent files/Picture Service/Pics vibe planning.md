(e.g., infra setup → API + DB → backend service → frontend → async jobs → testing & rollout).


# Security Integration (In All Phases)
 - Inherit FastAPI middleware for authentication
 - Enforce ownership validation on create/delete
 - Ensure blob access is not exposed publicly
 - Structured logging for each action at API level, services and db/blob layer
 - Follow existing patterns from other secured endpoints in the codebase

# Phase 1: Infra Setup

## Goals:

1. Provision core image infrastructure:
    1. Azure Blob Storage
    2. CosmosDB pictures collection
    3. Parameter wiring via Bicep templates
    4. Parameters for each env in json param files.
    5. Update to makefile.

## Key Deliverables:

1. Blob containers for originals and thumbnails
2. `pictures` collection in CosmosDB with:
    1. `Partition key`: `parent_id`
    2. Composite index on (`parent_type`, `parent_id`)
3. Bicep file updates for Blob config
4. Environment parameter files updated across environments
5. Makefile support to deploy Blob infra and KeyVault `AZURE_BLOB_CONNECTION_STRING` setup

# Phase 2: Foundational Backend

## Goals:

1. Add core model and service logic
2. Integrate with Azure Blob
3. Establish reusable picture service


## Key Deliverables:

1. `picture.py` model
2. Azure Blob wrapper and uploader/downloader
3. `picture_service.py`:
    1. Create/read/update/delete support
    2. Writes to blob, stores metadata in CosmosDB
    3. Handles image metadata extraction and validation
4. Constants added for allowed formats, size

# Phase 3: Profile Picture Upload

### Goals:

1. Person profile picture upload flow
2. Store metadata + enforce validations

### Key Deliverables:

1. POST `/person/{UUID}/profilePic` endpoint.
2. Accepts multipart upload
3. Validates file type and size (JPEG/PNG/GIF, 10MB)
4. Calls picture_service to:
    1. Store blob
    2. Persist metadata
5. Updates Person with new picture metadata
6. Marks previous picture deleted=true (soft delete)

# Phase 4: Picture Retrieval and Display APIs

###Goals:

1. Implement secure GET and DELETE endpoints
2. Add fallback for default images.

### Key Deliverables:

1. GET `/pictures/{parent_type}/{parent_id}` - metadata
2. GET `/pictures/{parent_type}/{parent_id}/full` - full image
3. DELETE `/pictures/{parent_type}/{parent_id}` - soft delete.
4. Default images served if no match in DB (fallback in service logic)

# Phase 5: Testing & Developer Documentation

### Goals:
1. Ensure correctness of backend via robust tests
2. Document implementation and API usage

### Key Deliverables:
1. Unit tests for:
    1. Blob upload wrapper
    2. Picture metadata validation
    3. Service logic
2. Integration tests with mock Blob and CosmosDB
3. End-to-end test for upload, retrieve, and delete
4. Developer documentation (e.g. docs/images.md) including:
    1. How picture upload works
    2. Metadata model reference
    3. Backend usage patterns
5. ✅ API reference auto-generated via FastAPI schema

# Phase 6: Async Thumbnail Generation (Post V1)

### Goals:

1. Add asynchronous thumbnail generation via Azure Functions
2. Add GET endpoint for thumbnails

### Key Deliverables:

1. Azure Functions Infra
    1. App service plan
    2. Blob-triggered function or queue-based trigger
    3. Thumbnail generation logic with Pillow or equivalent
    4. Uploads thumbnails to separate blob container
2. Update CosmosDB thumbnail_paths field
3. Extend config:
    - THUMBNAIL_CONFIG by entity type
4. Extend GET `/pictures/{parent_type}/{parent_id}/thumbnail?size=...`
5. Add tests for thumbnail generation worker

# Phase 7: Versioning, Multi-picture Support & Advanced Features (Post V1)
### Goals:

1. Implement picture versioning with audit trail and rollback support.
2. Support multiple pictures per entity (e.g., gallery).
3. Implement hard-delete lifecycle management.
4. Integrate malware scanning and content moderation pipelines.
5. Plan CDN integration and multi-region storage.

### Key Deliverables:

1. DB schema and API changes for versioning and multi-picture.
2. Background cleanup job for hard delete.
3. Integration with malware/content scanning services.
4. CDN configuration and testing.
5. Update existing, add tests and documentation for new functionalities.

### Risks:

1. Complex data migration for existing pictures.
2. Scalability under high concurrency.
