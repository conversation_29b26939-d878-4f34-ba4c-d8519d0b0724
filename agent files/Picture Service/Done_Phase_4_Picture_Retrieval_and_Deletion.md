
# Phase 4: Picture Retrieval and Display APIs — Codex-Friendly Task Plan

This implementation plan corresponds exactly to Phase 4 in `Pics vibe planning.md` and reflects the behavior and expectations defined in `Pics PRD.md`. These tasks enable full retrieval of picture metadata and image content, deletion handling, and fallback logic for default images.

---

## 🎯 Goals

1. Implement GET endpoints for retrieving picture metadata and content.
2. Implement picture deletion via soft delete.
3. Support default image fallback when no picture exists for an entity.

---

## 📦 Key Deliverables

1. `GET /pictures/{parent_type}/{parent_id}` — fetch picture metadata.
2. `GET /pictures/{parent_type}/{parent_id}/full` — fetch full image.
3. `DELETE /pictures/{parent_type}/{parent_id}` — soft delete.
4. Default image logic integrated into retrieval endpoints when no user-uploaded image exists.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: GET Metadata Endpoint

**File:** `peepsapi/crud/api/pictures.py`

**Prompt:**
"Add `GET /pictures/{parent_type}/{parent_id}/metadata` endpoint:
- Uses `picture_service.get_picture_metadata(parent_type, parent_id)`
- Returns metadata fields from the latest non-deleted picture
- Use FastAPI path parameters and response models."

---

### 🔹 Task 2: GET Full Picture Endpoint

**Prompt:**
"Add `GET /pictures/{parent_type}/{parent_id}` endpoint:
- Use `picture_service.get_picture()` to return image bytes
- Set correct `Content-Type` header based on stored MIME type
- If no picture is found, `picture_service.get_picture()` will serve default via:
  - `get_default_picture(parent_type) → bytes`
- All requests must be authenticated (middleware handles this)
- Use `StreamingResponse` to return binary blob"

---

### 🔹 Task 3: Soft Delete Endpoint

**Prompt:**
"Add `DELETE /pictures/{parent_type}/{parent_id}` endpoint:
- Calls `picture_service.delete_picture(parent_type, parent_id)`
- Marks the latest picture record as `deleted = true`
- Returns 204 No Content on success
- Return 404 if no picture is found"

---

### 🔹 Task 4: Default Image Fallback Logic

**File:** `picture_service.py`

**Prompt:**
"Implement `get_picture()` logic:
- Look up the latest non-deleted picture for given parent
- If not found, return default image bytes from blob:
  - blob path: `defaults/{parent_type}.jpg`
- Default image blob is stored in the same Azure container
- Example fallback: `defaults/person.jpg`"

---

## 🧪 Acceptance Criteria

- Metadata and full image endpoints return correct info and binary blobs
- Missing pictures return default image for type
- Deletion updates the `deleted` flag in DB, does not remove blob
- All routes enforce existing auth middleware and permissions
- All endpoints visible and testable via FastAPI Swagger UI
