
# Phase 3: Profile Picture Upload — Codex-Friendly Task Plan

This implementation plan corresponds exactly to Phase 3 in `Pics vibe planning.md` and is based on behavior defined in `Pics PRD.md`. It provides Codex-compatible task slices for developing the profile picture upload API.

---

## 🎯 Goals

1. Implement upload API endpoint for Person profile picture.
2. Integrate with the previously created picture service.
3. Store new picture metadata and blob.
4. Soft delete any previously stored picture.

---

## 📦 Key Deliverables

1. Person route is able to accept picture uploads and uses picture service to store picture in Azure Blob.
2. `POST /person/{UUID}/profilePic` endpoint is functional.
3. DB soft delete logic is triggered when replacing an existing picture.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Add Profile Picture Upload Endpoint

**File:** `peepsapi/crud/routes/people.py`

**Prompt:**
"Add a `POST /person/{person_id}/profilePic` endpoint that:
- Accepts a `multipart/form-data` payload with a single field `image_file`
- Validates the person exists using `get_person(person_id)`
- Reads the uploaded file as bytes
- extract content-type and original-filename
- Passes all info to `picture_service.upload_picture(...)`

---

### 🔹 Task 2: Wire Endpoint to Picture Service

**Prompt:**
"Inside the upload endpoint for Person:
- Call `upload_picture(image_file, 'person', person_id, filename, content_type)`
- On success updates Person object in a new attribute profile_pic to `/pictures/{parent_type}/{parent_id}`
- update Person object in db
- return 201 created on success or return any errors as appropriate
  | Status Code | Error Code        | Description                                      |
  | ----------- | ----------------- | ------------------------------------------------ |
  | 400         | `InvalidFileType` | Uploaded file is not JPEG, PNG, or GIF           |
  | 400         | `FileTooLarge`    | Uploaded file exceeds 10MB size limit            |
  | 404         | `PersonNotFound`  | No person found with the given `personId`        |
  | 401         | `Unauthorized`    | User is not authorized to upload for this person |
  | 500         | `InternalError`   | Unexpected server error                          |
- Log the upload using util.logging

---

## 🧪 Acceptance Criteria

- `POST /person/{UUID}/profilePic` accepts valid image files
- Metadata and blob are stored via service
- If a picture already exists, it's marked `deleted=true`
- FastAPI auto-doc shows route and input/output clearly
- Returned response includes timestamp and blob paths

---
