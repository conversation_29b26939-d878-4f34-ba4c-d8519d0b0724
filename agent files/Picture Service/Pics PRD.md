# Comprehensive Feature Specification: Picture Upload & Management

*For Person Profiles, Posts, Comments in `peepsapi` Backend*

---

## Table of Contents

1. [Feature Context and Clarifications](#feature-context-and-clarifications)
2. [System Architecture Overview](#system-architecture-overview)
3. [Technology Stack and Justification](#technology-stack-and-justification)
4. [File System Structure](#file-system-structure)
5. [Database Schema Design](#database-schema-design)
6. [API Design](#api-design)
7. [CRUD Operations](#crud-operations)
8. [User Experience Flow](#user-experience-flow)
9. [Security Considerations](#security-considerations)
10. [Testing Strategy](#testing-strategy)
11. [Data Management & Scalability](#data-management--scalability)
12. [Error Handling and Logging](#error-handling-and-logging)
13. [Configuration](#configuration)
14. [Future-Proofing and Expansion Notes](#future-proofing-and-expansion-notes)
15. [Summary and Next Steps](#summary-and-next-steps)

---

## 1. Feature Context and Clarifications

- We have many entities, we will only implement image management for **Person** and **PersonPreview** in V1. But later we need support for **Post**, **Comment**, **Community**, **Event** and others to come.
- Each can have **one picture** attached initially (V1), but the design must allow easy evolution to multiple pictures later.
- Picture uploads must be **max 10MB**, allowed types: **JPEG, PNG, GIF** in V1, in future we will add support for other media types including audio & video as well as higher upload size limit.
- Pictures are **mandatory for profiles**, **optional for rest of types**.
- Metadata extracted/stored includes: original filename, MIME type, size, width/height, upload timestamp, person UUID.
- **Peson UUID** associated with the picture comes from API current person method.
- Uploads are single-file at a time via REST API; frontends handle cropping/resizing client-side for profiles.
- Storage on **Azure Blob Storage**, currently no CDN but planned for future.
- Pictures are accessed **via backend proxy with auth**, no public URLs.
- Soft deletes implemented V1, hard deletes handled later by background cleanup.
- Repo structure shared for **`peepsapi`**, backend Python with REST APIs.
- (Post V1) Thumbnails generated asynchronously with configurable sizes; this is configurable based on associated.
- (Post V1) malware scanning, content moderation, multi-region support, multi-picture, and versioning.
- Design should accommodate for post V1 and out of scope of V1.

---

## 2. System Architecture Overview

# Picture Upload and Management Feature Specification for `peepsapi

``` plaintext

+---------------------+        +-------------------------+         +------------------+
|  Client Applications | <----> |  peepsapi Backend API   | <-----> | Azure Blob Storage|
| (Mobile/Web Clients) |        | (RESTful API Server)    |         | (Image Storage)   |
+---------------------+        +-------------------------+         +------------------+
                                      |
                                      | (Async thumbnail generation triggers)
                                      v
                              +------------------+
                              | Background Worker |
                              |  (Azure Functions)|
                              +------------------+
```

- Client apps handle image cropping/resizing before upload (esp. profiles).
- Backend accepts uploads, validates, stores metadata and original images, **Post V1:** triggers async thumbnail generation.
- **post V1** Background workers generate thumbnails and update DB asynchronously.
- All image requests authorized and proxied through backend for security.

---

## 3. Technology Stack and Justification

| Layer             | Technology            | Reason/Benefit                              |
|-------------------|-----------------------|---------------------------------------------|
| Backend Framework | Python (existing)     | Existing codebase, Azure SDK support        |
| Storage           | Azure Blob Storage    | Scalable, durable, cost-effective           |
| Database          | CosmosDb (existing)   | Existing, supports JSON fields for metadata |
| Async Jobs        | Azure Functions       | Serverless, event-driven, scalable          |

---

## 4. File System Structure

### Backend (`peepsapi`)

```plaintext
infra/
 ├── azure-blob.bicep                 # Azure blob infra deployment config
 ├── main-parameters-dev.json         # environment parameters for deployments (test/dev/stage/api)
peepsapi/
 ├── crud/
 │    ├── routes/
 │    │    ├── people.py              # Exposes wrapper Person/{id}/Pic (Delete, Get, Post, Put)
 │    │    ├── pictures.py            # Pictures metadata and object (Get)
 │    │    ├── posts.py
 │    │    ├── comments.py
 │    │    └── ...other entity APIs
 │    ├── services/
 │    │    ├── picture_service.py     # Picture interaction with AzureBlob libraries, updates to Person profile ...
 │    │    ├── people_services.py
 │    │    └── ...other services
 │    ├── models/
 │    │    ├── picture.py             # Picture model with metadata & relationships
 │    │    ├── person.py
 │    │    ├── post.py
 │    │    ├── comment.py
 │    │    └── ...other models
 │    └── utils/
 │         ├── constants.py           # Thumbnail sizes for Person in V1 and other models later
 │         └── ...other crud utils
 ├── services/
 │    ├── cosmosdb.py                 # Comsosdb centeralized logic (this needs to renamed and moved later from crud/core to this location)
 │    └── azure_blob.py               # Azure Blob Storage config
 ├── main.py                          # Application entrypoint, router registrations
 ├── Makefile                         # Makefile manages deploy-infra where we will deploy our Azure blob

 ....

```

---

## 5. Database Schema Design

### Collection: `pictures`

| Column            | Type          | Description                                           | Constraints               |
|-------------------|---------------|-------------------------------------------------------|---------------------------|
| id                | UUID (PK)     | Unique identifier                                     | Primary Key               |
| parent_type       | ENUM          | 'person', 'post', 'comment', future: 'community' etc  |                           |
| parent_id         | UUID          | corresponding parent entity                           | Partition Key             |
| blob_storage_path | VARCHAR       | Blob path of original image                           | Not null                  |
| original_filename | VARCHAR       | Original uploaded filename                            |                           |
| content_type      | VARCHAR       | MIME type (image/jpeg, image/png, image/gif)          |                           |
| size_bytes        | INTEGER       | Size in bytes                                         |                           |
| width             | INTEGER       | Image width pixels                                    |                           |
| height            | INTEGER       | Image height pixels                                   |                           |
| thumbnail_paths   | LIST          | Map of thumbnail size keys to blob paths              | Example: {"small": "path"}|
| upload_timestamp  | TIMESTAMP     | When image was uploaded                               |                           |
| deleted           | BOOLEAN       | Soft delete flag                                      | Default FALSE             |
| version           | INTEGER       | Versioning for future                                 | Default 1                 |


---

## 6. Infra setup

- CosmosDB partition key: `parent_id`
- Composite indexes to be created on `(parent_type, parent_id)` to optimize querying pictures by entity.
- This indexing strategy supports future multi-picture retrieval and efficient lookups.
- Update cosmosdb parameters for pictures collection in each environment parameter file.
- Add resource definition in azure-blob.bicep, this will define deployment parameters.
- Add parameters related to deployment to each environment (test/dev/stage/api)
- Add relevant changes in Makefile to accomodate for deployment of Azure Blob, this includes following the existing pattern of extracting connection string from Azure Blob and set it in secret AZURE-BLOB-CONNECTION-STRING in KeyVault.

## 7. API Design

#### 7.1 {Person} Picture Upload

**POST** `/person/{UUID}/profilePic`

### Request
- *Path parameter:*
  - `personId` (UUID) — the person’s unique identifier

- *Headers:*
  - `Content-Type: multipart/form-data`

- *Body:*
  - Multipart form-data with one field:
  - `image_file` (binary) — JPEG, PNG, or GIF file, max 10MB

### Response
- **Status:** `201 Created`

- **Internally**
person route sends following payload to picture service to fulfill the request.

  ```json
  {
    "parent_type": "person",
    "parent_id": "parent_uuid",
    "image_file": bytes
  }
  ```

  On success it sets the following values in its data.
  This will be update the person object with

  ```json
  Person: {
    "id": UUID
    ....
    "picture": "{protocol}://{host}/pictures/person/{id}/full",
    "picture_thumbnail": "{protocol}://{host}/pictures/person/{id}/thumbnail?size=small",  # not in V1
    ....
  }
  ```
### Possible Errors

  | Status Code | Error Code        | Description                                      |
  | ----------- | ----------------- | ------------------------------------------------ |
  | 400         | `InvalidFileType` | Uploaded file is not JPEG, PNG, or GIF           |
  | 400         | `FileTooLarge`    | Uploaded file exceeds 10MB size limit            |
  | 404         | `PersonNotFound`  | No person found with the given `personId`        |
  | 401         | `Unauthorized`    | User is not authorized to upload for this person |
  | 500         | `InternalError`   | Unexpected server error                          |

## 7.2 Get Picture Metadata

**GET** `/pictures/{parent_type}/{parent_id}`

### Request
- *Path parameter:*
  - `parent_type`: `"person"` | `"post"` | ...
  - `parent_Id`: UUID of the parent entity

### Response
- **Status:** `200 OK`
- **Body:** JSON object:

  ```json
  {
    "id": "uuid-picture-id",
    "parent_type": "person",
    "parent_id": "uuid-parent-id",
    "original_filename": "profile.jpg",
    "content_type": "image/jpeg",
    "size_bytes": 153600,
    "width": 800,
    "height": 800,
    "thumbnails": [
      "small",
      "medium"
    ],
    "upload_timestamp": "2025-05-25T15:00:00Z",
    "deleted": false,
    "version": 1
  }
  ```

### Possible Errors
| Status Code | Error Code          | Description                                |
| ----------- | ------------------- | ------------------------------------------ |
| 404         | `PictureNotFound`   | No picture found for given parent\_type/id |
| 400         | `InvalidParentType` | parent\_type not supported                 |
| 401         | `Unauthorized`      | Access denied                              |
| 500         | `InternalError`     | Unexpected server error                    |

---

## 7.3 Download Picture

- All Pic url in Preview objects are set to `/pictures/{parent_type}/{parent_id}` as default.
- Default images for entities (e.g., person) are stored on Azure Blob Storage.
- Picture service responds with these default images when no picture record exists for a given parent entity in the database.

### 7.3.1 Download full Picture
**GET** `/pictures/{parent_type}/{parent_id}/full`

#### Request
- *Path parameter:*
  - `parent_type`: `"person"` | `"post"` | ...
  - `parent_Id`: UUID of the parent entity

#### Response
- **Status:** `200 OK`
- **Body:** Binary image data.
- **Content-Type:** Matches stored image MIME type.

#### Possible Errors
| Status Code | Error Code          | Description                                |
| ----------- | ------------------- | ------------------------------------------ |
| 400         | `InvalidParentType` | parent\_type not supported                 |
| 401         | `Unauthorized`      | Access denied                              |
| 500         | `InternalError`     | Unexpected server error                    |

### 7.3.2 Download Thumbnaiul Picture

**GET** `/pictures/{parent_type}/{parent_id}/thumbnail?size=small|medium|large` # not in v1

#### Request
- *Path parameter:*
  - `parent_type`: `"person"` | `"post"` | ...
  - `parent_Id`: UUID of the parent entity
- *Query parameters:*
  - `size` (str): `"small"` | `"medium"` | `"large"`

#### Response
- **Status:** `200 OK`
- **Body:** Binary image data.
- **Content-Type:** Matches stored image MIME type.

#### Possible Errors
| Status Code | Error Code          | Description                                |
| ----------- | ------------------- | ------------------------------------------ |
| 400         | `InvalidParentType` | parent\_type not supported                 |
| 400         | `InvalidSize` | Thumbnail size param invalid                 |
| 401         | `Unauthorized`      | Access denied                              |
| 500         | `InternalError`     | Unexpected server error

---

## 7.4 Delete Picture (Soft Delete)

- Marks `deleted = true`; **Post V1** background job handles hard delete later.

**DELETE** `/pictures/{parent_type}/{parent_id}`

#### Request
- *Path parameter:*
  - `parent_type`: `"person"` | `"post"` | ...
  - `parent_Id`: UUID of the parent entity

#### Response
- **Status:** `204 No Content`
- **Body:** Empty.

#### Possible Errors
| Status Code | Error Code          | Description                                |
| ----------- | ------------------- | ------------------------------------------ |
| 401         | `Unauthorized`      | Access denied                              |
| 404         | `PictureNotFound` | No picture found for given parent_type/id                 |
| 500         | `InternalError`     | Unexpected server error

---

## 8. CRUD Operations

| Operation | Description                                   | Notes                                  |
|-----------|-----------------------------------------------|----------------------------------------|
| Create    | Validate and upload image; save metadata      | Async thumbnail generation             |
| Read      | Fetch metadata or image                       | Support filtering by parent            |
| Update    | use create, V++, deleted=true, replace        | Plan versioning later                  |
| Delete    | Soft delete by flagging                       | (Post V1) Hard delete async cleanup    |

---

## 9. User Experience Flow

- Profile picture upload enforced during registration with client cropping.
- Post and Comment picture uploads optional, one at a time from camera or library.
- Loading/progress indicators during upload and thumbnail processing.
- Soft delete on removal with user confirmation.

---

## 10. Security Considerations

- All endpoints inherit authentication from middleware.
- Photo creation is only triggered via Person and other endpoints to assure association.
- Authorization checks to ensure user owns/modifies parent entity.
- Strict file size and format validation.
- (Post V1): malware scanning and content moderation integration.

---

## 11. Testing Strategy (Post V1)

- Unit tests for model validations and API request validation.
- Integration tests with mocked Azure Blob Storage for upload/download.
- End-to-end tests for upload, retrieval, and deletion flows in user scenarios.
- Performance tests for upload concurrency.

---

## 12. Data Management & Scalability

- (Post V1) Use Azure Blob Storage lifecycle policies for cleaning soft-deleted images.
- Backend caching of metadata and URLs for performance.
- (Post V1) Pagination support for future multi-picture listing.
- Design for horizontal scaling and multi-region deployment, implementation (Post V1).

---

## 13. Error Handling and Logging

- When no picture is found for a parent entity, the service returns a default image rather than a 404 Not Found status.
- Clients should handle image URLs uniformly without special cases for missing pictures.
- Use standard HTTP status codes:
  - `400` Bad Request (validation errors)
  - `401` Unauthorized
  - `404` Not Found  - N/A
  - `413` Payload Too Large
  - `415` Unsupported Media Type

- Structured logging of uploads, downloads, deletes including user and request metadata.
- (Post V1) Alerting on repeated failures or storage errors.

---

## 14. Configuration

Add the following to `constants.py`:

```python
PICTURE_CONTAINER_NAME = "pictures-originals"
THUMBNAIL_CONTAINER_NAME = "pictures-thumbnails"
THUMBNAIL_CONFIG = {
    "person": {
        "sizes": {"small": (100, 100)},
    },
    "post": {
        ....
    },
    "comment": {
        ...
    },
}
DEFAULT_IMAGES = {
  "person": {
        "sizes": {
          "full": "DEFAULT_PERSON_FULL",
          "small": "DEFAULT_PERSON_SMALL"
        },
    },
    "post": {
        ....
    },
    "comment": {
        ...
    },
}
MAX_UPLOAD_SIZE_MB = 10
ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif"]
```

Add following to .env
AZURE_BLOB_CONNECTION_STRING=

This will be loaded at runtime along with other secrets.

## 15. Future-Proofing and Expansion Notes (Post V1)

- Transition from 1:1 to 1:N pictures per entity by removing `picture_id` from parent tables and relying solely on `pictures.parent_type` + `parent_id`.
- Picture updates create new picture records with incremented version numbers.
- Previous versions are soft deleted (`deleted = true`) but retained for audit and rollback.
- This versioning system enables rollback support and historical tracking of images.
- Integrate automated malware and inappropriate content scanning pipelines.
- Add CDN for image delivery acceleration.
- Implement hard-delete lifecycle for compliance.
- Multi-region and horizontal scaling infrastructure improvements.
