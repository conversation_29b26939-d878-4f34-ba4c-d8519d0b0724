
# Phase 2: Foundational Backend — Codex-Friendly Task Plan

This document contains the exact task breakdown for Phase 2 as defined in `Pics vibe planning.md`, with Codex-ready feature slices that implement the detailed behavior defined in `Pics PRD.md`.

---

## Goals (from planning)

1. Implement basic picture model.
2. Implement Azure Blob wrapper and storage logic.
3. Implement Picture services.

---

## Key Deliverables (from planning)

1. Basic picture model is added.
2. Azure blob is correctly instantiated and is able to read and write pictures.
3. Picture service:
    - Correctly configured to interact with Azure blob and Cosmos DB.
    - Able to create, update pictures.
    - Provides interface to get full, thumbnail, and metadata.

---

## Codex-Friendly Tasks

### 🔹 Task 1: Define the Picture Model

**File:** `peepsapi/models/picture.py`

**Prompt:**
Create a Pydantic model for a picture record stored in CosmosDB. This model should follow the schema defined in the PRD:

```python
class Picture(BaseModelWithExtra):
    id: UUID
    parent_type: Literal["person", "post", "comment"]  # more in future
    parent_id: UUID
    blob_storage_path: str
    size_bytes: int
    width: int
    height: int
    thumbnail_paths: Dict[str, str] = {}
    upload_timestamp: UTCDatetime
    deleted: bool = False
    version: int = 1
    original_filename: Optional[str]
    content_type: Optional[str]
    format: Optional[str]
    mode: Optional[str]
    is_animated: bool = False
    exif: Dict[str, Any] = field(default_factory=dict)
    gps: Optional[Dict[str, Any]]
    histogram: Optional[list]
    colors: Optional[list]
```

- Set CosmosDB partition key to `parent_id`.
- Ensure model can serialize to/from Cosmos-compatible structure.

---

### 🔹 Task 2: Implement Azure Blob Wrapper

**File:** `peepsapi/services/azure_blob.py`

**Prompt:**
Write a wrapper around Azure Blob SDK that provides:

```python
def upload_blob(container_name: str, blob_name: str, data: bytes, content_type: str) -> str
def download_blob(container_name: str, blob_name: str) -> bytes
def delete_blob(container_name: str, blob_name: str) -> None
```

- Load `AZURE_BLOB_CONNECTION_STRING` from env or KeyVault.
- Use Azure SDK's `BlobServiceClient`.
- Upload should return the full blob path or URL.

---

### 🔹 Task 3: Implement Picture Service

**File:** `peepsapi/services/picture_service.py`

**Prompt:**
Implement a service that supports:

```python
def upload_picture(image_file: bytes, parent: Any, parent_id: UUID, file: bytes, content_type: str) -> Picture
def get_picture(parent_type: str, parent_id: UUID) -> bytes
def get_picture_metadata(parent_type: str, parent_id: UUID) -> Picture
def delete_picture(parent_type: str, parent_id: UUID)
```

Behavior (from PRD):

- use parent_type = parent.__class__.__name__.lower() to get parent_type
- Validate `content_type` and file size (`<= 10MB`).
- Extract image dimensions using Pillow to store in cosmosdb.
- Extract format, content-type, image-file, mode, is_animated, exif, gps, histogram, and colors from image_file to store in cosmosdb record.
- Construct blob path: `pictures-originals/{uuid}/original` where UUID is Picture `id`
- Upload image via blob wrapper.
- Create new `Picture` record in CosmosDB with:
  - version = 1
  - deleted = False
- If a record with deleted = True already exists for `(parent_type, parent_id)`:
  - Mark the old record `deleted = True` and create a new record with old records' version + 1
- For `get_picture`: stream the full blob if present, else fallback to default from config.
- For `get_picture_metadata`: return the latest undeleted record by parent.
- For `delete_picture`: soft-delete the latest picture for the parent.

---

### 🔹 Task 4: Add Constants

**File:** `peepsapi/utils/constants.py`

**Prompt:**
Add the following constants:

```python
ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif"]
MAX_UPLOAD_SIZE_MB = 10
PICTURE_CONTAINER_NAME = "pictures-originals"
DEFAULT_VERSION = 1
```

Use this enum in the `Picture` model and service method inputs.

---

### 🔹 Task 5: Image Metadata Helper

**File:** `peepsapi/services/picture_service.py` (internal helper)

**Prompt:**
Write a helper function that uses Pillow to extract image width and height:

```python
def extract_image_metadata(image_bytes: bytes) -> Picture
```

Use it during upload to store all image metadata records in the DB record.

---

## Completion Criteria

- CosmosDB stores correct picture records for uploaded files.
- Upload stores blob + creates DB record + marks prior record deleted.
- Metadata includes validated file type, size, dimensions, timestamp.
- `get_picture()` returns blob bytes or default.
- `get_picture_metadata()` returns expected JSON metadata.
- `delete_picture()` marks record `deleted=true`.
