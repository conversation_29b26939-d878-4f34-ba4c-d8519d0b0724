
# 🚧 Phase 1: Infra Setup — Implementation Plan

This document outlines the Codex-friendly task breakdown for Phase 1 of the Picture Upload & Management feature: provisioning core infrastructure in Azure for Blob Storage and CosmosDB, including Bicep files, environment configs, and deployment automation.

---

## 🛠 Goals

1. Provision Azure Blob Storage (originals + thumbnails).
2. Create CosmosDB `pictures` collection with proper indexing.
3. Configure environment parameter files for all environments.
4. Update Makefile to support new infra deployment.
5. Ensure secrets (blob connection string) integrate with runtime.

---

## ✅ Codex-Ready Task List

### 🔹 1. Azure Blob Storage via Bicep

**Prompt:**
"Create a Bicep module that provisions an Azure Blob Storage account with two private containers: `pictures-originals` and `pictures-thumbnails`. Apply standard storage account rules and return outputs for connection string and container names."

---

### 🔹 2. Bicep Integration with Parameter Files

**Prompt:**
"Update `main-parameters-dev.json`, `main-parameters-stage.json`, and `main-parameters-api.json` to include parameters for:
- blob storage account name
- original container name
- thumbnail container name
- any keys required for connection"

---

### 🔹 3. CosmosDB Pictures Collection

**Prompt:**
"Update CosmosDB Bicep deployment to include a new `pictures` collection with:
- partition key: `/parent_id`
- composite index on `(parent_type, parent_id)`
- TTL and throughput consistent with other collections."

---

### 🔹 4. Update Makefile

**Prompt:**
"Extend Makefile to:
- Add a new target `deploy-pictures-infra`
- Call the relevant Bicep deployment script for blob + CosmosDB
- Extract the Azure Blob connection string and store it in KeyVault as `AZURE_BLOB_CONNECTION_STRING`"

---

### 🔹 5. Secret Integration

**Prompt:**
"Ensure the backend runtime reads `AZURE_BLOB_CONNECTION_STRING` from environment variables loaded via `.env` or secret store (KeyVault → .env). Modify the loader logic if needed."

---

### 🧪 Acceptance Criteria

- `make deploy-infra` successfully provisions blob containers and CosmosDB collection
- All environments (dev/stage/api) have correct parameters set
- Connection string is validated and visible in KeyVault
- Makefile deploys blob storage without errors
- Team can verify via `az storage container list` and portal
