
# Phase 5: Testing & Developer Documentation — Codex-Friendly Task Plan

This implementation plan corresponds exactly to Phase 5 in `Pics vibe planning.md` and aligns with testing strategy and documentation requirements specified in `Pics PRD.md`. These tasks cover automated testing, developer-facing documentation, and API schema validation.

---

## 🎯 Goals

1. Write automated tests to ensure upload, retrieval, and deletion logic are reliable.
2. Document the picture service and image upload lifecycle.
3. Ensure API contracts are verifiable via FastAPI’s OpenAPI spec.
4. Align with existing logging and error handling patterns.

---

## 📦 Key Deliverables

1. Unit tests for blob interaction, picture service, and image validation logic.
2. Integration and E2E tests for full upload → fetch → delete lifecycle.
3. Developer documentation for the picture system and configuration.
4. FastAPI-generated OpenAPI schema confirmed accurate and clean.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Unit Tests for Picture Service

**Directory:** `peepsapi/tests/unit/`

**Prompt:**
"Write unit tests for:
- `upload_picture()` — mocks blob upload and DB insert, checks metadata.
- `get_picture_metadata()` — returns correct record or raises `PictureNotFound`.
- `delete_picture()` — sets `deleted = true` on the latest version.
Use pytest and patch blob client + Cosmos calls."

---

### 🔹 Task 2: Integration Tests for Full Workflow

**Directory:** `peepsapi/tests/integration/`

**Prompt:**
"Write integration tests that:
- Create a test person, upload image, fetch metadata and full image.
- Delete image and verify fallback behavior.
- Simulate failure conditions (e.g. bad MIME type, oversized file).
- Mock Azure Blob and use test CosmosDB collection."

---

### 🔹 Task 3: End-to-End Test

**Prompt:**
"Create an E2E test that:
- Calls the `/person/{UUID}/profilePic` endpoint with valid JPEG.
- Confirms metadata and image retrieval works.
- Confirms delete sets the record as deleted.
- Confirms fallback to default image afterward.
Run test via test client using FastAPI’s `TestClient()`."

---

### 🔹 Task 4: Developer Documentation

**File:** `docs/crud/Pictures.md`

**Prompt:**
"Write developer-facing documentation that includes:
- Overview of picture model and fields.
- Upload → Blob → Metadata lifecycle.
- How thumbnail support (Phase 6) will integrate.
- How to access default images and test the fallback.
- Where to configure thumbnail sizes, limits, allowed types."

---

### 🔹 Task 5: API Schema Review

**Prompt:**
"Ensure FastAPI auto-generates OpenAPI schema:
- Confirm all routes appear in `/docs` UI.
- Confirm request and response models match real payloads.
- Add descriptive `summary` and `description` fields to all picture routes.
- Validate response models like `PictureUploadResponse` are annotated."

---

## 🧪 Acceptance Criteria

- All unit/integration tests pass on CI and mock services.
- FastAPI Swagger `/docs` UI reflects all current picture routes.
- Picture service logic has 90%+ test coverage.
- `docs/crud/pictures.md` is committed and useful to developers onboarding or debugging.

---

Once verified, proceed to Phase 6: Async Thumbnail Generation.
