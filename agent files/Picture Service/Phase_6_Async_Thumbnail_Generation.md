
# Phase 6: Async Thumbnail Generation — Codex-Friendly Task Plan

This implementation plan corresponds exactly to Phase 6 in `Pics vibe planning.md` and integrates thumbnail generation as described in `Pics PRD.md`. It introduces an asynchronous Azure Function that generates and stores image thumbnails after uploads.

---

## 🎯 Goals

1. Add thumbnail generation via Azure Functions.
2. Process and store thumbnails asynchronously after upload.
3. Support per-entity configuration of thumbnail sizes and crop modes.
4. Extend picture retrieval to expose thumbnail endpoints.

---

## 📦 Key Deliverables

1. Azure Functions infrastructure deployed and configured.
2. Blob-triggered thumbnail function deployed and active.
3. Thumbnails saved to `pictures-thumbnails` container.
4. CosmosDB records updated with `thumbnail_paths`.
5. New `GET /pictures/{parent_type}/{parent_id}/thumbnail?size=` endpoint added.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Azure Functions Infrastructure

**File:** `infra/azure-function.bicep`

**Prompt:**
"Add Bicep module to provision:
- Azure Function App (consumption plan)
- Azure Storage Account (if not reusing blob store)
- Identity and access for blob access
Deploy function app via Makefile script."

---

### 🔹 Task 2: Implement Blob-Triggered Thumbnail Generator

**Directory:** `thumbnail_function/`

**Prompt:**
"Create an Azure Function in Python that:
- Is triggered on blob upload in `pictures-originals` container
- Downloads the image
- Reads config from `THUMBNAIL_CONFIG` based on `parent_type`
- Generates one or more thumbnails using Pillow
- Uploads them to `pictures-thumbnails/{uuid}/{size}.jpg`
- Updates CosmosDB record’s `thumbnail_paths` field"

---

### 🔹 Task 3: Add `THUMBNAIL_CONFIG` Per Entity Type

**File:** `utils/constants.py`

**Prompt:**
"Add:
```python
THUMBNAIL_CONFIG = {
  'person': {
    'sizes': {'small': (100, 100), 'medium': (300, 300)},
    'crop': True
  },
  'post': {
    'sizes': {'small': (300, 200)},
    'crop': False
  }
}
```
Used by thumbnail generator and validated at runtime."

---

### 🔹 Task 4: Update CosmosDB Record with Thumbnails

**Prompt:**
"Within Azure Function:
- After uploading thumbnails to blob
- Update the original picture document in CosmosDB
- Add paths under `thumbnail_paths = {size: blob_path}`"

---

### 🔹 Task 5: Add Thumbnail Download Endpoint

**File:** `crud/api/pictures.py`

**Prompt:**
"Add `GET /pictures/{parent_type}/{parent_id}/thumbnail?size=small|medium`:
- Look up `thumbnail_paths[size]` from DB
- If found, download from blob and return as `StreamingResponse`
- If not found, return 404
- Ensure auth and fallback default thumbnail logic if desired"

---

## 🧪 Acceptance Criteria

- Uploading a picture triggers Azure Function automatically.
- Thumbnails are created and stored in `pictures-thumbnails/{uuid}/`.
- CosmosDB `thumbnail_paths` is populated.
- Thumbnail download endpoint serves correct content.
- Fallback behavior (404 or default) is testable.

---

Once this phase is deployed and functional, proceed to Phase 7 for versioning and multi-picture features.
