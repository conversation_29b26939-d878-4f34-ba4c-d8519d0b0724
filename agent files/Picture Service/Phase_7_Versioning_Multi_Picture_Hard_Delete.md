
# Phase 7: Versioning, Multi-picture, Hard Delete (Post V1) — Codex-Friendly Task Plan

This implementation plan corresponds exactly to Phase 7 in `Pics vibe planning.md` and introduces advanced capabilities described in `Pics PRD.md`, including picture versioning, multi-picture support, hard deletes, and prep for CDN and moderation.

---

## 🎯 Goals

1. Enable multiple picture versions per entity.
2. Support multiple pictures (1:N) per entity.
3. Implement background job for hard deletion.
4. Lay groundwork for future CDN and moderation integration.

---

## 📦 Key Deliverables

1. CosmosDB schema updated to support multiple versions per parent.
2. Optional `version` param added to relevant API endpoints.
3. New API to list all pictures for a parent entity.
4. Hard delete worker to clean up deleted images from blob & DB.
5. Placeholder hooks for malware scanning and CDN proxying.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Versioning Behavior in Picture Service

**File:** `services/picture_service.py`

**Prompt:**
"Update `upload_picture()` to:
- Query existing pictures for (parent_type, parent_id)
- Determine current highest `version`
- Set new picture's version = previous + 1
- Mark all other pictures for that parent as `deleted = true`
Keep soft-delete logic but preserve full version history."

---

### 🔹 Task 2: Add `version` Parameter to Metadata Endpoint

**Prompt:**
"Update `get_picture_metadata()` to:
- Accept optional `version: int = None`
- If version provided, return exact match (non-deleted)
- If not, return latest non-deleted version"

Also apply to `get_picture()` and `thumbnail` endpoints.

---

### 🔹 Task 3: Implement Multi-Picture Listing

**Prompt:**
"Add `GET /pictures/{parent_type}/{parent_id}/all`:
- Returns list of all pictures (non-deleted and deleted)
- Include metadata fields, sort by `version` descending
- Add response model `PictureListResponse` with list[PictureMetadata]"

---

### 🔹 Task 4: Hard Delete Worker

**File:** `workers/picture_cleanup.py`

**Prompt:**
"Create background script or Azure Function to:
- Periodically query for `deleted=true` records older than N days
- Delete associated blobs from both containers
- Remove record from CosmosDB
- Configurable TTL via env var `PICTURE_DELETE_TTL_DAYS`"

---

### 🔹 Task 5: Add Moderation Hook Placeholder

**Prompt:**
"In `upload_picture()`, add call to:
```python
scan_for_violations(image_bytes: bytes) -> bool
```
Stub implementation returns True.
Future implementation may integrate with AV/Malware APIs."

---

### 🔹 Task 6: CDN Proxy Support (optional, placeholder)

**Prompt:**
"Add optional support for:
- Constructing public CDN URLs for thumbnails (if CDN is enabled)
- Use config flag `USE_CDN = True` and prefix URLs accordingly
Stub in `utils/cdn.py` to generate and swap CDN paths."

---

## 🧪 Acceptance Criteria

- Uploading multiple pictures increments version.
- Listing endpoint returns full version history.
- Deletion flag works per version.
- Hard delete job removes old records from blob and DB.
- Moderation stub and CDN prep logic in place, inactive by default.

---

This is the final phase scoped in current roadmap. Optional Phase 8 may include centralized monitoring, alerting, and infra cleanup.
