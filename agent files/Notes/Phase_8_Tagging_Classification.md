# Phase 8: Tagging & Classification — Codex-Friendly Task Plan

This phase adds tags and advanced filtering to Notes.

---

## 🎯 Goals

1. Allow notes to be tagged for organization/search.
2. Enable filtering by tag in API.

---

## 📦 Key Deliverables

1. Tag normalization and model changes.
2. Filtering in GET endpoint.
3. Indexing for tags in Cosmos.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Schema/Service Update

**Prompt:**
- Add tags normalization/validation in create/update flows.
- Ensure tags are lowercased, trimmed, and deduplicated.

---

### 🔹 Task 2: Filtering Support

**Prompt:**
- Update `GET /notes` endpoint to support filtering by one or more tags.

---

### 🔹 Task 3: Indexing

**Prompt:**
- Add tags to CosmosDB indexing policy for efficient queries.

---

### 🔹 Task 4: Testing

**Prompt:**
- Unit/integration tests for creating/filtering notes by tag.

---

## 🧪 Acceptance Criteria

- Notes can be created and queried by tag.
- Tag values are consistent and searchable.
- Filtering returns only matching notes.

---
