# Phase 10: Notification Integration — Codex-Friendly Task Plan

This phase adds event-driven notification hooks for Notes.

---

## 🎯 Goals

1. Notify users when notes are shared or they are mentioned.
2. Integrate with event bus or notification system.

---

## 📦 Key Deliverables

1. Service emits events for shares/mentions.
2. Pub/sub or notification handler integration.
3. Delivery and logging of notification actions.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Event Emitter

**Prompt:**
- Add hooks in notes service for `on_note_shared` and `on_note_mentioned`.
- Emit structured events (with author, recipient, note_id, action).

---

### 🔹 Task 2: Integration

**Prompt:**
- Publish events to pub/sub, message bus, or notification service.
- Ensure async, non-blocking delivery.

---

### 🔹 Task 3: Notification Delivery

**Prompt:**
- Ensure notifications are delivered for:
    - Notes shared with user
    - Mentions in notes user has access to

---

### 🔹 Task 4: Logging

**Prompt:**
- Log notification events and any failures.

---

### 🔹 Task 5: Testing

**Prompt:**
- Mock/test event emission and notification delivery.

---

## 🧪 Acceptance Criteria

- Notifications are triggered for shares and mentions.
- Events are published in required format.
- Failures are logged and testable.

---

Ready for review! Request final phase for Full-Text Search & Vector Retrieval.
