# Phase 7: Rich Content + Attachments — Codex-Friendly Task Plan

This phase introduces rich note bodies and file/media attachments.

---

## 🎯 Goals

1. Enable notes to support media files (images, and videos) and text files, and rich text content.
2. Safely render rich content in all consumers.

---

## 📦 Key Deliverables

1. Schema: `attachments: List[str]` in Note.
2. File upload flow (blob storage, S3, etc.).
3. Markdown/renderer extension for inlined attachments.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Schema Extension

**Prompt:**
- Add `attachments: List[str]` to Note model.

---

### 🔹 Task 2: File Upload Flow

**Prompt:**
- We are going to extend and use picture_service.
- Once extended to support text file attachements, we need to rename picture_service to file_service.
- Store files in blob storage; store URLs/paths in `attachments`.

---

### 🔹 Task 3: Renderer Extension

**Prompt:**
- Update markdown/rendering utilities to inline images and file links based on `attachments`.

---

### 🔹 Task 4: Security

**Prompt:**
- Validate attachment types and file size. Set specific file size limits constants per genre (images, videos, and text).
- Ensure files are only accessible to permitted users.

---

### 🔹 Task 5: Testing

**Prompt:**
- Test attachment upload, retrieval, and inline rendering.

---

## 🧪 Acceptance Criteria

- Notes can include file/image attachments and display inline (where supported).
- Attachments are stored, validated, and permissioned correctly.
- Markdown with attachments renders safely in all clients.

---
