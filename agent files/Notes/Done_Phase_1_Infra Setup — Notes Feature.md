# Phase 1: Infra Setup — Codex-Friendly Task Plan

This plan is scoped to foundational infra and schema work for the Notes feature, as defined in `Notes PIP.md` and `Notes PRD.md`.

---

## 🎯 Goals

1. Provision CosmosDB and schema for the notes feature, supporting both MVP and future extension.
2. Implement core data model and enums in codebase.
3. Set up reproducible, environment-aware infrastructure as code.

---

## 📦 Key Deliverables

1. CosmosDB container: `notes`, with proper partition key and indexes.
2. Schema defined and deployable via Bicep and environment param files.
3. `Note` Pydantic model with enums for type and visibility.
4. Infrastructure deploy validation in at least one dev environment.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Define CosmosDB Container

**File:** `infra/container.bicep`

**Prompt:**
"Add a new CosmosDB container named `notes` with:
- Partition key: `/author_id`
- Indexes: `/object_id`, `/created_at`, `/note_group_id`
- Excluded from indexing: `/content/*`
- TTL disabled (`defaultTtl: -1`)
- Consistency: Session

Update all environment-specific param files (e.g. `main-parameters-dev.json`, `main-parameters-test.json`, etc.) to ensure container is provisioned in all environments.
"

---

### 🔹 Task 2: Prepare Infrastructure Deployment

**Files:** `infra/main.bicep`, environment param files, Makefile (if used)

**Prompt:**
"Wire the `notes` container into infra deployment flows:
- Update the root bicep and param files for all environments.
- Add variables/targets for infra deployment (if Makefile or CI/CD is used).
- Document any new ENV variables or secrets needed."

---

### 🔹 Task 3: Implement the Note Model

**File:** `crud/models/note.py`

**Prompt:**
"Create a Pydantic `Note` model that supports all planned fields and enums:
- `id: UUID`
- `author_id: UUID`  (partition key)
- `object_id: UUID`
- `object_type: NoteObjectType` (enum)
- `note_group_id: UUID` (generated using uuid5)
- `content: str`
- `created_at: datetime`
- `updated_at: datetime`
- `visibility: NoteVisibility` (enum, default `PRIVATE`)
- `tags: List[str]` (default empty list)
- `version: int` (default 1)

Also define:

```python
class NoteObjectType(str, Enum):
    PERSON = \"person\"
    COMMUNITY = \"community\"
    CONVERSATION = \"conversation\"

class NoteVisibility(str, Enum):
    PRIVATE = \"private\"
    SHARED = \"shared\"
    PUBLIC = \"public\"
    GROUP = \"group\"

## 🧪 Acceptance Criteria

- All infra and schema details are discoverable by future team members.
- Param files are updated and infra deploys cleanly in CI or local.
- Note model is importable and passes basic instantiation/unit tests.
- Enums for type and visibility are present and ready for CRUD/route logic in Phase 2.

---
