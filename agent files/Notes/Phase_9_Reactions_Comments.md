# Phase 9: Reactions & Comments — Codex-Friendly Task Plan

This phase brings lightweight collaboration to Notes via reactions and comments.

---

## 🎯 Goals

1. Allow users to react to and comment on notes.
2. Display reactions/comments in note detail.

---

## 📦 Key Deliverables

1. Models: `note_reactions`, `note_comments`.
2. Endpoints for posting/fetching reactions/comments.
3. Update GET note to include these fields.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Define Models

**Prompt:**
- Add models for `NoteReaction` and `NoteComment` linked to `note_id`.

---

### 🔹 Task 2: Endpoints

**Prompt:**
- `POST /notes/{note_id}/reactions`
- `POST /notes/{note_id}/comments`
- Update `GET /notes/{note_id}` to return reactions/comments.

---

### 🔹 Task 3: Service Logic

**Prompt:**
- Validate user is allowed to react/comment per visibility/sharing.
- Aggregate reactions for each note.

---

### 🔹 Task 4: Testing

**Prompt:**
- Test posting and retrieving reactions/comments for authorized/unauthorized users.

---

## 🧪 Acceptance Criteria

- Notes support reactions and comments via API.
- Aggregates are accurate and reflect current state.
- Only authorized users may react or comment.

---
