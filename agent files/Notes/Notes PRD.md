### 1. File System

**Backend** (`peepsapi/`)

```
peepsapi/
├── crud/
│   ├── models/
│   │   └── note.py                # Pydantic models for notes
│   ├── routes/
│   │   └── notes.py               # REST API endpoints
│   └── services/
│       └── notes_service.py       # Business logic for notes
├── utils/
│   ├── markdown.py                # generic Markdown rendering, permissions
│   └── permissions.py             # application wide permissions
tests/
└── test_notes.py                  # Unit/integration tests for notes
```

---

### 2. Person-Centric Notes

* **Feature goal**: Enable any person to take notes on any `person` object (including themselves), storing markdown-formatted content. Extendable to other object types.

* **API relationships**:

  * `POST /notes/{object_type}/{object_id}` — create note
  * `GET /notes{object_type}/{object_id}` — retrieve all notes on an object by current user
  * `PATCH /notes/{note_id}` — update note
  * `DELETE /notes/{note_id}` — delete note

---

### 3. System Architecture Overview

* **Architecture**: Follows <PERSON>eeps' standard service/repo split
* **Stack**: Python backend, CosmosDB, REST APIs
* **Integration points**: Auth system for user ID; CosmosDB containers; `Person` entity model

---

### 4. Infrastructure Setup

#### 4.1. CosmosDB Container

* Name: `notes`
* Partition key: `/author_id`
* Throughput: Auto-scale enabled
* Indexing policy:
  * Included: `object_id`, `created_at`, `note_group_id`, `tags` (future)
  * Excluded: `content` (unless full-text search planned)
* TTL: Disabled (may add cleanup policy later)
* Consistency: Session

#### 4.2. Bicep Definition

Define CosmosDB container in `infra/container.bicep`:

```bicep
resource notesContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2021-10-15' = {
  name: 'notes'
  properties: {
    partitionKey: {
      paths: ['/author_id']
      kind: 'Hash'
    }
    defaultTtl: -1
    indexingPolicy: {
      indexingMode: 'consistent'
      includedPaths: [
        { path: '/object_id/?' }
        { path: '/created_at/?' }
        { path: '/note_group_id/?' }
      ]
      excludedPaths: [
        { path: '/content/*' }
      ]
    }
  }
}
```

---

### 5. Database Schema Design

* **CosmosDB JSON schema**:

```json
{
  "id": "uuid",
  "author_id": "uuid",             // ID of person creating the note
  "object_id": "uuid",             // ID of person being noted on
  "object_type": "person",         // Future-proofing
  "note_group_id": "uuid",         // Deterministic group ID for threads; used for versioning, not needed for querying in V1. uuid5(author, object) mimic pattern from connection_service.gen_connection_id()
  "content": "# markdown body",
  "created_at": "ISO 8601",
  "updated_at": "ISO 8601",
  "visibility": "private",         // Future: public, group
  "tags": ["optional"],
  "version": 1                      // Future: versioning support
}
```

* **Data models**
```python
# usage similar to PictureService.get_container_by_owner_type
NoteObjectType(str, Enum):
    PERSON = "person"
    COMMUNITY = "community"
    CONVERSATION = "conversation"

NoteVisibility(str, Enum):
    PRIVATE = "private"
    SHARED = "shared"
    PUBLIC = "public"
    GROUP = "group"
```

* **Partition key**: `author_id`
* **Indexes**: on `object_id`, `author_id`, `note_group_id`, `created_at`

---

### 6. Comprehensive API Design

#### 6.1. POST `/notes/{object_type}/{object_id}`

* **Purpose**: Create a new note
* **Request body**:

```json
{
  "object_id": "<uuid>",
  "object_type": "person",
  "content": "string (markdown)",
  "tags": ["optional"]
}
```

* **Response**:

```json
{
  "data": {
    "note": "<note>"
  }
}
```

#### 6.2. GET `/notes/{object_type}/{object_id}`

* **Purpose**: Retrieve user’s notes on a specific object
* **Response**:

```json
{
  "data": [
    {
      "id": "<uuid>",
      "content": "...",
      "updated_at": "..."
    }
  ]
}
```

#### 6.3. PATCH `/notes/{note_id}`

* **Request body**:

```json
{
  "content": "updated markdown",
  "tags": ["optional"]
}
```

#### 6.4. DELETE `/notes/{note_id}`

* Soft delete for now (optional flag to retain future undo support)

---

### 7. Detailed CRUD Operations

* **Create**: Validate `object_type`, check object existence; require `content`
* **Read**: Filter by `object_id`, scoped to `author_id`
* **Update**: Author-only access, audit `updated_at`
* **Delete**: Soft-delete model or retain for future recovery

---

### 8. Security Considerations

* Author-only access to notes in V1
* Future visibility roles: `private`, `shared`, `group`, `public`
* Data sanitization of markdown to prevent XSS (render markdown server-side or sanitize frontend)

---

### 9. Testing Strategy

* Unit tests for:

  * CRUD operations
  * Permissions (only author can edit/delete)
* Integration tests for route + service layer
* Edge cases: Empty content, missed or malformed object ID

---

### 10. Data Management

* CosmosDB lifecycle: No TTL for now
* Optional future indexing for `tags`
* Future support for `version` and `history` documents

---

### 11. Error Handling & Logging

* Use Peeps error conventions (`ServerError`, `ResourceNotFoundError`)
* Decorate all route methods with `@handle_exceptions("NOTES")`
* Log note creation, deletion, and update attempts with metadata

---

### 12. Planned Future Features (Out of Scope for V1)

While V1 restricts functionality to markdown-based private notes on `person` objects, the system design supports future extensions:

#### 12.1. Versioning and Edit History
- Maintain an immutable history of edits
- `note_group_id` will be used as grouping root
- Create `note_versions` table or inline `version_of`/`previous_versions` schema
- Expose `GET /notes/{id}/history` API

#### 12.2. Visibility and Sharing
- Support visibility modes: `PUBLIC`, `GROUP`, `SHARED`
- Extend model with `shared_with: List[UUID]` or `group_id`
- Requires role-based permissions enforcement and RBAC implementation

#### 12.3. Rich Content and Attachments
- Support uploading images, linking files, or embedding media
- Add `attachments: List[str]` to schema
- Extend content renderer for rich text

#### 12.4. Tagging and Classification
- Enable custom or system-defined tags
- Index tags for query/filter
- UI/UX integration: suggest tags, group by tag

#### 12.5. Note Reactions and Comments
- Allow reactions (👍, 👎, 🧠) and threaded comments
- Attach `note_id` to `reaction` or `comment` entity

#### 12.6. Notifications & Feeds
- Notify users when mentioned a note that is visible to them (requires notification service implementation)
- Notify users when it's shared with them (requires notification service implementation)
- Publish to activity feed based on visibility or share

#### 12.7. Search and Query Expansion
- Filter notes by tag, content keyword, or time window
- Integrate with full-text search or vector DBs (Azure Cognitive Search?)
