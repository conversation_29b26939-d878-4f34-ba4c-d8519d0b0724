# Phase 6: Visibility and Sharing — Codex-Friendly Task Plan

This phase enables notes to be shared, group/public, or private, per Notes PIP/PRD.

---

## 🎯 Goals

1. Support `private`, `shared`, `group`, and `public` visibility.
2. Allow notes to be shared with specific users or groups.

---

## 📦 Key Deliverables

1. Schema expanded: `visibility`, `shared_with: List[UUID]`, `group_id`.
2. RBAC rules enforced in routes/services.
3. `GET /notes/shared-with-me` endpoint.
4. Permissions unit/integration tests.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Schema Extension

**Prompt:**
- Add `visibility: NoteVisibility` (enum), `shared_with: List[UUID]`, `group_id: Optional[UUID]` to Note model.

---

### 🔹 Task 2: Service Logic/Permissions

**Prompt:**
- Update service logic to enforce visibility:
    - `private`: only author
    - `shared`: author and users in `shared_with`
    - `group`: users in `group_id`
    - `public`: anyone
- Implement RBAC permission checks in all CRUD/read endpoints.

---

### 🔹 Task 3: New Endpoint

**Prompt:**
- Implement `GET /notes/shared-with-me` to return all notes shared to current user.

---

### 🔹 Task 4: Testing

**Prompt:**
- Add tests for all visibility modes:
    - Create, read, update, delete in each mode
    - Permissions boundary cases (e.g., user in/out of group)

---

## 🧪 Acceptance Criteria

- Notes respect their `visibility` mode at API and service layer.
- Unauthorized reads/writes are blocked.
- `GET /notes/shared-with-me` returns all notes where user is in `shared_with` or group.
- Regression tests for CRUD with new permissions.

---
