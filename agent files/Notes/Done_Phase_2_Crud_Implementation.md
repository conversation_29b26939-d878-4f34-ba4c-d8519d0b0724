# Phase 2: CRUD Backend — Codex-Friendly Task Plan

This implementation plan corresponds exactly to Phase 2 in `Notes PIP.md` and draws all relevant behavior from `Notes PRD.md`. All tasks below are engineered for direct use by Codex/Cursor/AI agents.

---

## 🎯 Goals

1. Implement core Notes model CRUD endpoints and service logic.
2. Enforce author ownership and integrate markdown sanitization.
3. Scaffold for extensible future expansion (versioning, visibility).

---

## 📦 Key Deliverables

1. `Note` model and enums are available for route/service use.
2. `crud/routes/notes.py` exposes all core endpoints:
    - `POST /notes/{object_type}/{object_id}`
    - `GET /notes/{object_type}/{object_id}`
    - `PATCH /notes/{note_id}`
    - `DELETE /notes/{note_id}`
3. `crud/services/notes_service.py` implements the business logic.
4. Permissions, DI, markdown utils, and consistent error handling are used throughout.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Define Note Model (If not done in Phase 1)

**File:** `crud/models/note.py`

**Prompt:**
"Ensure the `Note` model includes:
- All schema fields: `id`, `author_id`, `object_id`, `object_type`, `note_group_id`, `content`, `created_at`, `updated_at`, `visibility`, `tags`, `version`
- Enums: `NoteObjectType`, `NoteVisibility` as defined in the PRD
- Pydantic model should enforce type validation and sensible defaults."

---

### 🔹 Task 2: Create Service Layer

**File:** `crud/services/notes_service.py`

**Prompt:**
"Implement a NotesService class with:
- `create_note(author_id, object_id, object_type, content, tags) -> Note`
    - Validates `object_type == NoteObjectType.PERSON`
    - Generates `note_group_id` using `uuid5(author_id, object_id)`
    - Sanitizes markdown with `utils/markdown.py`
    - Stores the new note in CosmosDB with correct partitioning
- `get_notes_for_object(author_id, object_id) -> List[Note]`
    - Returns all notes for `object_id` under current `author_id`, sorted by `updated_at desc`
- `update_note(note_id, author_id, updates) -> Note`
    - Only allows update if `author_id` matches the note’s author
    - Applies updates and updates `updated_at`
    - Sanitizes markdown
- `delete_note(note_id, author_id)`
    - Soft deletes the note (sets `visibility` to `PRIVATE` and/or flags as deleted)
    - Only allowed for the note’s author
- All methods should log operations and raise errors using Peeps conventions."

---

### 🔹 Task 3: Scaffold Routes

**File:** `crud/routes/notes.py`

**Prompt:**
"Create FastAPI endpoints for notes:
- `POST /notes`
    - Accepts: `object_id`, `object_type`, `content`, `tags`
    - Fetches `author_id` from auth/session context
    - Returns the created note (JSON)
- `GET /notes?object_id=...`
    - Fetches notes scoped by `author_id` and `object_id`
    - Returns a list of notes (JSON), sorted by `updated_at desc`
- `PATCH /notes/{note_id}`
    - Accepts partial update for `content`, `tags`
    - Only allows if current user is author
    - Returns the updated note (JSON)
- `DELETE /notes/{note_id}`
    - Soft deletes note, only by author
    - Returns success/failure
- Decorate all endpoints with `@handle_exceptions(\"NOTES\")`
- Use dependency injection for service/container."

---

### 🔹 Task 4: Ownership and Permissions Enforcement

**Files:** `crud/services/notes_service.py`, `crud/routes/notes.py`

**Prompt:**
"Ensure:
- All modifying operations (update, delete) require `author_id` to match note’s author
- Unauthorized attempts raise `ServerError` (401/403)
- Only `object_type=person` is valid in V1 (raise error otherwise)
- Add clear error messages for all violations"

---

### 🔹 Task 5: Markdown Processing Integration

**File:** `utils/markdown.py`, `crud/services/notes_service.py`

**Prompt:**
"On note create and update:
- Run `sanitize_markdown(content)` from `utils/markdown.py`
- Reject or clean content containing XSS or disallowed patterns
- Ensure markdown is rendered safely for future UI consumption"

---

### 🔹 Task 6: Dependency Injection and Logging

**Files:** All service and route files

**Prompt:**
"Follow Peeps convention for DI:
- Inject Cosmos container/service in all routes
- Log create/update/delete attempts (log fields: `author_id`, `note_id`, `operation`, status)
- Ensure errors are caught by `@handle_exceptions` and logged at service level"

---

## 🧪 Acceptance Criteria

- `POST /notes/{object_type}/{object_id}` creates a note and stores it in CosmosDB with the right partition key.
- `GET /notes/{object_type}/{object_id}` returns all notes for that object by current author.
- `PATCH /notes/{note_id}` only works for the author; updates `content` and/or `tags`.
- `DELETE /notes/{note_id}` soft-deletes the note; only works for the author.
- All endpoints return errors in standard Peeps response format.
- All content is sanitized and safe for downstream rendering.

---
