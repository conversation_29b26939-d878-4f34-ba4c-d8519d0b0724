# Phase 4: Internal QA & Performance Hardening — Codex-Friendly Task Plan

This plan covers all internal validation, scale testing, and real-world “dogfooding” to ensure Notes feature reliability before external or production rollout.

---

## 🎯 Goals

1. Ensure notes feature is robust for both self and cross-person use cases.
2. Validate performance under high-density author partitions.
3. Confirm markdown rendering and data flows are safe for all downstream consumers (e.g., agents, mobile).

---

## 📦 Key Deliverables

1. Internal manual test suite (scenarios, scripts).
2. Automated scale and stress tests.
3. Performance tuning and bottleneck documentation.
4. Optional: GET endpoint supports sorting (`created_at desc`).

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Manual End-to-End Validation

**Prompt:**
- Test full create→read→update→delete loop on:
    - A note about self.
    - A note about another person.
    - Multiple notes per person.
- Verify correct ownership/partitioning (notes only visible to author).
- Check that all model fields populate as expected in Cosmos and API response.
- Confirm that deleted notes are not returned by default.
- Test markdown rendering for edge-case inputs (large notes, malicious/complex markdown).

---

### 🔹 Task 2: Scale & Performance Testing

**Prompt:**
- Create test scripts to insert 1,000+ notes under a single author.
- Time and document:
    - Note creation time at scale.
    - GET queries under high partition load.
- Identify any Cosmos throughput issues or bottlenecks.
- If required, tune throughput or indexing.

---

### 🔹 Task 3: Sorting & API Tuning

**Prompt:**
- Add or verify that `GET /notes/{object_type}/{object_id}` supports sorting by `created_at desc`.
- Test pagination or chunking (if needed for UI or agent use).

---

### 🔹 Task 4: Markdown Render Safety

**Prompt:**
- Validate that markdown content from DB is safe to render in web/mobile/agent environments.
- Document any markdown edge cases or pitfalls for downstream consumers.

---

## 🧪 Acceptance Criteria

- All CRUD flows work for self and other-person notes in manual and automated tests.
- GET endpoint reliably returns only the current author's notes, and soft-deleted notes are never returned.
- CosmosDB shows acceptable performance and response times under load (target: <500ms for GET 1,000 notes).
- Markdown rendering does not cause any rendering bugs, security issues, or edge-case failures.
- Documentation for any performance, infra, or downstream render issues is up-to-date.
