# Phase 5: Versioning Support — Codex-Friendly Task Plan

This phase adds versioning/edit history to Notes, as outlined in Notes PIP/PRD.

---

## 🎯 Goals

1. Allow users to access past edits for any note.
2. Persist version history with minimal friction for read/update APIs.

---

## 📦 Key Deliverables

1. `note_versions` container/table (or inline version chain).
2. Service logic for creating/accessing versions.
3. `GET /notes/{id}/history` endpoint.
4. Update logic to create version records on edit.
5. Unit/integration tests for version lineage and retrieval.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Schema Changes

**Prompt:**
- Add a new `note_versions` container or table with:
    - `version_id: UUID`
    - `note_id: UUID`
    - `content: str`
    - `author_id: UUID`
    - `created_at: datetime`
- OR: Add `version_of` and `latest_version_id` fields to base note model.

---

### 🔹 Task 2: Service Logic

**Prompt:**
- On PATCH/update, before modifying the note:
    - Write previous version to `note_versions`.
    - Increment `version` field on the updated note.
- Implement `get_note_history(note_id) -> List[NoteVersion]`
    - Sorted by `created_at desc`.

---

### 🔹 Task 3: Route Changes

**Prompt:**
- Add new route: `GET /notes/{note_id}/history`
    - Returns the full edit/version lineage for the note.

---

### 🔹 Task 4: Testing

**Prompt:**
- Add tests for:
    - Version creation on update.
    - Retrieval of version history.
    - Edge: editing non-existent note, requesting history for non-owned note.

---

## 🧪 Acceptance Criteria

- Editing a note creates a new version in `note_versions`.
- `GET /notes/{id}/history` returns all versions, newest first.
- Versions are only accessible to the note’s author.
- No breaking changes for existing note CRUD flows.

---
