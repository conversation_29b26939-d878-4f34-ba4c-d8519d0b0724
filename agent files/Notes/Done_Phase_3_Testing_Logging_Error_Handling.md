# Phase 3: Testing, Logging & Error Handling — Codex-Friendly Task Plan

This plan covers all validation, logging, and error-handling work to ensure correctness, observability, and robustness, as defined in `Notes PIP.md` and `Notes PRD.md`.

---

## 🎯 Goals

1. Ensure CRUD endpoints and service logic work as expected.
2. Enforce and test Peeps-standard error and log formats.
3. Catch regressions and edge cases before rollout.

---

## 📦 Key Deliverables

1. Unit tests for all major service methods and edge cases.
2. Integration tests covering API, auth, and partitioning behavior.
3. Structured logs for all CRUD actions.
4. Error handling that matches Peeps conventions.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Implement Unit Tests

**File:** `tests/test_notes.py`

**Prompt:**
"Add unit tests for all service-layer methods in `notes_service.py`:
- Test happy paths for `create_note`, `get_notes_for_object`, `update_note`, `delete_note`.
- Test edge cases:
    - Attempt to update/delete as non-author (should raise error).
    - Invalid input: missing `content`, invalid `object_type`, empty `object_id`.
    - Ensure markdown is sanitized and saved as expected.
- Test correct population of model fields: `created_at`, `updated_at`, `note_group_id`, `version`.
- Validate that deleted notes are not returned by default read operations."

---

### 🔹 Task 2: Implement Integration/API Tests

**File:** `tests/test_notes.py`

**Prompt:**
"Write integration tests for all API endpoints:
- Use a test client to call `POST /notes/{object_type}/{object_id}`, `GET /notes/{object_type}/{object_id}`, `PATCH /notes/{id}`, `DELETE /notes/{id}`.
- Inject different auth contexts to check author scoping (partition key).
- Validate that GETs only return notes belonging to the correct author.
- Confirm that errors are surfaced for unauthorized actions."

---

### 🔹 Task 3: Logging

**Files:** `crud/services/notes_service.py`, `crud/routes/notes.py`

**Prompt:**
"Add structured logging to all create, update, and delete actions:
- Log fields: `note_id`, `author_id`, `object_id`, `operation`, status.
- Use Peeps' existing logger and formatting.
- Ensure logs are written on both successful and error cases."

---

### 🔹 Task 4: Error Handling

**Files:** All route/service files

**Prompt:**
- "Wrap all route methods with `@handle_exceptions(\"NOTES\")` (from utils/decorators.py).
- Raise `ServerError`, `ResourceNotFoundError` for known error cases.
- Ensure API responses conform to Peeps’ error response format:

```json
{
  \"data\": null,
  \"error\": {
    \"message\": \"...\",
    \"code\": \"...\",
    \"details\": { ... }
  }
}

## 🧪 Acceptance Criteria

- All CRUD methods and endpoints are covered by unit and integration tests.
- Invalid input, permission errors, and XSS attempts are caught by tests.
- All actions are logged with required metadata.
- API returns errors using Peeps-standard structure, with clear error codes/messages.
- Ready for UAT and confident backend delivery.

---
