# Phase 11: Full-Text Search & Vector Retrieval — Codex-Friendly Task Plan

This phase brings search and LLM/semantic capabilities to Notes.

---

## 🎯 Goals

1. Enable robust keyword and semantic search.
2. Support LLM agent access to relevant notes.

---

## 📦 Key Deliverables

1. FTS pipeline (Azure Cognitive Search, OpenSearch, etc.).
2. Vector embedding of note content (optional).
3. Search endpoint(s) and ranking.

---

## ✅ Codex-Friendly Tasks

### 🔹 Task 1: Search Index Pipeline

**Prompt:**
- Integrate note write/update/delete flows with FTS indexer.
- Sync `content`, `tags`, `object_type` fields.

---

### 🔹 Task 2: Search Endpoint

**Prompt:**
- Implement `GET /notes/search?q=...` API.
- Support basic and advanced filtering (tag, author, date).

---

### 🔹 Task 3: Vector Embedding (Optional)

**Prompt:**
- On note create/update, generate vector embedding for `content`.
- Store in vector DB or with search index.

---

### 🔹 Task 4: Testing

**Prompt:**
- Test end-to-end search:
    - By keyword
    - By tag
    - By semantic similarity (if enabled)

---

## 🧪 Acceptance Criteria

- Search API returns relevant notes for keyword queries.
- (If enabled) LLM/agent use case retrieves contextually similar notes.
- Indexing pipeline is reliable and up to date.

---

All phases complete! Let me know which phase you want next, or if you want a zipped bundle of all files.
