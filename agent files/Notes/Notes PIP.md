# Notes Feature — Phased Implementation Plan

This implementation plan is based on the finalized PRD and designed for clean rollout, testability, and future extensibility. It includes all phases, from initial infrastructure setup to long-term advanced feature capabilities.

---

# Security Integration (In All Phases)

* Use FastAPI middleware for authentication
* Enforce author ownership on all operations
* Structured logging per CRUD action
* Wrap all route logic with `@handle_exceptions("NOTES")`
* Input validation and markdown sanitization to prevent XSS

---

# Phase 1: Infra Setup

## Goals:

1. Provision CosmosDB for notes storage
2. Prepare Bicep infra and environment setup
3. Define and validate data models

## Key Deliverables:

1. CosmosDB container `notes` with:

   * Partition key: `/author_id`
   * Indexes: `object_id`, `created_at`, `note_group_id`
   * Excluded from indexing: `content`
2. Bicep definition added to `infra/container.bicep`
3. Parameter files updated across environments
4. `crud/models/note.py`:

   * Schema fields: `id`, `author_id`, `object_id`, `object_type`, `note_group_id`, `content`, `created_at`, `updated_at`, `visibility`, `tags`, `version`
   * Enum declarations for `NoteObjectType`, `NoteVisibility`
5. Infra validation locally with mocks or dev deployment

---

# Phase 2: V1 CRUD Implementation

## Goals:

1. Build initial backend logic for personal notes on `person`
2. Establish reliable service layer and REST API surface

## Key Deliverables:

1. `crud/routes/notes.py`:

   * `POST /notes/{object_type}/{object_id}`
   * `GET /notes/{object_type}/{object_id}`
   * `PATCH /notes/{note_id}`
   * `DELETE /notes/{note_id}`
2. `crud/services/notes_service.py`:

   * Methods: `create_note`, `get_notes_for_object`, `update_note`, `delete_note`
   * Inject Cosmos container with DI
3. Ownership validation via `author_id` from auth context
4. Use of `utils/markdown.py` to sanitize content
5. Limit `object_type` to `person` only in V1

---

# Phase 3: Testing, Logging & Error Handling

## Goals:

1. Ensure system correctness and observability
2. Catch regressions and unexpected inputs

## Key Deliverables:

1. Unit tests in `test_notes.py`:

   * CRUD happy paths
   * Edge cases: empty fields, invalid types
2. Integration tests covering route → service interaction
3. Logs for all actions including: `note_id`, `author_id`, `operation`
4. Exception handling with Peeps-standard `ServerError`, `ResourceNotFoundError`

---

# Phase 4: Internal QA & Performance Hardening

## Goals:

1. Validate V1 notes for internal use (agents, mobile)
2. Handle scale and correctness edge cases

## Key Deliverables:

1. Internal tests for note flows on self and others
2. Optional: sorting in GET `/notes` by `created_at desc`
3. Test high-density authorship: 1000+ notes
4. Markdown render safety validation

---

# Phase 5: Versioning Support

## Goals:

1. Enable note edit tracking and access to history

## Key Deliverables:

1. Option A: Add new `note_versions` container with fields:

   * `version_id`, `note_id`, `content`, `author_id`, `created_at`
2. Option B: Extend base note schema with:

   * `version_of`, `latest_version_id`
3. Add route: `GET /notes/{id}/history`
4. Patch note → automatically stores version before overwrite
5. Add version tests and optional lineage validation

---

# Phase 6: Visibility and Sharing

## Goals:

1. Allow notes to be shared across users, groups, or public

## Key Deliverables:

1. Schema additions:

   * `visibility` (Enum), `shared_with`, `group_id`
2. Add RBAC enforcement in `permissions.py`
3. Service logic updated to honor share rules
4. New endpoint: `GET /notes/shared-with-me`

---

# Phase 7: Rich Content + Attachments

## Goals:

1. Allow attachment uploads and rich note bodies

## Key Deliverables:

1. Schema: `attachments: List[str]`
2. Upload flow for media via blob
3. Markdown renderer extension for image/file embeds

---

# Phase 8: Tagging + Classification

## Goals:

1. Support tag-based search and filtering

## Key Deliverables:

1. Service-level tag normalization
2. Filtering param in `GET /notes/{object_type}/{object_id}?tag=...`
3. Index `tags` in Cosmos indexing policy

---

# Phase 9: Reactions & Comments

## Goals:

1. Enable lightweight collaboration and threaded feedback

## Key Deliverables:

1. Schema: `note_reactions`, `note_comments`
2. Endpoints:

   * `POST /notes/{id}/reactions`
   * `POST /notes/{id}/comments`
3. Update GET `/notes/{id}` to return recent reactions/comments

---

# Phase 10: Notification Integration

## Goals:

1. Enable event-driven awareness around note sharing and mentions

## Key Deliverables:

1. Event emitter hook (e.g., `on_note_shared`, `on_note_mentioned`)
2. Publish events to pub/sub system or existing event bus
3. Deliver events for shared notes and mentions

---

# Phase 11: Full-Text Search & Vector Retrieval

## Goals:

1. Support semantic and keyword search across notes

## Key Deliverables:

1. Azure Cognitive Search/OpenSearch pipeline
2. Index fields: `content`, `tags`, `object_type`, etc.
3. Optional: generate vector embeddings for agent-based search
4. Endpoint: `GET /notes/search?q=...`
