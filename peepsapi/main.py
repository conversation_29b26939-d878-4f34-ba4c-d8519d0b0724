"""Unified entry point for the PeepsAPI application.

Combines both the CRUD operations and the AI functionality.
This module initializes the FastAPI application, sets up middleware,
and defines the main API routes.
"""

import asyncio
import os
import time
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles

from peepsapi import config
from peepsapi.crud.jobs.comment_jobs import execute_increment_comment_counters
from peepsapi.crud.jobs.feed_jobs import (
    execute_add_each_others_posts_to_feeds,
    execute_add_post_to_feed,
    execute_create_feed,
    execute_remove_each_others_posts_from_feeds,
    execute_remove_post_from_feed,
)
from peepsapi.services.cosmos_containers import (
    get_comments_container,
    get_connections_container,
    get_feeds_container,
    get_people_container,
    get_posts_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.services.middleware.case_conversion import CaseConversionMiddleware
from peepsapi.services.middleware.error_handlers import add_exception_handlers
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)
logger.info("🛠️ Starting configuration loading...")
try:
    config.init()
    logger.info("✅ Configuration loading completed successfully")
except Exception as e:
    logger.error(f"❌ Error during configuration loading: {str(e)}")
    logger.error("❌ The process is frozen to prevent infinite failure loop on restart")
    logger.error("❌ Fix the problem and restart the process")

    # Freeze the process indefinitely
    while True:
        time.sleep(1)

# Import modules after config and logging initialization
from peepsapi.ai.routes.process import router as ai_router  # noqa: E402
from peepsapi.auth import add_auth_middleware  # noqa: E402
from peepsapi.auth import router as auth_router  # noqa: E402
from peepsapi.crud.router import router as crud_router  # noqa: E402
from peepsapi.crud.routes.admin import router as admin_router  # noqa: E402
from peepsapi.dashboard import router as dashboard_router  # noqa: E402

# Conditionally import data loading function
enable_data_loading = config.get("ENABLE_DATA_LOADING", "true").lower() != "false"

if enable_data_loading:
    # Import the data loading function conditionally as it's not a part of the Docker image build
    from scripts.data_load import validate_and_load_data  # noqa: E402


# Define lifespan context manager for startup/shutdown events
@asynccontextmanager
async def lifespan(_app):
    """Initialize database and load data on startup.

    Data loading can be disabled by setting the ENABLE_DATA_LOADING environment variable to 'false'.
    """
    try:
        # Initialize database connection
        from peepsapi.services.cosmos_db import (
            CosmosContainer,
            DatabaseConnection,
            DatabaseConnectionAsync,
        )

        DatabaseConnection.initialize()
        DatabaseConnectionAsync.initialize()

        # Check for required containers and create them if missing
        required_containers = [
            # Active containers
            # Auth containers
            "authentication_challenges",
            "device_tokens",
            "invite_tokens",
            "passkey_credentials",
            "registration_challenges",
            "session_tokens",
            # Crud containers
            "connections",
            "people",
            "pictures",
            "notes",
            # System containers
            "audit_logs",
            "locks",
            "active_jobs",  # Background jobs container
            "feeds",  # User feeds container
            # Inactive containers
            "comments",
            "communities",
            "conversations",
            "events",
            "messages",
            "posts",
        ]

        # List existing containers and create missing ones
        available_containers = CosmosContainer.list_all_containers()
        for container_name in required_containers:
            if container_name not in available_containers:
                CosmosContainer.create_container_if_not_exists(container_name)

    except Exception as e:
        logger.error(f"❌ Startup error: {str(e)}")
        raise

    from peepsapi.jobs import loop as jobs

    # Start jobs loop
    jobs_loop = asyncio.create_task(
        jobs.start_loop(
            job_handlers={
                "add_post_to_feed": execute_add_post_to_feed,
                "create_feed": execute_create_feed,
                "remove_post_from_feed": execute_remove_post_from_feed,
                "remove_each_others_posts_from_feeds": execute_remove_each_others_posts_from_feeds,
                "add_each_others_posts_to_feeds": execute_add_each_others_posts_to_feeds,
                "increment_comment_counters": execute_increment_comment_counters,
            },
            container_dependencies={
                "feeds": get_feeds_container(),
                "people": get_people_container(),
                "connections": get_connections_container(),
                "posts": get_posts_container(),
                "comments": get_comments_container(),
            },
        )
    )

    # The application is running
    yield

    # Stop jobs loop
    jobs.stop_loop()

    # Await for the job-loop shutdown
    await jobs_loop

    # Close async DB connection
    await DatabaseConnectionAsync.close_connection()


# Initialize FastAPI app with configuration from environment variables
app = FastAPI(
    title=config.get("APP_NAME", "PeepsAPI"),
    version=config.get("API_VERSION", "1.0.0"),
    debug=config.get("DEBUG_MODE", "false").lower() == "true",
    lifespan=lifespan,
)

# Add exception handlers
add_exception_handlers(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add case conversion middleware to convert camelCase to snake_case in requests
app.add_middleware(CaseConversionMiddleware)

# Add authentication middleware
add_auth_middleware(
    app,
    public_paths=[
        "/",  # Root path redirects to /dashboard or /dashboard/login
    ],
    exclude_path_prefixes=[
        # peepsapi/main.py
        "/.well-known/",
        "/favicon.ico",
        "/health",
        # peepsapi/auth/routes/azure_ad.py
        "/auth/azure-ad/login",
        "/auth/azure-ad/callback",
        # peepsapi/auth/routes/login.py
        "/auth/login/challenge",
        "/auth/login/verify",
        # peepsapi/auth/routes/recovery.py
        "/auth/recover/challenge",
        "/auth/recover/initiate",
        "/auth/recover/verify",
        # peepsapi/auth/routes/registration.py
        "/auth/register/challenge",
        "/auth/register/verify",
        # peepsapi/dashboard/__init__.py
        "/dashboard/login",
    ],
)

webauthn_enabled = os.getenv("WEBAUTHN_ENABLED", "true").lower() == "true"

# Include routers
app.include_router(auth_router, prefix="/auth")
app.include_router(crud_router)
app.include_router(ai_router, prefix="/ai", tags=["ai"])
app.include_router(dashboard_router, prefix="/dashboard")
app.include_router(admin_router)

# Mount static files
app.mount("/static", StaticFiles(directory="./static"), name="static")


@app.get("/health")
async def health_check():
    """Check the health of the API.

    Returns:
        dict: A status message indicating the API is healthy
    """
    return {"status": "healthy"}


@app.get("/", include_in_schema=False)
def read_root(request: Request):
    """Root endpoint for the API.

    Redirects to the dashboard login, which will show the login page if not authenticated.
    """
    if hasattr(request.state, "person_id"):
        return RedirectResponse(url="/dashboard")

    return RedirectResponse(url="/dashboard/login")


@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    """Serve the favicon for the application."""
    return FileResponse("static/favicon.ico")


@app.get("/.well-known/apple-app-site-association")
async def apple_app_site_association():
    """Apple App Site Association endpoint for Universal Links and App Clips.

    Returns:
        dict: AASA configuration for the app
    """
    logger.debug("📝 Apple App Site Association endpoint accessed")
    return {
        "webcredentials": {
            "apps": os.getenv("WEBAUTHN_APPLE_APPS", "").split(","),
        }
    }


@app.get("/.well-known/assetlinks.json")
async def android_asset_links():
    """Android App Links / Digital Asset Links endpoint.

    Serves the assetlinks.json file required for associating the website
    with an Android application for features like shared login credentials.

    Returns:
        FileResponse: The assetlinks.json file.
    """
    logger.debug("📝 Android Asset Links endpoint accessed")
    file_path = "static/.well-known/assetlinks.json"
    # Ensure the Content-Type is explicitly application/json
    # FileResponse usually infers it, but it's good to be sure for critical files.
    return FileResponse(file_path, media_type="application/json")
