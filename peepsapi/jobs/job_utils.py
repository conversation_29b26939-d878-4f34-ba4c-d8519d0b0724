from typing import Literal, Union
from uuid import uuid4

from azure.cosmos.exceptions import CosmosHttpResponseError

from peepsapi.crud.services.connection_service import gen_connection_id
from peepsapi.jobs.loop import reserve
from peepsapi.models.datetime import UTCDateTime
from peepsapi.models.job import (
    AddEachOthersPostsToFeedsParams,
    AddPostToFeedParams,
    CreateFeedParams,
    IncrementCommentCountersParams,
    Job,
    RemoveEachOthersPostsFromFeedsParams,
    RemovePostFromFeedParams,
)
from peepsapi.services.cosmos_db import CosmosContainerAsync
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def create(
    action: Literal[
        "add_post_to_feed",
        "create_feed",
        "remove_post_from_feed",
        "remove_each_others_posts_from_feeds",
        "add_each_others_posts_to_feeds",
        "increment_comment_counters",
    ],
    params: Union[
        AddPostToFeedParams,
        CreateFeedParams,
        RemovePostFromFeedParams,
        RemoveEachOthersPostsFromFeedsParams,
        AddEachOthersPostsToFeedsParams,
        IncrementCommentCountersParams,
    ],
    active_jobs_container: CosmosContainerAsync,
):
    try:
        logger.info(f"🎯 Creating background job for {action} action")

        # Rules-based ID generation to stop duplicate jobs creation
        logger.info(f"action == '{action}'")
        if action in {"add_post_to_feed", "remove_post_from_feed"}:
            id = params.post_id  # type: ignore
        elif action in {
            "add_each_others_posts_to_feeds",
            "remove_each_others_posts_from_feeds",
        }:
            id = gen_connection_id(params.person_id_1, params.person_id_2)  # type: ignore
        else:
            id = uuid4()
    except Exception as e:
        logger.error(f"❌ Failed to generate ID for background job", exc_info=e)

    try:
        # Job is added to the database and is available for reservation by workers
        job = Job(
            id=id,
            action=action,
            reserved_until=UTCDateTime.now(),
            params=params,
        )

        await active_jobs_container.create_model(model=job, model_class=Job)
        logger.info(f"✅ Background job {id} created")
        reserve()
    except CosmosHttpResponseError as e:
        if e.status_code == 409:  # Conflict status code
            logger.warning(f"⚠️ Background job {id} already exists, skipping creation")
        else:
            raise
    except Exception as e:
        logger.error(f"❌ Failed to create background job {id}", exc_info=e)
