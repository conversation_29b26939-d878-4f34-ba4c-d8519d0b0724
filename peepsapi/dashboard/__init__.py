"""Dashboard package for PeepsAPI.

This package contains modules for the admin dashboard, including
authentication monitoring, system status, and other administrative features.
"""

from typing import Optional

from fastapi import APIR<PERSON>er, <PERSON><PERSON>, Request
from fastapi.responses import HTMLResponse, RedirectResponse

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.azure_ad_service import (
    AZURE_AD_COOKIE_NAME,
    azure_ad_service,
)
from peepsapi.dashboard.routes import monitoring
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import AuthenticationError, ServerError
from peepsapi.utils.logging import get_logger
from peepsapi.utils.templates import render_template

# Configure logger
logger = get_logger(__name__)

# Create a router for all dashboard endpoints
router = APIRouter()

# Include the monitoring router
router.include_router(monitoring.router, include_in_schema=False)


# Add the login page route
@router.get("/login", response_class=HTMLResponse, include_in_schema=True)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def login_page():
    """Serve the login page with Azure AD and Passkey login options.

    Returns:
        HTMLResponse: The HTML login page
    """
    logger.info("Login page requested")

    try:
        template_content = render_template("login.html")
        if not template_content:
            # Fallback if template is not found
            logger.error("❌ Login template not found")
            return HTMLResponse(content="<h1>Login page not available</h1>")

        logger.info("Login page rendered successfully")
        return HTMLResponse(content=template_content)
    except Exception as e:
        logger.error(f"❌ Error rendering login page", extra={"error": str(e)})
        raise ServerError(
            message="Error rendering login page",
            error_code="LOGIN_PAGE_ERROR",
            details={"error": str(e)},
        )


# Add the main dashboard route directly to the router
@router.get("/", include_in_schema=True)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def dashboard(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Serve the main dashboard page or redirect to login if not authenticated."""
    # Log request information for debugging
    logger.info(
        "Dashboard request received",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    try:
        # Use the centralized authentication function
        is_authenticated = await auth_service.authenticate_request(
            request=request,
            session_token=session_token,
            azure_ad_token=azure_ad_token,
        )

        # If not authenticated, show the login page directly instead of redirecting
        if not is_authenticated:
            logger.warning(
                "User not authenticated, showing login page",
                request=request,
            )
            # Render the login template
            template_content = render_template("login.html", {})
            if not template_content:
                # Fallback if template is not found
                logger.error("Login template not found", request=request)
                return HTMLResponse(content="<h1>Login page not available</h1>")

            return HTMLResponse(content=template_content)

        # Get user information from request state
        user_name = getattr(request.state, "user_name", "Unknown User")
        user_email = getattr(request.state, "user_email", "<EMAIL>")
        auth_source = getattr(request.state, "auth_source", "unknown")

        # Create context for template rendering
        context = {
            "user_name": user_name,
            "user_email": user_email,
            "auth_source": auth_source,
        }

        # Render the template
        template_content = render_template("dashboard.html", context)
        if not template_content:
            # Fallback if template is not found
            logger.error("❌ Dashboard template not found", request=request)
            return HTMLResponse(content="<h1>Dashboard not available</h1>")

        logger.info(
            "Dashboard rendered successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": auth_source},
        )
        return HTMLResponse(content=template_content)
    except Exception as e:
        logger.error(
            f"Error rendering dashboard: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None)
            if hasattr(request, "state")
            else None,
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error rendering dashboard",
            error_code="DASHBOARD_ERROR",
            details={"error": str(e)},
        )


# Add a route for API documentation
@router.get("/api-docs", response_class=HTMLResponse, include_in_schema=False)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def api_docs(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Serve the API documentation page.

    This route requires authentication and is meant to be used within the dashboard.
    It renders the Swagger UI documentation.

    Args:
        request (Request): The FastAPI request object
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        HTMLResponse: The Swagger UI HTML
    """
    logger.info(
        "API documentation requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    try:
        # Use the centralized authentication function
        is_authenticated = await auth_service.authenticate_request(
            request=request,
            session_token=session_token,
            azure_ad_token=azure_ad_token,
        )

        # If not authenticated, show the login page
        if not is_authenticated:
            logger.warning(
                "User not authenticated, showing login page",
                request=request,
            )
            template_content = render_template("login.html", {})
            if not template_content:
                logger.error("Login template not found", request=request)
                return HTMLResponse(content="<h1>Login page not available</h1>")
            return HTMLResponse(content=template_content)

        # If authenticated, redirect to the Swagger UI
        logger.info(
            "User authenticated, redirecting to Swagger UI",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )
        return RedirectResponse(url="/docs")
    except Exception as e:
        logger.error(
            f"Error processing API docs request: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None)
            if hasattr(request, "state")
            else None,
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error processing API docs request",
            error_code="API_DOCS_ERROR",
            details={"error": str(e)},
        )


# Add a route for account management
@router.get("/accounts", response_class=HTMLResponse, include_in_schema=False)
@handle_exceptions(error_code_prefix="DASHBOARD")
async def accounts(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    """Serve the account management page.

    This route requires authentication and is meant to be used within the dashboard.
    It renders the account management interface with WebAuthn functionality.

    Args:
        request (Request): The FastAPI request object
        session_token (Optional[str]): The JWT session token
        azure_ad_token (Optional[str]): The Azure AD session token

    Returns:
        HTMLResponse: The account management HTML
    """
    logger.info(
        "Account management page requested",
        request=request,
        extra={"client_ip": request.client.host if request.client else "unknown"},
    )

    try:
        # Use the centralized authentication function
        is_authenticated = await auth_service.authenticate_request(
            request=request,
            session_token=session_token,
            azure_ad_token=azure_ad_token,
        )

        # If not authenticated, show the login page
        if not is_authenticated:
            logger.warning(
                "User not authenticated, showing login page",
                request=request,
            )
            template_content = render_template("login.html", {})
            if not template_content:
                logger.error("Login template not found", request=request)
                return HTMLResponse(content="<h1>Login page not available</h1>")
            return HTMLResponse(content=template_content)

        # Render the accounts template
        template_content = render_template("accounts.html")
        if not template_content:
            # Fallback if template is not found
            logger.error("❌ Accounts template not found", request=request)
            return HTMLResponse(
                content="<h1>Account management page not available</h1>"
            )

        logger.info(
            "Account management page rendered successfully",
            request=request,
            person_id=getattr(request.state, "person_id", None),
            extra={"auth_source": getattr(request.state, "auth_source", "unknown")},
        )
        return HTMLResponse(content=template_content)
    except Exception as e:
        logger.error(
            f"Error rendering account management page: {e}",
            request=request,
            person_id=getattr(request.state, "person_id", None)
            if hasattr(request, "state")
            else None,
            extra={"error": str(e)},
        )
        raise ServerError(
            message="Error rendering account management page",
            error_code="ACCOUNTS_PAGE_ERROR",
            details={"error": str(e)},
        )


# Export commonly used functions and classes
__all__ = [
    "router",
]
