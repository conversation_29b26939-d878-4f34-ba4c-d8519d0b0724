"""API models for AI components.

This module provides Pydantic models for AI API endpoints,
including user input processing and action responses.
"""

from typing import Any, Dict, Optional

from pydantic import Field

from peepsapi.models.base import BaseModelWithExtra


class UserInput(BaseModelWithExtra):
    """Model for user input to the AI processing endpoint."""

    text: str


class ActionResponse(BaseModelWithExtra):
    """Model for AI action response.

    This is the main response model that the UI will communicate with.
    It has a streamlined structure with only essential fields.

    Attributes:
        id (str): Unique identifier for the action
        success (bool): Whether the action was successful
        message (str): A message about the action status or result
        response (Dict): Contains intent and content specific to each intent type
    """

    id: Optional[str] = None
    success: bool
    message: str
    response: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Contains intent and content specific to each intent type",
    )
