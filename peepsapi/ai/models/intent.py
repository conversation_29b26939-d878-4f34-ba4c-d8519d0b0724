"""Intent models for AI components.

This module provides Pydantic models for intent parsing and handling,
including parsed intent structures and related data models.
"""

import uuid
from typing import Any, Dict, List

from pydantic import Field

from peepsapi.models.base import BaseModelWithExtra


class ParsedIntent(BaseModelWithExtra):
    """Data model for parsed intent.

    Attributes:
        intent (str): The type of action to perform
        parameters (dict): Parameters for the action
        context_needed (list): List of required context items
        data_needed (list): List of required data items
        action_id (str): Unique identifier for this action
    """

    intent: str
    parameters: Dict[str, Any]
    context_needed: List[str]
    data_needed: List[str] = Field(default_factory=list)
    action_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
