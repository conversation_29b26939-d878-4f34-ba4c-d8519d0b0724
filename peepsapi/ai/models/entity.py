"""Entity models for AI components.

This module provides models for entity validation, matching, and suggestion,
including confidence scoring and entity matching structures.
"""

from typing import Any, Dict, Optional
from uuid import UUID

from peepsapi.models.base import BaseModelWithExtra


class EntityMatch(BaseModelWithExtra):
    """Data model for entity matching results.

    Attributes:
        entity_id (UUID): The ID of the matched entity
        entity_type (str): The type of entity (person, community)
        name (str): The name of the matched entity
        confidence (float): The confidence score (0-1)
        original_query (str): The original query string
        additional_info (Dict[str, Any]): Additional entity information
    """

    entity_id: UUID
    entity_type: str
    name: str
    confidence: float
    original_query: str
    additional_info: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert the entity match to a dictionary.

        Returns:
            Dict[str, Any]: Dictionary representation of the entity match
        """
        result = {
            "entity_id": self.entity_id,
            "entity_type": self.entity_type,
            "name": self.name,
            "confidence": self.confidence,
            "original_query": self.original_query,
        }

        # Add additional info if present
        if self.additional_info:
            result.update(self.additional_info)

        return result

    def __str__(self) -> str:
        """Return string representation of the entity match.

        Returns:
            str: String representation
        """
        return f"{self.name} ({self.entity_type}, {self.confidence:.2f})"
