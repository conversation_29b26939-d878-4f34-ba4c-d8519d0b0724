"""Service for Azure OpenAI API interactions using Singleton pattern.

This module provides a singleton service for interacting with the Azure OpenAI API,
ensuring that only one client is created and reused throughout the application.
"""

import os
from typing import Optional

from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class OpenAIService:
    """Singleton class for Azure OpenAI API interactions.

    This class implements the Singleton pattern to ensure only one instance
    of the OpenAI client is created and reused throughout the application.
    """

    _instance: Optional["OpenAIService"] = None
    _client = None
    _initialized = False

    def __new__(cls):
        """Ensure only one instance of OpenAIService exists.

        Returns:
            OpenAIService: The singleton instance
        """
        if cls._instance is None:
            cls._instance = super(OpenAIService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the OpenAI client if not already initialized.

        This method is called after __new__ and initializes the client
        only once, even if multiple instances are requested.
        """
        # Only initialize once
        if not self._initialized:
            self._initialize_client()
            self.__class__._initialized = True

    def _initialize_client(self):
        """Initialize the OpenAI client with configuration from environment variables."""
        api_key = os.getenv("OPENAI_API_KEY")
        endpoint = os.getenv("OPENAI_ENDPOINT")
        api_version = os.getenv("OPENAI_API_VERSION")
        deployment = os.getenv("OPENAI_DEPLOYMENT")

        logger.info("🛠️ OpenAI Service Configuration:")
        logger.info(f"   Endpoint: {endpoint}")
        logger.info(f"   API Version: {api_version}")
        logger.info(f"   Deployment: {deployment}")
        logger.info(f"   API Key: {'*' * 20 if api_key else 'Not set'}")

        if not api_key or not endpoint:
            logger.error(
                "❌ OpenAI API key or endpoint not set in environment variables."
            )
            return

        try:
            from openai import AzureOpenAI

            self._client = AzureOpenAI(
                azure_endpoint=endpoint,
                api_key=api_key,
                api_version=api_version,
            )
            logger.info("✅ OpenAI client initialized successfully")

        except ImportError:
            logger.error("❌ OpenAI package not installed.")
        except Exception as e:
            logger.error(f"❌ Error initializing OpenAI client: {str(e)}")

    @property
    def client(self):
        """Get the OpenAI client.

        Returns:
            AzureOpenAI: The OpenAI client instance, or None if not initialized
        """
        return self._client

    def is_available(self):
        """Check if OpenAI is available.

        Returns:
            bool: True if the OpenAI client is initialized, False otherwise
        """
        return self._client is not None


# Create a singleton instance
_service = OpenAIService()


def is_available():
    """Check if OpenAI is available.

    Returns:
        bool: True if the OpenAI client is initialized, False otherwise
    """
    return _service.is_available()


def get_client():
    """Get the OpenAI client if available, otherwise raise an exception.

    Returns:
        AzureOpenAI: The OpenAI client instance

    Raises:
        RuntimeError: If the OpenAI client is not available
    """
    if not is_available():
        raise RuntimeError("OpenAI client is not available. Check logs for details.")
    return _service.client


def chat_completions_create(
    messages,
    model,
    max_tokens=2048,
    temperature=0.6,
    top_p=1,
    response_format={"type": "json_object"},
):
    """Create a chat completion using the Azure OpenAI client.

    Args:
        messages (list): A list of message dictionaries for the chat completion.
        model (str): The model to use for the chat completion.
        max_tokens (int, optional): The maximum number of tokens to generate. Defaults to 2048.
        temperature (float, optional): Sampling temperature. Defaults to 0.6.
        top_p (float, optional): Nucleus sampling probability. Defaults to 1.
        response_format (dict, optional): The format of the response. Defaults to {"type": "json_object"}.

    Returns:
        str: The content of the generated message.
    """
    if os.getenv("OPENAI_MOCK_ENABLED", "false").lower() == "true":
        logger.info("🧠 OpenAI Mock enabled")
        input = messages[1]["content"]
        input = input.split("Input: ")
        input = input[len(input) - 1]
        return input

    client = get_client()
    response = client.chat.completions.create(
        messages=messages,
        model=model,
        max_completion_tokens=max_tokens,
        temperature=temperature,
        top_p=top_p,
        response_format=response_format,
    )

    content = response.choices[0].message.content
    logger.info(f"🧠 OpenAI raw response: {content}")

    if hasattr(response, "usage"):
        logger.info(
            f"🧪 Token usage - Prompt: {response.usage.prompt_tokens}, "
            + f"Completion: {response.usage.completion_tokens}, "
            + f"Total: {response.usage.total_tokens}"
        )

    return content
