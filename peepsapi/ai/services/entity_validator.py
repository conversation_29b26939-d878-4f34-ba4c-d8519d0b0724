"""Entity validation and suggestion for AI actions.

This module provides functionality for validating and suggesting entities
based on user input with confidence scoring. It supports matching both people
and communities with proper error handling and confidence thresholds.
"""

# TODO Implement entity validation for events, posts
# TODO (Possibly) Use Cosmos DB Text Search method, better and faster

from difflib import SequenceMatcher
from typing import List, Optional

from peepsapi.ai.models.entity import EntityMatch
from peepsapi.crud.routes.communities import query_communities
from peepsapi.crud.services.connection_service import query_connections
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.logging import get_logger

connections_container = CosmosContainer("connections")
communities_container = CosmosContainer("communities")

logger = get_logger(__name__)

# Confidence thresholds
# HIGH_CONFIDENCE_THRESHOLD = 0.8  # Proceed automatically without confirmation
# MEDIUM_CONFIDENCE_THRESHOLD = 0.7  # Proceed with unsure tone in response
# LOW_CONFIDENCE_THRESHOLD = 0.5  # Minimum threshold to consider a match

# Entity types
PERSON_ENTITY = "person"
COMMUNITY_ENTITY = "community"


async def validate_entity(
    entity_name: str, data_needed: List, user_id: str
) -> Optional[EntityMatch]:
    """Validate that an entity exists and return the best match.

    Args:
        entity_name (str): The name of the entity to validate
        data_needed (List): List of data types needed for validation
        user_id (str): The ID of the user making the request

    Returns:
        Optional[EntityMatch]: The best entity match or None if no match found
    """
    try:
        # Initialize match variables
        best_person_match = None
        best_community_match = None

        # Use optimized methods for entity lookup
        if "people" in data_needed:
            best_person_match = await _find_best_person_match(entity_name, user_id)

        if "communities" in data_needed:
            best_community_match = await _find_best_community_match(
                entity_name, user_id
            )

        # TODO No need for checking other entities
        # Check if there are any other entity types requested besides 'people' and 'communities'
        other_entities = [
            item for item in data_needed if item not in ("people", "communities")
        ]
        if other_entities:
            logger.warning(f"Unknown entity types requested: {other_entities}")

        # TODO Just print findings if any, even if both
        # Return the first valid match found, prioritizing person over community
        return best_person_match or best_community_match

    except Exception as e:
        logger.error(f"Error validating '{entity_name}': {str(e)}")
        return None


async def _find_best_person_match(
    person_name: str, user_id: str
) -> Optional[EntityMatch]:
    """Find the best matching person from a list of people using fuzzy matching.

    This function performs fuzzy matching on person names with proper error handling.
    It returns the best match if it meets the minimum confidence threshold.

    Args:
        person_name (str): The name to match

    Returns:
        Optional[EntityMatch]: The best match or None if no match found or an error occurs
    """
    try:
        logger.info(f"person_id: {user_id}, query: {person_name}")
        connection_query_result = query_connections(
            person_id=user_id,  # connection owner person ID
            query=person_name,
            connections_container=connections_container,
        )

        if len(connection_query_result) == 0:
            logger.warning(f"No person match found for '{person_name}'")
            return None

        person_preview = connection_query_result[0].person_preview
        logger.debug("Person Preview: {person_preview}")
        # move outside of the for-loop
        #
        # Note: contains duplicate data in .name and .additional_info.full_name,
        # but we are keeping original logic for now for API/contract compatibility
        return EntityMatch(
            entity_id=person_preview.id,
            entity_type=PERSON_ENTITY,
            name=f"{person_preview.name} {person_preview.last_name}".strip(),
            confidence=1,  # 1 since we're using exact match
            original_query=person_name,
            additional_info={
                "full_name": f"{person_preview.name} {person_preview.last_name}".strip(),
                "current_role": person_preview.current_role,
                "current_company": person_preview.current_company,
            },
        )
    except Exception as e:
        logger.error(f"Error finding person match for '{person_name}': {str(e)}")
        return None


async def _find_best_community_match(
    community_name: str, user_id: str
) -> Optional[EntityMatch]:
    """Find the best matching community from a list of communities using fuzzy matching.

    This function performs exact and fuzzy matching on community names with proper error handling.
    It filters communities based on the user's access permissions and returns the best match
    if it meets the minimum confidence threshold.

    Args:
        community_name (str): The name to match
        user_id (str): The ID of the user making the request, used for filtering accessible communities

    Returns:
        Optional[EntityMatch]: The best match or None if no match found or an error occurs
    """
    try:
        # Get sanitized communities data
        community_query_result = query_communities(
            user_id, community_name, communities_container
        )

        if len(community_query_result["items"]) == 0:
            logger.warning(f"No community match found for '{community_name}'.")
            return None

        community = community_query_result["items"][0]
        return EntityMatch(
            entity_id=community.id,
            entity_type=COMMUNITY_ENTITY,
            name=community.name,
            confidence=community_query_result["confidence_scores"][0],
            original_query=community_name,
            additional_info={
                "description": community.description,
                "visibility": community.visibility,
            },
        )
    except Exception as e:
        logger.error(f"Error finding community match for '{community_name}': {str(e)}")
        return None


# NOT IN USE, WILL BE REMOVED WITH Full-text search cabapilities of CosmosDB
def _calculate_similarity(str1: str, str2: str) -> float:
    """Calculate similarity between two strings with special handling for names.

    This function uses a combination of exact matching, substring matching,
    and sequence matching to calculate a similarity score between 0 and 1.

    It applies special rules for:
    - Exact matches (score: 1.0)
    - Prefix matches for nicknames (e.g., "Bri" in "Brian")
    - Short string matches with length-based penalties
    - Substring matches with reduced confidence
    - Fuzzy matches using SequenceMatcher for typos and variations

    Args:
        str1 (str): First string (typically the query)
        str2 (str): Second string (typically the name to match against)

    Returns:
        float: Similarity score between 0 and 1, with higher values indicating better matches
    """
    # Handle empty strings
    if not str1 or not str2:
        return 0.0

    # Exact match
    if str1 == str2:
        return 1.0

    # Get lengths for later calculations
    len_str1 = len(str1)
    len_str2 = len(str2)

    # Handle very short input strings (likely not valid names)
    if len_str1 < 3:
        # For very short strings, require exact prefix match and penalize more
        if str2.startswith(str1):
            # Calculate a reduced score based on length difference
            # Short prefix matches should have lower confidence
            return 0.6 * (len_str1 / len_str2)
        else:
            # Not even a prefix match for a very short string
            return SequenceMatcher(None, str1, str2).ratio() * 0.5

    # TODO: handle `if len_str2 >= 3 and str1.startswith(str2)`
    # Check for nickname-like substring matches (e.g., "Bri" in "Brian")
    # Only consider it a strong match if it's a prefix and at least 3 chars
    if len_str1 >= 3 and str2.startswith(str1):
        # Calculate a more appropriate score for prefix matches
        # The longer the prefix relative to the full name, the higher the score
        prefix_ratio = len_str1 / len_str2
        if prefix_ratio > 0.7:  # If prefix is more than 70% of the name
            return 0.85 + (0.15 * prefix_ratio)  # Score between 0.85 and 1.0
        else:
            return 0.7 + (0.15 * prefix_ratio)  # Score between 0.7 and 0.85

    # Check for other substring matches, but with reduced confidence
    if str1 in str2:
        # Substring but not prefix - lower confidence
        return 0.6 * (len_str1 / len_str2)
    if str2 in str1:
        return 0.6 * (len_str2 / len_str1)

    # Use sequence matcher for more complex comparisons
    # This handles typos and other fuzzy matches
    return SequenceMatcher(None, str1, str2).ratio()
