"""Optimized intent parsing for user input.

This module provides optimized functionality for parsing user input to extract
intent, parameters, and required context.
"""

import json
import os

from peepsapi.ai.models.intent import ParsedIntent
from peepsapi.ai.services.openai_service import chat_completions_create
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

# TODO Implement inferences into flow- inferences: optional additional data types or signals
# that might be useful but are not explicitly requested
#  (e.g., "people" inferred from context or tone): list"


async def intent_parser(user_input: str) -> ParsedIntent:
    """Parse user input to extract intent, parameters, and required context.

    This function uses OpenAI to parse the user input and extract structured
    information about the intended action, with optimized prompts.

    Args:
        user_input (str): The user input to parse

    Returns:
        ParsedIntent: A structured representation of the user's intent
    """
    try:
        system_prompt = """
        You are an assistant that extracts structured actions from user commands and identifies what data
        is needed to fulfill those actions.

        AVAILABLE DATA TYPES:
        The following data types are available in our system. You need to specify which ones are needed
        for each request:

        1. `people` - Contains person data:
        - User profiles with name, surname, profile picture URL, current role, company, location
        - Contact information including emails and social links
        - Professional background and skills
        - USE FOR: Finding people, getting recipient information for messages, answering questions about specific people

        2. `communities` - Contains community data:
        - Community profiles with name, description, tags, member count
        - Community topics and focus areas
        - Public community information
        - USE FOR: Finding communities, answering questions about communities, community-related searches

        3. `conversations` - Contains conversation metadata:
        - Conversation participants and basic metadata
        - Recent conversation summaries
        - USE FOR: Questions about recent conversations, finding specific conversations

        4. `messages` - Contains message data:
        - Message content, sender, timestamp
        - Recent message history
        - USE FOR: Questions about message history, finding specific messages

        5. `posts` - Contains post data:
        - Post content, author, community association
        - Post topics and engagement metrics
        - USE FOR: Questions about posts, finding specific content, topic-based searches

        6. `comments` - Contains comment data:
        - Comment content, author, associated post
        - Comment threads and discussions
        - USE FOR: Questions about discussions, finding specific comments

        7. `events` - Contains event data:
        - Event titles, descriptions, dates, locations
        - Event organizers and categories
        - USE FOR: Questions about events, finding upcoming events

        IMPORTANT: Only request the specific data types that are needed to fulfill the user's request."""

        user_prompt = """
        Extract structured actions from the user command and identify what data is needed to fulfill the request.

        STEP 1:

        IDENTIFY THE INTENT
        Focus on these specific intents:
        - "search_network" for searching people or communities
        - "chat" for general conversation and questions
        - "send_message" for direct messages to individuals

        STEP 2:

        EXTRACT PARAMETERS

        1. For search_network:
        - Extract "query" parameter for the search term
        - Extract any "filters" that might narrow the search

        2. For send_message:
        - Extract "recipient" parameter for the message recipient
        - Extract "content" parameter for the message content

        3. For chat:
        - Extract "content" parameter for the query or conversation topic

        STEP 3:

        IDENTIFY MISSING CONTEXT (CRITICAL STEP)
        1. For search_network:
        - If the search query is missing or empty (e.g., just "Search for" with no specific query),
            you MUST add "search_query" to context_needed
        - The success of the action depends on having a valid search query

        2. For send_message:
        - If no recipient is specified, add "recipient" to context_needed
        - If the content is missing, add "message_content" to context_needed

        STEP 4:

        DETERMINE DATA NEEDED
        This is the most important step. Based on the intent and parameters, determine what data types are needed:

        1. For search_network:
        - Be specific about what data is needed based on the query
        - Only add data types when there's a valid query to search with

        2. For send_message:
        - Analyze the recipient to determine if it's a person or a community:
            - If the recipient appears to be a person, add "people" to data_needed
            - If the recipient appears to be a community, add "communities" to data_needed
            - If unsure, include both "people" and "communities" in data_needed

        3. For chat:
        - Carefully analyze what the chat is about and only request relevant data types
        - If asking about people, add "people" to data_needed
        - If asking about communities, add "communities" to data_needed
        - If asking about messages, chatting add messages data type
        - If asking about posts, comments, or events, add those specific data types
        - For general questions not related to any specific data, leave data_needed empty

        EXAMPLES:

        Example 1:
        Input: "Search for people who know about machine learning"
        Output:
        {
        "intent": "search_network",
        "parameters": {"query": "machine learning"},
        "context_needed": [],
        "data_needed": ["people"]
        }

        Example 2:
        Input: "Send a message to Ilya saying I found an iOS developer"
        Output:
        {
        "intent": "send_message",
        "parameters": {"recipient": "Ilya", "content": "I found an iOS developer"},
        "context_needed": [],
        "data_needed": ["people"]
        }

        Example 3:
        Input: "Post in the Product HK community about the upcoming hackathon"
        Output:
        {
        "intent": "send_message",
        "parameters": {"recipient": "Product HK", "content": "Information about the upcoming hackathon"},
        "context_needed": [],
        "data_needed": ["communities"]
        }

        Example 4:
        Input: "What are the upcoming events in the AI community?"
        Output:
        {
        "intent": "chat",
        "parameters": {"content": "What are the upcoming events in the AI community?"},
        "context_needed": [],
        "data_needed": ["events", "communities"]
        }

        IMPORTANT RULES:
        1. Always check if required parameters are missing:
        - For search_network, a query is required
        - For send_message, both recipient and content are required
        - If any required parameter is missing, add it to context_needed

        2. If the user's intent is unclear, default to intent = "chat" and extract the message content as-is.

        3. Do not hallucinate parameters - if information is not provided, mark it as missing in context_needed.

        Return a JSON object with:
        - intent: the type of action ("search_network", "chat", "send_message"): str
        - parameters: all needed data (e.g., query, filters, recipient, content): dict
        - context_needed: any external context that would be needed to execute: list
        - data_needed: list of specific data types needed from the available options
        (people, communities, messages, posts, comments, events)

        Input: """

        prompt = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt + user_input},
        ]

        model_name = os.getenv("OPENAI_MODEL_NAME")
        model_temp = os.getenv("OPENAI_MODEL_TEMP")
        content = chat_completions_create(
            messages=prompt,
            model=model_name,
            max_tokens=2048,
            temperature=model_temp,
            top_p=1,
        )

        try:
            # Parse JSON response
            parsed = json.loads(content)

            # TODO Make sure parsed has correct format, reinforce prompt

            return ParsedIntent(**parsed)

        except Exception as error:
            logger.error(f"Error parsing intent response: {str(error)}")
            raise error

    except Exception as error:
        logger.error(f"Azure GPT request failed: {str(error)}")
        raise error
