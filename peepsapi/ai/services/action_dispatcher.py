"""Action dispatcher for AI components.

This module provides functionality for dispatching actions based on parsed intents,
with execution of actions.
"""

from typing import Any, Dict, Optional

from peepsapi.ai.models.intent import ParsedIntent
from peepsapi.ai.services.entity_validator import validate_entity
from peepsapi.ai.services.intent_handlers.chat import handle_chat
from peepsapi.ai.services.intent_handlers.search_network import handle_search_network
from peepsapi.ai.services.intent_handlers.send_message import handle_send_message
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


def _create_error_response(
    message: str, action_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized error response.

    Args:
        message (str): Error message
        action_id (Optional[str]): Action ID to include in the response

    Returns:
        Dict[str, Any]: Standardized error response
    """
    return {
        "success": False,
        "message": message,
        "response": None,
        "id": action_id,
    }


# TODO Is there better way handle id creation?
def _sanitize_action_result_response_format(result, parsed_intent):
    # Ensure result is a valid dictionary
    if not isinstance(result, dict):
        logger.error("Handler result is not a dictionary. Got: %r", result)
        result = {
            "success": False,
            "message": "Invalid handler result",
            "response": None,
        }

    # Set id
    result["id"] = parsed_intent.action_id

    # If response is present but doesn't have intent, add it
    if result.get("response") and "intent" not in result["response"]:
        result["response"]["intent"] = parsed_intent.intent

    return result


async def dispatch_action(parsed_intent: ParsedIntent, user_id: str) -> Dict[str, Any]:
    """Dispatch an action based on the parsed intent.

    This function routes the parsed intent to the appropriate handler
    based on the intent type and parameters, with execution.

    Args:
        parsed_intent (ParsedIntent): The parsed intent object containing intent type and parameters

    Returns:
        Dict[str, Any]: A response from the handler containing the result of the action
    """
    if not parsed_intent:
        logger.error("Invalid parsed intent")
        return _create_error_response("Invalid parsed intent")

    if parsed_intent.context_needed:
        logger.error(f"Missing required context: {parsed_intent.context_needed}")
        return _create_error_response(
            f"Missing required context: {parsed_intent.context_needed}",
            parsed_intent.action_id,
        )

    # Extract data from parsed intent
    intent = parsed_intent.intent
    parameters = parsed_intent.parameters
    data_needed = parsed_intent.data_needed
    action_id = parsed_intent.action_id

    # Handle chat intent
    if intent == "chat":
        logger.info("Proceeding with chat intent...")

        content = parameters.get("content", "")

        result = await handle_chat(content, data_needed, user_id)

        # Add action_id to the result
        return _sanitize_action_result_response_format(result, parsed_intent)

    # Handle search_network intent
    if intent == "search_network":
        logger.info("Proceeding with search_network...")
        # Basic parameter validation
        if "query" not in parameters or not parameters["query"]:
            logger.warning("Search query is required but not provided")
            return _create_error_response(
                "Missing required parameter: query", action_id
            )

        query = parameters["query"]
        filters = parameters.get("filters")

        result = await handle_search_network(query, data_needed, user_id, filters)

        # Add action_id to the result
        return _sanitize_action_result_response_format(result, parsed_intent)

    # Handle send_message intent with entity validation
    if intent == "send_message":
        # Basic parameter validation
        if "recipient" not in parameters:
            logger.warning("Recipient is required but not provided")
            return _create_error_response(
                "Missing required parameter: recipient", action_id
            )

        # The desired recipient name as provided by the user input
        recipient_input_name = parameters.get("recipient")
        message_content = parameters.get("content", "")

        # If content is empty, provide a default message
        if not message_content:
            message_content = f"Hi, {recipient_input_name}."
            logger.info(
                f"Message content not provided, using default greeting: '{message_content}'"
            )

        # Validate entity (person or community)
        recipient_match = await validate_entity(
            recipient_input_name, data_needed, user_id
        )

        if not recipient_match:
            logger.warning(f"No match found for '{recipient_input_name}'")
            return _create_error_response(
                f"No match found for '{recipient_input_name}'", action_id
            )

        # Execute the send_message action
        result = await handle_send_message(
            recipient=recipient_match.name,
            message_content=message_content,
            recipient_id=recipient_match.entity_id,
            recipient_type=recipient_match.entity_type,
        )

        # Add action_id to the result
        return _sanitize_action_result_response_format(result, parsed_intent)

    # Unknown intent
    logger.error(f"Unknown intent: {intent}")
    return _create_error_response(f"Unknown intent: {intent}", action_id)
