"""Handler for sending messages to users and communities.

This module provides functionality for sending direct messages to users and communities.
"""

from typing import Any, Dict

from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def handle_send_message(
    recipient: str, message_content: str, recipient_id: str, recipient_type: str
) -> Dict[str, Any]:
    """Handle sending a message to a user or community.

    Args:
        recipient (str): The name of the recipient
        message_content (str): The content of the message
        recipient_id (str): The ID of the recipient if already validated
        recipient_type (str): Type of recipient (person or community), defaults to person

    Returns:
        Dict[str, Any]: A response containing the result of the operation
    """
    try:
        # Log sending action with actual recipient name
        logger.info(f"Preparing message to person {recipient}: '{message_content}'")

        # Return success response
        return {
            "success": True,
            "message": f"Message prepared to be sent to {recipient}",
            "response": {
                "intent": "send_message",
                "content": {
                    "recipient": {
                        "id": recipient_id,
                        "name": recipient,
                        "type": recipient_type,
                    },
                    "message_content": message_content,
                },
            },
        }

    except Exception as e:
        logger.error(f"Error sending message: {str(e)}")
        return {
            "success": False,
            "message": f"Error sending message: {str(e)}",
            "response": None,
        }
