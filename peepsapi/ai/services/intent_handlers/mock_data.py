from typing import Any, Dict, List

from peepsapi.crud.models.base import PersonPreview
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.picture_service import build_picture_url_by_id
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def mock_data(user_id: str, data_needed: List[str]) -> Dict[str, Any]:
    """
    Retrieve selective data based on the user ID and data types needed.

    This function fetches data from various sources based on the data types
    specified in data_needed. Currently returns mock data for demonstration purposes.

    Args:
        user_id (str): The ID of the user making the request
        data_needed (List[str]): List of data types to retrieve (people, communities, etc.)

    Returns:
        Dict[str, Any]: A dictionary containing the requested data types
    """
    logger.info(f"Retrieving selective data for user {user_id}: {data_needed}")

    # Initialize the context data dictionary
    context_data = {}

    # Add user data
    context_data["user"] = {
        "id": user_id,
        "name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "current_role": "Software Engineer",
        "current_company": "Tech Company",
        "location": "San Francisco, CA",
        "profile_pic": build_picture_url_by_id(parent_class=Person, parent_id=user_id),
    }

    # Add people data if requested
    if "people" in data_needed:
        context_data["people"] = [
            {
                "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
                "name": "Alice",
                "last_name": "Smith",
                "current_role": "Product Manager",
                "current_company": "Product Co",
                "location": "New York, NY",
                "profile_pic": build_picture_url_by_id(
                    parent_class=Person,
                    parent_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
                ),
            },
            {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "name": "Bob",
                "last_name": "Johnson",
                "current_role": "Designer",
                "current_company": "Design Studio",
                "location": "Los Angeles, CA",
                "profile_pic": build_picture_url_by_id(
                    parent_class=Person,
                    parent_id="550e8400-e29b-41d4-a716-446655440000",
                ),
            },
        ]

    # Add communities data if requested
    if "communities" in data_needed:
        context_data["communities"] = [
            {
                "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
                "name": "Product HK",
                "description": "Mock community for sharing and discovering products",
                "member_count": 42,
                "visibility": "public",
                "profile_picture_url": "https://example.com/community1.jpg",
            },
            {
                "id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8",
                "name": "Tech Enthusiasts",
                "description": "Mock community for tech enthusiasts to discuss the latest trends",
                "member_count": 128,
                "visibility": "public",
                "profile_picture_url": "https://example.com/community2.jpg",
            },
        ]

    # Add posts data if requested
    if "posts" in data_needed:
        context_data["posts"] = [
            {
                "id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8",
                "content": "Just launched our new product!",
                "author": PersonPreview(
                    id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
                    name="Alice Smith",
                    profile_pic_thumb=build_picture_url_by_id(
                        parent_class=Person,
                        parent_id="6ba7b812-9dad-11d1-80b4-00c04fd430c8",
                    ),
                ).model_dump(),
                "community_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
                "created_at": "2023-05-15T10:30:00Z",
            }
        ]

    # Add events data if requested
    if "events" in data_needed:
        context_data["events"] = [
            {
                "id": "6ba7b813-9dad-11d1-80b4-00c04fd430c8",
                "title": "Tech Meetup",
                "description": "Monthly meetup for tech enthusiasts",
                "location": "San Francisco, CA",
                "start_time": "2023-06-01T18:00:00Z",
                "end_time": "2023-06-01T20:00:00Z",
                "community_id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8",
            }
        ]

    # Add messages data if requested
    if "messages" in data_needed:
        context_data["messages"] = [
            {
                "id": "6ba7b814-9dad-11d1-80b4-00c04fd430c8",
                "content": "Hey, how's it going?",
                "sender_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
                "recipient_id": user_id,
                "created_at": "2023-05-14T15:45:00Z",
            }
        ]

    # Add comments data if requested
    if "comments" in data_needed:
        context_data["comments"] = [
            {
                "id": "6ba7b815-9dad-11d1-80b4-00c04fd430c8",
                "content": "Great product!",
                "author": PersonPreview(
                    id="550e8400-e29b-41d4-a716-446655440000",
                    name="Bob Johnson",
                    profile_pic_thumb=build_picture_url_by_id(
                        parent_class=Person,
                        parent_id="6ba7b815-9dad-11d1-80b4-00c04fd430c8",
                    ),
                ).model_dump(),
                "target_id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8",
                "target_type": "post",
                "created_at": "2023-05-15T11:15:00Z",
            }
        ]

    logger.info(f"Retrieved stab data for types: {', '.join(context_data.keys())}")
    return context_data
