"""Handler for searching the network using AI with context-aware capabilities.

This module provides functionality for searching across various data types
(people, communities, posts, comments, events, conversations)
using AI with the database as context. It leverages OpenAI's language models
to perform semantic search and return relevant results based on the user's query.

The search process:
1. Retrieves relevant data from the database based on the requested data types
2. Constructs a prompt with the search query, filters, and database context
3. Uses OpenAI to analyze the data and find relevant matches
4. Returns structured results with relevance explanations
"""

import json
import os
from typing import Any, Dict, List

from peepsapi.ai.services.openai_service import chat_completions_create
from peepsapi.crud.models.base import PersonPreview
from peepsapi.crud.models.post import PostsResponse
from peepsapi.crud.services.connection_service import get_connections_page
from peepsapi.crud.services.feed_service import get_feed_page
from peepsapi.services.cosmos_containers import (
    get_connections_container,
    get_feeds_container,
    get_people_container,
    get_posts_container,
)
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def get_multiple_connections_pages(
    user_uuid: str,
    max_items: int,
    sub_query: str = "c.status = @status",
    sub_query_parameters: List[dict] = None,
) -> List[Any]:
    """
    Helper function to iterate through multiple pages of connections.

    Args:
        user_uuid (str): The ID of the user whose connections to retrieve
        max_items (int): Maximum number of items to retrieve
        sub_query (str): The sub-query to filter connections
        sub_query_parameters (List[dict]): Parameters for the sub-query

    Returns:
        List[Any]: List of all connections retrieved across pages
    """
    if sub_query_parameters is None:
        sub_query_parameters = [{"name": "@status", "value": "accepted"}]

    connections_container = get_connections_container()
    all_connections = []
    next_page_token = None

    while len(all_connections) < max_items:
        try:
            connections_response = get_connections_page(
                owner_person_id=user_uuid,
                sub_query=sub_query,
                sub_query_parameters=sub_query_parameters,
                next_page_token=next_page_token,
                connections_container=connections_container,
            )

            # Add connections from this page
            all_connections.extend(connections_response.items)

            # Check if we have more pages
            if connections_response.next_page is None:
                break

            next_page_token = connections_response.next_page

            # Stop if we've reached our limit
            if len(all_connections) >= max_items:
                break

        except Exception as e:
            logger.error(f"Error retrieving connections page: {str(e)}")
            break

    # Truncate to max_items if we exceeded it
    return all_connections[:max_items]


async def get_multiple_feed_pages(user_uuid: str, max_items: int) -> PostsResponse:
    """
    Helper function to iterate through multiple pages of feed data.

    Args:
        user_uuid (str): The ID of the user whose feed to retrieve
        max_items (int): Maximum number of posts to retrieve

    Returns:
        PostsResponse: Response containing posts and people data
    """
    feeds_container = get_feeds_container()
    posts_container = get_posts_container()
    people_container = get_people_container()

    all_posts = []
    all_people = []
    people_map = {}
    next_page_token = None
    final_next_page = None

    while len(all_posts) < max_items:
        try:
            posts_response = await get_feed_page(
                owner_person_id=user_uuid,
                next_page_token=next_page_token,
                feeds_container=feeds_container,
                posts_container=posts_container,
                people_container=people_container,
                get_posts=True,
            )

            # Add posts from this page
            all_posts.extend(posts_response.items)

            # Add people from this page to the map (to avoid duplicates)
            for person in posts_response.people:
                people_map[str(person.id)] = person

            # Check if we have more pages
            if posts_response.next_page is None:
                final_next_page = None
                break

            next_page_token = posts_response.next_page

            # Stop if we've reached our limit
            if len(all_posts) >= max_items:
                # If we stopped due to reaching max_items, preserve next_page for continuation
                final_next_page = posts_response.next_page
                break

        except Exception as e:
            logger.error(f"Error retrieving feed page: {str(e)}")
            break

    # Convert people map back to list
    all_people = list(people_map.values())

    # Truncate to max_items if we exceeded it
    return PostsResponse(
        people=all_people, items=all_posts[:max_items], next_page=final_next_page
    )


async def get_context_data(user_uuid: str, data_needed: List[str]) -> Dict[str, Any]:
    """
    Retrieve selective data based on the user ID and data types needed.

    This function fetches data from various sources based on the data types
    specified in data_needed. For "people" and "posts", it retrieves real data
    from the database. For other types, it returns mock data.

    Args:
        user_id (str): The ID of the user making the request
        data_needed (List[str]): List of data types to retrieve (people, communities, etc.)

    Returns:
        Dict[str, Any]: A dictionary containing the requested data types
    """
    logger.info(f"Retrieving selective data for user {user_uuid}: {data_needed}")

    # Initialize the context data dictionary
    context_data = {}

    # Add user data (mock for now)
    context_data["user"] = {
        "id": str(user_uuid),
        "name": "John",
        "last_name": "Doe",
        "current_role": "Software Engineer",
        "current_company": "Tech Company",
        "location": "San Francisco, CA",
        "profile_pic_thumb": "https://example.com/profile.jpg",
    }

    # Add people data if requested - REAL DATA
    if "people" in data_needed:
        try:
            max_connections = int(os.getenv("OPENAI_CONTEXT_CONNECTIONS_AMOUNT", 20))

            # Get user's connections using the helper function
            all_connections = await get_multiple_connections_pages(
                user_uuid=user_uuid, max_items=max_connections
            )

            # Transform connections to the expected format
            context_data["people"] = []
            for connection in all_connections:
                context_data["people"].append(connection.person_preview.model_dump())

            logger.info(f"Retrieved {len(context_data['people'])} real connections")

        except Exception as e:
            logger.error(f"Error retrieving connections: {str(e)}")
            raise

    # Add communities data if requested - MOCK DATA
    if "communities" in data_needed:
        context_data["communities"] = [
            {
                "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
                "name": "Product HK",
                "description": "Mock community for sharing and discovering products",
                "member_count": 42,
                "visibility": "public",
                "profile_pic_thumb": "https://example.com/community1.jpg",
            },
            {
                "id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8",
                "name": "Tech Enthusiasts",
                "description": "Mock community for tech enthusiasts to discuss the latest trends",
                "member_count": 128,
                "visibility": "public",
                "profile_pic_thumb": "https://example.com/community2.jpg",
            },
        ]

    # Add posts data if requested - REAL DATA
    if "posts" in data_needed:
        try:
            max_posts = int(os.getenv("OPENAI_CONTEXT_POSTS_AMOUNT", 20))

            # Get user's feed posts using the helper function
            feed_data = await get_multiple_feed_pages(
                user_uuid=user_uuid, max_items=max_posts
            )

            # Transform posts to the expected format
            context_data["posts"] = []

            # Create a mapping from person_id to person data for quick lookup
            people_map = {str(person.id): person for person in feed_data.people}

            for post in feed_data.items:
                # Find the author from the people mapping
                author_id = str(post.author_person_id)
                author = people_map.get(author_id)

                if author:
                    author_data = {
                        "id": str(author.id),
                        "name": f"{author.name} {author.last_name}".strip(),
                        "profile_pic_thumb": author.profile_pic_thumb,
                    }
                else:
                    # Fallback if author not found in people list
                    author_data = {
                        "id": author_id,
                        "name": "Unknown Author",
                        "profile_pic_thumb": "",
                    }

                context_data["posts"].append(
                    {
                        "id": str(post.id),
                        "content": post.content,
                        "author": author_data,
                        "community_id": str(post.community_id)
                        if post.community_id
                        else None,
                        "created_at": post.created_at.isoformat()
                        if post.created_at
                        else None,
                    }
                )

            logger.info(f"Retrieved {len(context_data['posts'])} real posts")

        except Exception as e:
            logger.error(f"Error retrieving posts: {str(e)}")
            raise

    # Add events data if requested - MOCK DATA
    if "events" in data_needed:
        context_data["events"] = [
            {
                "id": "6ba7b813-9dad-11d1-80b4-00c04fd430c8",
                "title": "Tech Meetup",
                "description": "Monthly meetup for tech enthusiasts",
                "location": "San Francisco, CA",
                "start_time": "2023-06-01T18:00:00Z",
                "end_time": "2023-06-01T20:00:00Z",
                "community_id": "6ba7b811-9dad-11d1-80b4-00c04fd430c8",
            }
        ]

    # Add messages data if requested - MOCK DATA
    if "messages" in data_needed:
        context_data["messages"] = [
            {
                "id": "6ba7b814-9dad-11d1-80b4-00c04fd430c8",
                "content": "Hey, how's it going?",
                "sender_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
                "recipient_id": str(user_uuid),
                "created_at": "2023-05-14T15:45:00Z",
            }
        ]

    # Add comments data if requested - MOCK DATA
    if "comments" in data_needed:
        context_data["comments"] = [
            {
                "id": "6ba7b815-9dad-11d1-80b4-00c04fd430c8",
                "content": "Great product!",
                "author": PersonPreview(
                    id="550e8400-e29b-41d4-a716-************",
                    name="Bob",
                    last_name="Johnson",
                    profile_pic_thumb="https://example.com/bob.jpg",
                ).model_dump(),
                "target_id": "6ba7b812-9dad-11d1-80b4-00c04fd430c8",
                "target_type": "post",
                "created_at": "2023-05-15T11:15:00Z",
            }
        ]

    logger.info(f"Retrieved context data for types: {', '.join(context_data.keys())}")
    return context_data


async def handle_search_network(
    query: str, data_needed: List[str], user_id: str, filters: str = None
) -> Dict[str, Any]:
    """Handle searching the network for various entities using AI.

    Args:
        query (str): The search query
        data_needed (List[str]): List of data types to search (people, communities, posts, etc.)
        user_id (str): The ID of the user making the request
        filters (str, optional): Additional filters for the search

    Returns:
        Dict[str, Any]: A response containing the search results and metadata
    """
    try:
        # Log search action
        if filters:
            logger.info(f"Searching network for '{query}' with filters: {filters}")
        else:
            logger.info(f"Searching network for '{query}'")

        # If data_needed is not provided, default to people and communities
        if not data_needed:
            # TODO: `people` --> `connections`
            data_needed = ["people", "communities"]
            logger.info(
                "No data_needed specified, defaulting to people and communities"
            )
        else:
            logger.info(f"Searching in data types: {', '.join(data_needed)}")

        # Get selective context based on data_needed
        context_data = await get_context_data(user_id, data_needed)
        logger.info(
            f"Retrieved selective context data with keys: {', '.join(context_data.keys())}"
        )

        # Use AI to search the network with the database as context
        search_results = await ai_search_network(query, context_data, filters)

        # Calculate total results across all data types
        total_results = sum(
            len(search_results.get(data_type, [])) for data_type in search_results
        )

        return {
            "success": True,
            "message": f"Found {total_results} results for '{query}'",
            "response": {
                "intent": "search_network",
                "content": {
                    "results": search_results,
                    "query": query,
                    "filters": filters,
                    "data_types_searched": data_needed,
                },
            },
        }

    except Exception as e:
        logger.error(f"Error searching network: {str(e)}")

        return {
            "success": False,
            "message": f"Error searching network: {str(e)}",
            "response": None,
        }


async def ai_search_network(
    query: str,
    context_data: Dict[str, Any],
    filters: str = None,
) -> Dict[str, Any]:
    """Search the network using AI with the database as context.

    Args:
        query (str): The search query
        context_data (Dict[str, Any]): The context data containing various data types
        filters (str, optional): Additional filters for the search

    Returns:
        Dict[str, Any]: A dictionary with search results for each requested data type
    """
    system_prompt = """
    You are a search assistant for a professional network. Your task is to search through the provided database
    to find the most relevant matches for the user's query.

    You will be given a database containing various types of data, and a search query.
    You should return the most relevant results based on the query and any filters provided.

    For each data type, consider the following:
    - People: name, role, company, location, and other attributes
    - Communities: name, description, tags, and location
    - Posts: content, author, tags
    - Comments: content, associated post
    - Events: title, description, location, time, organizer
    - Conversations: participants, recent messages

    Return your results as a JSON object with arrays for each data type that was provided in the database.
    Each result object should include: id, name/title, and description.

    Only include results that are truly relevant to the query.
    If there are no relevant matches for a data type, return an empty array for that type.
    """

    user_prompt = f"""
    Search Query: {query}
    {f"Filters: {filters}" if filters else ""}

    Database:
    {json.dumps(context_data, indent=2)}

    Return only the JSON result with arrays for each data type that was provided in the database.
    For example, if the database contains "people" and "posts", your response should be:
    {{
        "people": [
            {{
                "id": "person_id",
                "name": "Person Name",
                "description": "Short description of why this person is relevant"
            }}
        ],
        "posts": [
            {{
                "id": "post_id",
                "title": "Post excerpt or summary",
                "description": "Short description of why this post is relevant"
            }}
        ]
    }}

    If a data type has no relevant results, include it with an empty array.
    """

    prompt = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]

    if os.getenv("OPENAI_MOCK_ENABLED", "false").lower() == "true":
        logger.info("Search network intent mock.")
        mock_answer = {
            "people": [
                {
                    "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
                    "name": "Mock Person 1",
                    "description": "This is a mock person result for search_network intent",
                },
                {
                    "id": "550e8400-e29b-41d4-a716-************",
                    "name": "Mock Person 2",
                    "description": "Another mock person result for search_network intent",
                },
            ],
            "posts": [
                {
                    "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
                    "title": "Mock Post Title",
                    "description": "This is a mock post result for search_network intent",
                }
            ],
            "communities": [
                {
                    "id": "7c9e6679-7425-40de-944b-e07fc1f90ae7",
                    "name": "Mock Community",
                    "description": "This is a mock community result for search_network intent",
                }
            ],
            "comments": [],
            "events": [
                {
                    "id": "a8098c1a-f86e-11da-bd1a-00112444be1e",
                    "title": "Mock Event Title",
                    "description": "This is a mock event result for search_network intent",
                }
            ],
            "messages": [],
        }
        return mock_answer

    model_name = os.getenv("OPENAI_MODEL_NAME")
    model_temp = os.getenv("OPENAI_MODEL_TEMP")
    content = chat_completions_create(
        messages=prompt,
        model=model_name,
        max_tokens=4096,
        temperature=model_temp,
        top_p=1,
    )

    logger.info(f"OpenAI search response: {content}")

    # Parse the JSON response
    try:
        parsed_content = json.loads(content)
        return parsed_content
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON response: {str(e)}")
        raise
