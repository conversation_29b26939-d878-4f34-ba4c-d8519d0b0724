"""Sophisticated handling of user chat inputs with AI.

This module provides handlers for processing chat messages using OpenAI
to generate contextually relevant responses based on user data.
"""

import json
import os
from typing import Any, Dict, List

from peepsapi.ai.services.intent_handlers.mock_data import mock_data
from peepsapi.ai.services.openai_service import chat_completions_create
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def format_system_prompt(context: Dict[str, Any]) -> str:
    """Format a system prompt based on user context.

    This function creates a system prompt for the AI assistant based on whatever
    context data is provided. It handles any type of context data that might be
    available in the database.

    Args:
        context (Dict[str, Any]): The user context data, which can include any tables
                                 from the database (people, communities, events, etc.)

    Returns:
        str: A formatted system prompt
    """
    system_prompt = (
        """You are an AI assistant for Peeps, a professional networking platform."""
    )

    # Extract user information
    user = context.get("user", {})
    if user:
        user_name = f"{user.get('name', '')} {user.get('last_name', '')}".strip()
        user_role = user.get("current_role", "")
        user_company = user.get("current_company", "")

        system_prompt += f"\nYou are assisting {user_name}"

        if user_role and user_company:
            system_prompt += f", who works as a {user_role} at {user_company}"
        elif user_role:
            system_prompt += f", who works as a {user_role}"
        elif user_company:
            system_prompt += f", who works at {user_company}"

        system_prompt += "."

    system_prompt += "\n\n"

    # Explain the context data that's available
    system_prompt += "You have access to the following context data:\n"

    # List all available context types
    for key, value in context.items():
        if key != "user" and value:  # Skip user
            system_prompt += f"\n- {key}:"
            if isinstance(value, list):
                for item in value:
                    system_prompt += f"\n  - {json.dumps(item, indent=2)}"
            else:
                system_prompt += f"\n  {json.dumps(value, indent=2)}"

    if len(context) == 1 and "user" in context:
        system_prompt += "\nNo additional context data available"

    system_prompt += "\n\n"

    # Add guidelines for the assistant
    system_prompt += """
    You are a helpful, professional assistant that supports users in navigating their professional network.

    Your goals are:

    Be professional, concise, and helpful in your communication.
    Use only the information available in the context (people, communities, posts, events, etc.).
    Do not fabricate or assume details not present in the provided context.
    When referencing an entity (person, community, post, etc.), include its reference using this format: [Ref:Type:id]
    If you make soft inferences (connections not explicitly stated but likely), list them in the soft_inferences array.
    Clearly describe the rationale and indicate uncertainty.
    Suggest additional actions or directions the user may find useful via the suggested_explorations array.
    These should be relevant, personalized, and grounded in the user's network or behaviors.

    Always return a JSON object with the following structure:

    {
    "response": "Main reply here with embedded [Ref:...] references.",
    "references": ["[Ref:...]", ...],
    "soft_inferences": [
        {
        "description": "Describe the inferred insight.",
        "relevance_score": 0.xx,
        "note": "Explain how this was inferred from context."
        }
    ],
    "suggested_explorations": [
        {
        "title": "Action label or suggestion",
        "reason": "Why this might be relevant",
        "action": "action_type (e.g., view_community, view_profile, start_chat)",
        "target_id": "Entity:identifier"
        }
    ]
    }
    """

    return system_prompt


async def generate_ai_response(content: str, context_data: Dict[str, Any] = {}) -> str:
    """Generate an AI response using OpenAI.

    Args:
        content (str): The user's message
        context (Dict[str, Any]): The user context data

    Returns:
        str: The AI-generated response
    """
    try:
        # Format system prompt based on context
        system_prompt = await format_system_prompt(context_data)

        # Create the messages for the chat completion
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": "User input: " + content},
        ]

        if os.getenv("OPENAI_MOCK_ENABLED", "false").lower() == "true":
            logger.info("chat intent mock.")
            mock_answer = """{
                "response": "This is intent handler mock response for chat. \
Referring to [Ref:person:cafebabe-0000-4000-8000-0000deadbeef]",
                "references": ["[Ref:person:cafebabe-0000-4000-8000-0000deadbeef]"],
                "soft_inferences": [
                    {
                        "description": "Mock inference description",
                        "relevance_score": 0.75,
                        "note": "Explanation of the mock inference"
                    }
                ],
                "suggested_explorations": [
                    {
                        "title": "Mock suggestion",
                        "reason": "Mock reason",
                        "action": "view_community",
                        "target_id": "community:deadbeef-0000-4000-8000-0000cafebabe"
                    }
                ]
            }"""
            return mock_answer

        model_name = os.getenv("OPENAI_MODEL_NAME")
        model_temp = os.getenv("OPENAI_MODEL_TEMP")
        content = chat_completions_create(
            model=model_name,
            messages=messages,
            max_tokens=1024,
            temperature=model_temp,
            top_p=0.95,
        )

        return content

    except Exception as e:
        logger.error(f"Error generating AI response: {str(e)}")
        return "I'm sorry, but I encountered an error while processing your request. Please try again later."


async def handle_chat(content: str, data_needed: List, user_id: str):
    """Handle a chat message with AI-generated responses.

    This function processes a chat message and generates a contextually relevant
    response using OpenAI, based on the user's context data.

    Args:
        content (str): The content of the chat message
        context (dict, optional): The context data for the chat

    Returns:
        dict: A response containing the processed message and AI-generated response
    """
    logger.info(f"Chat message: '{content}'")

    # Retrieve context, if any
    # context_data = await mock_data(user_id, data_needed)

    try:
        content = await generate_ai_response(content)  # , context_data)

        # Parse JSON response
        content = json.loads(content)

        return {
            "success": True,
            "message": "Chat message processed successfully",
            "response": {
                "intent": "chat",
                "content": {
                    "ai_response": content["response"],
                    "references": content["references"],
                    "soft_inferences": content["soft_inferences"],
                    "suggested_explorations": content["suggested_explorations"],
                    "context_data_used": {},  # context_data,
                },
            },
        }
    except Exception as e:
        logger.error(f"Error in handle_chat: {str(e)}")
        return {
            "success": False,
            "message": f"Error processing chat message: {str(e)}",
            "response": None,
        }
