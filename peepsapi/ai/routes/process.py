"""
This module defines the routes for AI-related operations in the application.

It includes an endpoint for processing natural language input, determining the
intent, and executing the appropriate action. The module leverages services
such as context management, intent parsing, and action dispatching to handle
user input effectively.

Modules:
    - logging: For logging information and errors.
    - fastapi: For defining API routes and dependencies.
    - peepsapi.ai.models: Contains data models for input and response.
    - peepsapi.ai.services.action_dispatcher: Handles action execution.
    - peepsapi.ai.services.context_service: Manages user-specific context.
    - peepsapi.ai.services.intent_parser: Parses natural language input.

Routes:
    - POST /ai/process: Processes user input and executes the appropriate action.
"""

from uuid import UUID

from fastapi import APIRouter, Depends

from peepsapi.ai.models import ActionResponse, UserInput
from peepsapi.ai.services.action_dispatcher import dispatch_action
from peepsapi.ai.services.intent_parser import intent_parser
from peepsapi.auth.services import auth_service
from peepsapi.utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


@router.post("/process", response_model=ActionResponse)
async def process_input(
    user_input: UserInput,
    current_person_id: UUID = Depends(auth_service.get_current_person),
):
    """Process natural language input, determine intent, and execute appropriate action.

    This endpoint serves as the main entry point for AI-powered NLP.
    It takes user text input, analyzes it to determine the user's intent, and dispatches
    the appropriate action handler to fulfill the request.

    Args: UserInput Model
    - text (str): The natural language input from the user.

    Returns: ActionResponse Model
    - id (Optional[str]): Unique identifier for the action.
    - success (bool): Whether the action was successful.
    - message (str): A message about the action status or result.
    - response (Optional[Dict]): Contains intent and content specific to each intent type.
        - For search_network: Contains search results and query information.
        - For send_message: Contains recipient and message content details.
        - For chat: Contains the AI response to the user's query.
    """
    try:
        logger.info(f"Processing with user id: {current_person_id}")
        logger.info(f"Processing input: {user_input.text}")

        # Parse the input using LLM to extract intent
        parsed = await intent_parser(user_input.text)
        logger.debug(f"Parsed result: {parsed}")

        # Dispatch the action to the appropriate intent handler
        result = await dispatch_action(parsed, current_person_id)

        return result

    except Exception as e:
        logger.error(f"Error processing input: {str(e)}")
        return {
            "success": False,
            "message": f"Error processing input: {str(e)}",
            "response": None,
        }
