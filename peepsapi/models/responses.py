"""Standard response models for the PeepsAPI application.

This module contains standardized response models for API endpoints,
including success and error responses.
"""

from typing import Any, Dict, List, Optional, Union

from pydantic import Field

from peepsapi.models.base import BaseModelWithExtra


class BaseResponse(BaseModelWithExtra):
    """Base response model for all API responses."""

    success: bool = True
    message: Optional[str] = None


class ErrorResponse(BaseResponse):
    """Standard error response model."""

    success: bool = False
    error_code: str
    error_type: str
    message: str
    details: Optional[Dict[str, Any]] = None


class SuccessResponse(BaseResponse):
    """Standard success response model."""

    success: bool = True
    data: Optional[Any] = None
