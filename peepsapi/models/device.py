"""Device models for the PeepsAPI application.

This module contains models related to devices, including the DeviceInfo model
which standardizes device information across the application.
"""

from typing import Optional

from peepsapi.models.base import BaseModelWithExtra
from peepsapi.utils import DateTimeModelMixin, UTCDateTime


class DeviceInfo(DateTimeModelMixin, BaseModelWithExtra):
    """Standardized device information model.

    This model provides a consistent structure for device information
    across the application, including authentication and session management.
    """

    name: str  # User-friendly name for the device
    type: str  # Type of device (e.g., "mobile", "desktop")
    os: Optional[str] = None  # Operating system of the device
    browser: Optional[str] = None  # Browser used
    user_agent: Optional[str] = None  # User agent string
    ip: Optional[str] = None  # IP address
    created_at: Optional[UTCDateTime]  # When the device info was created
    last_used_at: Optional[UTCDateTime] = None  # When the device was last used
    is_active: bool = True  # Whether the device is active
    credential_id: Optional[str] = None  # Reference to the PasskeyCredential
