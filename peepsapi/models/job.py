from typing import Literal, Optional, Union
from uuid import UUID

from pydantic import BaseModel, field_validator

from peepsapi.models.base import BaseModelWithExtra
from peepsapi.models.datetime import DateTimeModelMixin, UTCDateTime


class AddPostToFeedParams(BaseModel):
    post_id: UUID
    author_person_id: UUID
    created_at: UTCDateTime


class CreateFeedParams(BaseModel):
    owner_person_id: UUID
    starting_from: UTCDateTime
    quantity: int


class RemovePostFromFeedParams(BaseModel):
    post_id: UUID
    author_person_id: UUID


class RemoveEachOthersPostsFromFeedsParams(BaseModel):
    person_id_1: UUID
    person_id_2: UUID


class AddEachOthersPostsToFeedsParams(BaseModel):
    person_id_1: UUID
    person_id_2: UUID


class IncrementCommentCountersParams(BaseModel):
    parent_comment_id: Optional[UUID]
    post_author_person_id: UUID
    post_id: UUID


class Job(BaseModelWithExtra, DateTimeModelMixin):
    """Background jobs."""

    id: UUID
    action: Literal[
        "add_post_to_feed",
        "create_feed",
        "remove_post_from_feed",
        "remove_each_others_posts_from_feeds",
        "add_each_others_posts_to_feeds",
        "increment_comment_counters",
    ]
    reserved_until: UTCDateTime
    params: Union[
        AddPostToFeedParams,
        CreateFeedParams,
        RemovePostFromFeedParams,
        RemoveEachOthersPostsFromFeedsParams,
        AddEachOthersPostsToFeedsParams,
        IncrementCommentCountersParams,
    ]
    etag: Optional[str] = None

    @field_validator("params", mode="before")
    @classmethod
    def parse_params(cls, v, info):
        # info is a ValidationInfo object in Pydantic v2
        action = info.data.get("action")
        if action == "add_post_to_feed":
            return AddPostToFeedParams.model_validate(v)
        elif action == "create_feed":
            return CreateFeedParams.model_validate(v)
        elif action == "remove_post_from_feed":
            return RemovePostFromFeedParams.model_validate(v)
        elif action == "remove_each_others_posts_from_feeds":
            return RemoveEachOthersPostsFromFeedsParams.model_validate(v)
        elif action == "add_each_others_posts_to_feeds":
            return AddEachOthersPostsToFeedsParams.model_validate(v)
        elif action == "increment_comment_counters":
            return IncrementCommentCountersParams.model_validate(v)
        return v
