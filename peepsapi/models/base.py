"""Base models for the PeepsAPI application.
"""

import base64
import datetime
import json
from enum import Enum
from uuid import UUID

from pydantic import BaseModel, field_serializer

from peepsapi.models import UTCDateTime
from peepsapi.models.datetime import to_iso_string
from peepsapi.utils.case_conversion import cached_to_camel_case


def base64url_decode(data: str) -> bytes:
    """Decode a base64url-encoded string (without padding) to bytes."""
    padding = "=" * (-len(data) % 4)  # Add padding if needed
    return base64.urlsafe_b64decode(data + padding)


def is_utf8_decodable(data: bytes) -> bool:
    try:
        data.decode("utf-8")
        return True
    except UnicodeDecodeError:
        return False


def encode_b64url(data) -> str:
    """
    Encodes data to base64url string without padding.
    Supports both bytes and str inputs.
    """

    if is_utf8_decodable(data):
        return base64.urlsafe_b64encode(data).rstrip(b"=").decode("utf-8")
    else:
        return base64.urlsafe_b64encode(data).rstrip(b"=").decode("ascii")


def recursive_clean(obj):
    """Recursively clean data by encoding bytes and handling nested structures."""

    if isinstance(obj, bytes):
        return encode_b64url(obj)
    elif isinstance(obj, dict):
        return {k: recursive_clean(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [recursive_clean(v) for v in obj]
    elif isinstance(obj, BaseModel):
        return recursive_clean(obj.model_dump())
    elif isinstance(obj, UTCDateTime):
        return to_iso_string(obj)
    elif isinstance(obj, datetime.datetime):
        return to_iso_string(obj)
    return obj


# Base model that all models should inherit from
class BaseModelWithExtra(BaseModel):
    """Base model that allows extra fields.

    All models in the application should inherit from this class
    to ensure consistent handling of extra fields.

    This base model also handles:
    - Case conversion between snake_case (internal Python code) and camelCase (API)
    - Datetime serialization to ISO 8601 strings with 'Z' suffix for UTC timezone
    - Standardized model_dump_json_safe method for serialization
    """

    model_config = {
        "extra": "allow",
        "alias_generator": cached_to_camel_case,  # Convert snake_case to camelCase for API with caching
        "str_strip_whitespace": True,  # Auto-strip whitespace
        "validate_assignment": True,  # Validate on attribute assignment
        "populate_by_name": True,  # Allow populating by field name or alias
    }

    @field_serializer("*", mode="wrap")
    def serialize_fields(self, value, handler, info):
        """Custom serializer for all fields to handle datetime, bytes, and UUID."""
        # Let the default handler process first
        result = handler(value)

        # Apply custom serialization for specific types
        if isinstance(value, UTCDateTime):
            return (
                value.to_iso_string()
                if hasattr(value, "to_iso_string")
                else (
                    value.isoformat().replace("+00:00", "Z")
                    if hasattr(value, "isoformat")
                    else value
                )
            )
        elif isinstance(value, bytes):
            return encode_b64url(value)
        elif isinstance(value, UUID):
            return str(value)

        return result

    def model_dump_json_safe(self) -> dict:
        """Convert the model to a dictionary with cleaned values including datetime and bytes.

        Returns:
            dict: A dictionary representation of the model with safe serialized values
        """
        return recursive_clean(self.model_dump())

    def model_dump_json_clean(self) -> str:
        """Return JSON string with cleaned nested data."""
        return json.dumps(self.model_dump_json_safe())


class IdentifierType(str, Enum):
    """Enum for identifier types."""

    EMAIL = "email"
    PHONE = "phone"
