"""Datetime utilities for PeepsAPI.

This module provides standardized datetime handling utilities for the PeepsAPI application,
ensuring consistent datetime operations throughout the codebase.

Note: Most datetime serialization is now handled by the BaseModelWithExtra class in base.py,
which all models should inherit from. This module provides the UTCDateTime class and utility
functions for working with datetime objects.
"""

import datetime
from functools import lru_cache
from typing import Any, Dict, Optional, Union
from zoneinfo import ZoneInfo

from pydantic import BaseModel, field_serializer


class UTCDateTime(datetime.datetime):
    """A datetime class that always uses UTC timezone.

    This class extends the standard datetime class to ensure all datetime objects
    are in UTC timezone. It provides methods for conversion to and from different
    formats and timezones.
    """

    @classmethod
    def __get_validators__(cls):
        """Get validators for Pydantic v1 compatibility."""
        yield cls.validate

    @classmethod
    def __get_pydantic_core_schema__(cls, _source_type, _handler):
        """Get Pydantic v2 core schema.

        This method is required for Pydantic v2 compatibility.
        """
        from pydantic_core import core_schema

        return core_schema.union_schema(
            [
                core_schema.is_instance_schema(datetime.datetime),
                core_schema.str_schema(),
            ]
        )

    @classmethod
    def validate(cls, value):
        """Validate and convert a value to UTCDateTime.

        Args:
            value: The value to validate

        Returns:
            UTCDateTime: The validated datetime
        """
        if isinstance(value, str):
            return cls.from_iso_string(value)
        if isinstance(value, datetime.datetime):
            return cls.from_datetime(value)
        raise ValueError(f"Cannot convert {value} to UTCDateTime")

    @classmethod
    def now(cls) -> "UTCDateTime":
        """Get the current UTC datetime.

        Returns:
            UTCDateTime: The current datetime in UTC
        """
        return cls.from_datetime(datetime.datetime.now(datetime.timezone.utc))

    @classmethod
    def from_datetime(cls, dt: datetime.datetime) -> "UTCDateTime":
        """Convert a standard datetime to UTCDateTime.

        Args:
            dt (datetime.datetime): The datetime to convert

        Returns:
            UTCDateTime: The converted datetime in UTC
        """
        # If the datetime has no timezone, assume it's UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=datetime.timezone.utc)
        # If the datetime has a timezone, convert it to UTC
        elif dt.tzinfo != datetime.timezone.utc:
            dt = dt.astimezone(datetime.timezone.utc)

        return cls(
            dt.year,
            dt.month,
            dt.day,
            dt.hour,
            dt.minute,
            dt.second,
            dt.microsecond,
            tzinfo=datetime.timezone.utc,
        )

    @classmethod
    def from_iso_string(cls, iso_string: str) -> "UTCDateTime":
        """Create a UTCDateTime from an ISO format string.

        Args:
            iso_string (str): ISO format datetime string

        Returns:
            UTCDateTime: The parsed datetime in UTC
        """
        # Handle 'Z' suffix (UTC)
        if "Z" in iso_string:
            iso_string = iso_string.replace("Z", "+00:00")

        # Parse the string to a standard datetime
        dt = datetime.datetime.fromisoformat(iso_string)
        return cls.from_datetime(dt)

    @classmethod
    def from_timestamp(cls, timestamp: float) -> "UTCDateTime":
        """Create a UTCDateTime from a UNIX timestamp (seconds since epoch).

        Args:
            timestamp (float): The timestamp to convert

        Returns:
        UTCDateTime: A datetime in UTC
        """
        dt = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc)
        return cls.from_datetime(dt)

    def to_iso_string(self) -> str:
        """Convert to ISO 8601 format string.

        Returns:
            str: ISO format string with UTC timezone (Z suffix)
        """
        return self.isoformat().replace("+00:00", "Z")

    def to_local_timezone(self, tz_name: str) -> datetime.datetime:
        """Convert to a specific timezone.

        Args:
            tz_name (str): Timezone name (e.g., 'America/Los_Angeles')

        Returns:
            datetime.datetime: Datetime in the specified timezone
        """
        import pytz

        tz = pytz.timezone(tz_name)
        return self.astimezone(tz)

    def format_for_display(
        self, tz_name: Optional[str] = None, format_str: Optional[str] = None
    ) -> str:
        """Format the datetime for display.

        Args:
            tz_name (Optional[str]): Timezone name for conversion
            format_str (Optional[str]): Format string for strftime

        Returns:
            str: Formatted datetime string
        """
        dt = self
        if tz_name:
            dt = self.to_local_timezone(tz_name)

        if format_str:
            return dt.strftime(format_str)
        # Use to_iso_string for consistent formatting with Z suffix
        if dt.tzinfo == datetime.timezone.utc:
            return dt.isoformat().replace("+00:00", "Z")
        return dt.isoformat()

    def __json__(self):
        """Convert to a JSON-serializable format."""
        return self.to_iso_string()


class DateTimeModelMixin(BaseModel):
    """Mixin for models with datetime fields.

    This mixin provides validation methods for datetime fields and ensures
    that UTCDateTime is properly handled in serialization.

    Note: Most datetime serialization is now handled by the BaseModelWithExtra class,
    which all models should inherit from.
    """

    model_config = {
        "arbitrary_types_allowed": True,
    }

    @field_serializer("*", mode="wrap")
    def serialize_datetime_fields(self, value, handler, info):
        """Custom serializer for datetime fields."""
        # Let the default handler process first
        result = handler(value)

        # Apply custom serialization for UTCDateTime
        if isinstance(value, UTCDateTime):
            return (
                value.to_iso_string()
                if hasattr(value, "to_iso_string")
                else value.isoformat().replace("+00:00", "Z")
            )

        return result

    @classmethod
    def model_validate(cls, obj: Any, **kwargs) -> "DateTimeModelMixin":
        """Parse and validate input data.

        This method ensures all datetime fields are converted to UTCDateTime.

        Args:
            obj: The data to validate
            **kwargs: Additional arguments for model_validate

        Returns:
            DateTimeModelMixin: Validated model instance
        """
        # If obj is a dict, convert any datetime strings to UTCDateTime
        if isinstance(obj, dict):
            obj_copy = obj.copy()
            for key, value in obj_copy.items():
                if isinstance(value, str) and is_iso_datetime(value):
                    obj_copy[key] = UTCDateTime.from_iso_string(value)
                elif isinstance(value, datetime.datetime):
                    obj_copy[key] = UTCDateTime.from_datetime(value)
            return super().model_validate(obj_copy, **kwargs)
        return super().model_validate(obj, **kwargs)

    def model_dump_json_safe(self) -> dict:
        """Convert the model to a dictionary with datetime objects converted to ISO format strings.

        Returns:
            dict: A dictionary representation of the model with datetime objects as strings
        """
        return self.model_dump()


def is_iso_datetime(value: str) -> bool:
    """Check if a string is an ISO format datetime.

    Args:
        value (str): The string to check

    Returns:
        bool: True if the string is an ISO format datetime, False otherwise
    """
    try:
        # Handle 'Z' suffix (UTC)
        if "Z" in value:
            value = value.replace("Z", "+00:00")
        datetime.datetime.fromisoformat(value)
        return True
    except (ValueError, TypeError):
        return False


@lru_cache(maxsize=32)
def get_timezone(tz_name: str) -> ZoneInfo:
    """Get cached timezone object.

    Args:
        tz_name: Timezone name

    Returns:
        ZoneInfo: Cached timezone object
    """
    return ZoneInfo(tz_name)


def validate_timezone(tz_name: str) -> bool:
    """Validate timezone name.

    Args:
        tz_name: Timezone name to validate

    Returns:
        bool: True if valid timezone
    """
    try:
        ZoneInfo(tz_name)
        return True
    except Exception:
        return False


def validate_format_string(format_str: str) -> bool:
    """Validate datetime format string.

    Args:
        format_str: Format string to validate

    Returns:
        bool: True if valid format string
    """
    try:
        datetime.datetime.now().strftime(format_str)
        return True
    except Exception:
        return False


def to_utc_datetime(
    value: Union[str, datetime.datetime, UTCDateTime, None]
) -> Optional[UTCDateTime]:
    """Smart converter that handles multiple input types.

    Args:
        value: Input value to convert (str, datetime, UTCDateTime, or None)

    Returns:
        Optional[UTCDateTime]: Converted UTC datetime or None
    """
    if value is None:
        return None
    if isinstance(value, UTCDateTime):
        return value
    if isinstance(value, str):
        return UTCDateTime.from_iso_string(value)
    if isinstance(value, datetime.datetime):
        return UTCDateTime.from_datetime(value)
    raise ValueError(f"Cannot convert type {type(value)} to UTCDateTime")


def now() -> UTCDateTime:
    """Get the current UTC datetime.

    Returns:
        UTCDateTime: The current datetime in UTC
    """
    return UTCDateTime.now()


def from_iso_string(iso_string: str) -> UTCDateTime:
    """Create a UTCDateTime from an ISO format string.

    Args:
        iso_string (str): ISO format datetime string

    Returns:
        UTCDateTime: The parsed datetime in UTC
    """
    return to_utc_datetime(iso_string)


def from_datetime(dt: datetime.datetime) -> UTCDateTime:
    """Convert a standard datetime to UTCDateTime.

    Args:
        dt (datetime.datetime): The datetime to convert

    Returns:
        UTCDateTime: The converted datetime in UTC
    """
    return to_utc_datetime(dt)


def to_iso_string(dt: Union[datetime.datetime, UTCDateTime, str]) -> str:
    """Convert a datetime to ISO 8601 format string.

    Args:
        dt (Union[datetime.datetime, UTCDateTime, str]): The datetime to convert

    Returns:
        str: ISO format string with UTC timezone (Z suffix)
    """
    if isinstance(dt, str):
        # If it's already a string, check if it's a valid ISO format
        if is_iso_datetime(dt):
            return dt
        # If not, try to parse it
        try:
            return UTCDateTime.from_iso_string(dt).to_iso_string()
        except ValueError:
            return dt
    elif isinstance(dt, UTCDateTime):
        return dt.to_iso_string()
    return UTCDateTime.from_datetime(dt).to_iso_string()


def format_for_display(
    dt: Union[datetime.datetime, UTCDateTime],
    tz_name: Optional[str] = None,
    format_str: Optional[str] = None,
) -> str:
    """Format a datetime for display.

    Args:
        dt (Union[datetime.datetime, UTCDateTime]): The datetime to format
        tz_name (Optional[str]): Timezone name for conversion
        format_str (Optional[str]): Format string for strftime

    Returns:
        str: Formatted datetime string
    """
    if not isinstance(dt, UTCDateTime):
        dt = UTCDateTime.from_datetime(dt)

    # If no timezone or format is specified and it's UTC, use to_iso_string for consistent Z suffix
    if not tz_name and not format_str and dt.tzinfo == datetime.timezone.utc:
        return to_iso_string(dt)

    return dt.format_for_display(tz_name, format_str)


def serialize_datetime_dict(data: Dict[str, Any]) -> Dict[str, Any]:
    """Serialize datetime objects in a dictionary to ISO format strings.

    Args:
        data (Dict[str, Any]): The dictionary to serialize

    Returns:
        Dict[str, Any]: The serialized dictionary
    """
    result = {}
    for key, value in data.items():
        if isinstance(value, (datetime.datetime, UTCDateTime)):
            result[key] = to_iso_string(value)
        elif isinstance(value, str) and is_iso_datetime(value):
            # If it's already an ISO string, keep it as is
            result[key] = value
        elif isinstance(value, dict):
            result[key] = serialize_datetime_dict(value)
        elif isinstance(value, list):
            result[key] = [
                serialize_datetime_dict(item)
                if isinstance(item, dict)
                else to_iso_string(item)
                if isinstance(item, (datetime.datetime, UTCDateTime))
                else item
                for item in value
            ]
        else:
            result[key] = value
    return result
