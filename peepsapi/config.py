"""
This module provides functionality to load and manage runtime configuration.

Functions:
- configure_logging(): Sets up logging configuration for the application.
- init(): Connects to Azure Key Vault, retrieves secrets, and stores them in env. variables.
- get(key: str, default=None): **DEPRECATED** Retrieves a value for the given key.

Dependencies:
- azure-identity
- azure-keyvault-secrets
"""

import json
import logging
import os
import subprocess
import sys
from pathlib import Path

from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


def init(env_file=".env"):
    """
    Fetch secrets from Azure Key Vault and store them as environment variables.

    If any environment variable with the key defined in env file has empty value,
    The value will be fetched from Azure Key Vault, and `AZURE_KEY_VAULT_URL`
    environment variable must have been set.
    """
    # Load keys from env file
    env_keys = []
    env_file_path = os.path.join(os.getcwd(), env_file)
    with open(env_file_path, "r") as file:
        logger.info(f"🛠️ Loading a list of mandatory keys from '{env_file_path}'")
        for line in file:
            # Strip whitespace from the line
            stripped_line = line.strip()

            # Exclude empty lines and comments
            if not stripped_line or stripped_line.startswith("#"):
                continue

            # Split the line at the first '=' character
            if "=" in stripped_line:
                key = stripped_line.split("=", 1)[0]

            # Strip whitespace from the key
            key = key.strip()

            # Store the key if no value provided
            if not os.getenv(key):
                env_keys.append(key)

    if len(env_keys) == 0:
        logger.info("✅ All environment variables from .env have values.")
        logger.info("📝 Skip loading values from Azure Key Vault.")
        return

    logger.info("🛠️ CONFIG INITIALIZATION START")
    vault_url = os.getenv("AZURE_KEY_VAULT_URL")
    logger.info(f"🔐 AZURE_KEY_VAULT_URL: {vault_url}")
    if not vault_url:
        raise ValueError("AZURE_KEY_VAULT_URL not set")

    credential = DefaultAzureCredential()
    client = SecretClient(vault_url=vault_url, credential=credential)

    # Fetch missing values from Azure Key Vault
    for key in env_keys:
        try:
            # Convert from snake_case to kebab-case for Azure Key Vault
            secret_name = key.replace("_", "-").lower()
            secret_value = client.get_secret(secret_name).value
            os.environ[key] = secret_value
            logger.info(f"📦 Loaded {key} from Azure Key Vault ({vault_url})")
        except Exception as e:
            logger.error(f"⚠️ Failed to load secret {key}: {str(e)} ({vault_url})")

    logger.info("✅ CONFIG INITIALIZATION COMPLETE")


def get(key: str, default=None):
    """
    Retrieve a value for the given key.

    **DEPRECATED**: This function is deprecated and will be removed in future versions.
    Use `os.getenv(key, default)` instead.

    Value fetch order:
    1. Environment variable
    ~2. Values loaded from Azure Key Vault~
    3. Default value
    """
    return os.getenv(key, default)


def init_local_override():
    # Load environment variables from .env file
    load_dotenv(dotenv_path=".env", override=True)

    # Save AZURE_KEY_VAULT_URL from .env as it will be
    # overwritten by local.env
    azure_key_vault_url = os.getenv("AZURE_KEY_VAULT_URL")

    # Load environment variables from local-o.env file,
    # overwrite any env vars from .env, including AZURE_KEY_VAULT_URL
    load_dotenv(dotenv_path="local-override.env", override=True)

    # Initialize configuration keys defined in local-override.env
    # with AZURE_KEY_VAULT_URL defined in local-override.env
    init(env_file="local-override.env")

    # Restore AZURE_KEY_VAULT_URL from .env that was
    # overwritten by local-override.env
    os.environ["AZURE_KEY_VAULT_URL"] = azure_key_vault_url


def configure_logging():
    """Script to configure logging settings in local-log-config.json via environment variables."""
    VALID_LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    VALID_LOG_FORMATS = ["Minimal", "Standard", "Detailed", "Link"]

    DEFAULT_CONFIG = {
        "level": "INFO",
        "format": "Standard",
        "handler": "console",
        "level_azure": "WARNING",
    }

    # Get config from environment variables
    log_level_root = os.getenv("LOG_LEVEL_ROOT")
    log_level = os.getenv("LOG_LEVEL")
    log_level_azure = os.getenv("LOG_LEVEL_AZURE")
    log_format = os.getenv("LOG_FORMAT")
    is_log_colored = os.getenv("LOG_COLORED", "").lower() == "true"
    is_log_short_levelname = os.getenv("LOG_SHORT_LEVELNAME", "").lower() == "true"
    is_log_context = os.getenv("LOG_CONTEXT", "").lower() == "true"
    # Optional configs
    log_format_str = os.getenv("LOG_FORMAT_STRING", "")
    log_format_timestamp = os.getenv("LOG_FORMAT_TIMESTAMP", "")

    # Apply formatting and validation
    log_level_root = (
        log_level_root.upper() if log_level_root else DEFAULT_CONFIG["level"]
    )
    log_level = log_level.upper() if log_level else DEFAULT_CONFIG["level"]
    log_format = log_format.capitalize() if log_format else DEFAULT_CONFIG["format"]
    log_level_azure = (
        log_level_azure.upper() if log_level_azure else DEFAULT_CONFIG["level_azure"]
    )

    if log_level_root not in VALID_LOG_LEVELS:
        print(f"Invalid root log level: {log_level_root}")
        print(f"Valid log levels: {', '.join(VALID_LOG_LEVELS)}")
        sys.exit(1)
    if log_level not in VALID_LOG_LEVELS:
        print(f"Invalid log level: {log_level}")
        print(f"Valid log levels: {', '.join(VALID_LOG_LEVELS)}")
        sys.exit(1)
    if log_level_azure not in VALID_LOG_LEVELS:
        print(f"Invalid Azure log level: {log_level_azure}")
        print(f"Valid log levels: {', '.join(VALID_LOG_LEVELS)}")
        sys.exit(1)
    if log_format not in VALID_LOG_FORMATS:
        print(f"Invalid log format: {log_format}")
        print(f"Valid log formats: {', '.join(VALID_LOG_FORMATS)}")
        sys.exit(1)

    # Path to the logging config files
    local_config_path = Path.cwd() / "local-log-config.json"
    template_config_path = Path.cwd() / "template-log-config.json"

    # Load the template config
    with open(template_config_path, "r") as f:
        config = json.load(f)

    # Set the handler based on format
    if log_format in ("Minimal", "Detailed", "Link"):
        handler = log_format.lower() + "_console"
    else:
        handler = "console"

    # Update all peepsapi loggers
    peepsapi_loggers = [
        logger_name
        for logger_name in config["loggers"]
        if logger_name.startswith("peepsapi")
    ]
    for logger_name in peepsapi_loggers:
        config["loggers"][logger_name]["handlers"] = [handler]
        config["loggers"][logger_name]["level"] = log_level

    # Update all other explicit loggers (uvicorn, etc.)
    other_loggers = [
        logger_name
        for logger_name in config["loggers"]
        if not logger_name.startswith("peepsapi")
    ]

    for logger_name in other_loggers:
        config["loggers"][logger_name]["level"] = log_level

    # Update azure log level for root azure logger
    if "azure.core.pipeline.policies.http_logging_policy" in config["loggers"]:
        config["loggers"]["azure.core.pipeline.policies.http_logging_policy"][
            "level"
        ] = log_level_azure

    # Update the root logger
    config["root"]["level"] = log_level_root

    # Save the updated config
    with open(local_config_path, "w") as f:
        json.dump(config, f, indent=4)
        f.write("\n")

    print(f"Local logging configured in {local_config_path}:")
    print(f"  - Root log level: {log_level_root}")
    print(f"  - Log level: {log_level}")
    print(f"  - Azure log level: {log_level_azure}")
    print(f"  - Log format: {log_format}")
    print(f"  - Colored log: {'true' if is_log_colored else 'false'}")
    print(f"  - Short log levelname: {'true' if is_log_short_levelname else 'false'}")
    print(f"  - Log context: {'true' if is_log_context else 'false'}")
    # Log optional configs
    if log_format_str:
        print(f"  - Log format string: {log_format_str}")
    if log_format_timestamp:
        print(f"  - Log format timestamp: {log_format_timestamp}")


if __name__ == "__main__":
    # This script serves as a wrapper for executing commands when called directly.
    # When used with a hot-reload server, it helps avoid reloading secrets from
    # Azure Key Vault on each server restart.
    log_level = getattr(logging, os.getenv("LOG_LEVEL", "INFO").upper())
    logging.basicConfig(level=log_level, format="ENV LOADER: %(message)s")

    log_level_azure = os.getenv("LOG_LEVEL_AZURE", "WARNING")
    log_level_azure = log_level_azure.upper() if log_level_azure else "WARNING"
    if log_level_azure not in ("WARNING", "ERROR", "CRITICAL"):
        logger.warning(
            "⚠️ Recommended to set Azure log level to WARNING or higher to reduce verbosity"
        )

    log_level_azure_enum = getattr(logging, log_level_azure)
    logging.getLogger("azure").setLevel(log_level_azure_enum)
    logging.getLogger("azure.core.pipeline.policies.http_logging_policy").setLevel(
        log_level_azure_enum
    )

    init_local_override()

    # Initialize configuration keys defined in .env
    # and not initialized by local.env
    init(env_file=".env")

    print("🐞 Configuring logging...")
    configure_logging()

    # Check if a command is provided
    if len(sys.argv) > 1:
        command = sys.argv[1:]

        try:
            # Run the command as a subprocess
            result = subprocess.run(command, check=True)
            sys.exit(result.returncode)
        except KeyboardInterrupt:
            logger.info("🧹 triggered child process exit via SIGINT")
            sys.exit(130)

    logger.error(f"❌ {__file__}: No command provided to execute.")
    sys.exit(1)
