"""
Centralized container instances and getter functions for dependency injection.

This module provides a central location for all CosmosContainer instances and
their corresponding getter functions for use with FastAPI's dependency injection.

Usage:
    from peepsapi.services.cosmos_containers import get_people_container

    @router.get("/endpoint")
    def endpoint(container=Depends(get_people_container)):
        # Use container.create_model, container.replace_model, etc.
        pass
"""

from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync

# Sync interface

# Auth-related container instances
authentication_challenges_container = CosmosContainer("authentication_challenges")
device_tokens_container = CosmosContainer("device_tokens")
invite_tokens_container = CosmosContainer("invite_tokens")
passkey_credentials_container = CosmosContainer("passkey_credentials")
registration_challenges_container = CosmosContainer("registration_challenges")
session_tokens_container = CosmosContainer("session_tokens")

# Crud-related container instances
connections_container = CosmosContainer("connections")
people_container = CosmosContainer("people")
pictures_container = CosmosContainer("pictures")
notes_container = CosmosContainer("notes")
posts_container = CosmosContainer("posts")
comments_container = CosmosContainer("comments")
feeds_container = CosmosContainerAsync("feeds")
reactions_container = CosmosContainerAsync("reactions")
person_reactions_container = CosmosContainerAsync("person_reactions")

# System-related container instances
audit_logs_container = CosmosContainer("audit_logs")
locks_container = CosmosContainer("locks")
active_jobs_container = CosmosContainerAsync("active_jobs")

# Inactive container instances
communities_container = CosmosContainer("communities")
conversations_container = CosmosContainer("conversations")
events_container = CosmosContainer("events")
messages_container = CosmosContainer("messages")


def get_people_container():
    """Get the people container instance."""
    return people_container


def get_posts_container():
    """Get the posts container instance."""
    return posts_container


def get_comments_container():
    """Get the comments container instance."""
    return comments_container


def get_conversations_container():
    """Get the conversations container instance."""
    return conversations_container


def get_messages_container():
    """Get the messages container instance."""
    return messages_container


def get_communities_container():
    """Get the communities container instance."""
    return communities_container


def get_connections_container():
    """Get the connections container instance."""
    return connections_container


def get_locks_container():
    """Get the locks container instance."""
    return locks_container


def get_events_container():
    """Get the events container instance."""
    return events_container


def get_pictures_container():
    """Get the pictures container instance."""
    return pictures_container


def get_notes_container():
    """Get the notes container instance."""
    return notes_container


def get_active_jobs_container():
    """Get the active jobs container instance."""
    return active_jobs_container


def get_feeds_container():
    """Get the feeds container instance."""
    return feeds_container


def get_reactions_container():
    """Get the reactions container instance."""
    return reactions_container


def get_person_reactions_container():
    """Get the person_reactions container instance."""
    return person_reactions_container


# Auth-related getters


def get_invite_tokens_container():
    """Get the invite tokens container instance."""
    return invite_tokens_container


def get_registration_challenges_container():
    """Get the registration challenges container instance."""
    return registration_challenges_container


def get_authentication_challenges_container():
    """Get the authentication challenges container instance."""
    return authentication_challenges_container


def get_passkey_credentials_container():
    """Get the passkey credentials container instance."""
    return passkey_credentials_container


def get_session_tokens_container():
    """Get the session tokens container instance."""
    return session_tokens_container


def get_device_tokens_container():
    """Get the device tokens container instance."""
    return device_tokens_container


def get_audit_logs_container():
    """Get the audit logs container instance."""
    return audit_logs_container
