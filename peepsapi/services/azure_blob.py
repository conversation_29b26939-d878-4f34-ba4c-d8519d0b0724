"""Azure Blob Storage wrapper utilities."""

import os
from typing import Optional

from azure.storage.blob import BlobServiceClient

from peepsapi import config
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

_blob_client: Optional[BlobServiceClient] = None


def _get_client() -> BlobServiceClient:
    global _blob_client
    if _blob_client is not None:
        return _blob_client

    connection_string = config.get("AZURE_BLOB_CONNECTION_STRING")
    if not connection_string:
        raise ValueError("AZURE_BLOB_CONNECTION_STRING not configured")
    _blob_client = BlobServiceClient.from_connection_string(connection_string)
    return _blob_client


def upload_blob(
    container_name: str, blob_name: str, data: bytes, content_type: str
) -> str:
    """Upload bytes to Azure Blob Storage."""
    client = _get_client()
    container = client.get_container_client(container_name)
    blob = container.get_blob_client(blob_name)
    blob.upload_blob(data, overwrite=True, content_type=content_type)
    return blob.url


def download_blob(container_name: str, blob_name: str) -> bytes:
    """Download blob bytes from Azure Blob Storage."""
    client = _get_client()
    container = client.get_container_client(container_name)
    blob = container.get_blob_client(blob_name)
    stream = blob.download_blob()
    return stream.readall()


def delete_blob(container_name: str, blob_name: str) -> None:
    """Delete a blob from Azure Blob Storage."""
    client = _get_client()
    container = client.get_container_client(container_name)
    blob = container.get_blob_client(blob_name)
    blob.delete_blob()
