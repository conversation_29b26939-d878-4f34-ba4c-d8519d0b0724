"""Global exception handlers for the PeepsAPI application.

This module provides global exception handlers for the FastAPI application
to ensure consistent error responses across all endpoints.
"""

import os
import traceback
from typing import Union

from fastapi import FastAPI, HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError as PydanticValidationError

from peepsapi.models.responses import ErrorResponse
from peepsapi.utils.error_handling import PeepsAPIException, get_error_details
from peepsapi.utils.error_monitoring import track_error
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def peepsapi_exception_handler(
    request: Request, exc: Peeps<PERSON>IException
) -> JSONResponse:
    """Handle PeepsAPIException instances.

    Args:
        request (Request): FastAPI request object
        exc (PeepsAPIException): The exception

    Returns:
        JSONResponse: Standardized error response
    """
    logger.error(
        "❌ PeepsAPIException: {} - {}".format(exc.error_type, exc.detail),
        extra={
            "error_code": exc.error_code,
            "error_type": exc.error_type,
            "path": request.url.path,
            "method": request.method,
        },
    )

    # Track the error in the monitoring system
    track_error(
        error_type=exc.error_type,
        error_code=exc.error_code,
        status_code=exc.status_code,
        message=str(exc.detail),
        path=request.url.path,
        method=request.method,
        request=request,
        details=exc.details,
    )

    # Note: We could get person_id from request state if needed
    # person_id = getattr(request.state, "person_id", None)

    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False,
            error_code=exc.error_code,
            error_type=exc.error_type,
            message=exc.detail,
            details=exc.details,
        ).model_dump_json_safe(),
    )


async def validation_exception_handler(
    request: Request, exc: Union[RequestValidationError, PydanticValidationError]
) -> JSONResponse:
    """Handle validation errors.

    Args:
        request (Request): FastAPI request object
        exc (Union[RequestValidationError, PydanticValidationError]): The exception

    Returns:
        JSONResponse: Standardized error response
    """
    error_details = []

    if hasattr(exc, "errors"):
        for error in exc.errors():
            error_details.append(
                {
                    "loc": error.get("loc", []),
                    "msg": error.get("msg", ""),
                    "type": error.get("type", ""),
                }
            )

    logger.warning(
        "⚠️ Validation error: {}".format(str(exc)),
        extra={
            "path": request.url.path,
            "method": request.method,
            "details": error_details,
        },
    )

    # Track the error in the monitoring system
    track_error(
        error_type="validation_error",
        error_code="VALIDATION_ERROR",
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message="Request validation failed",
        path=request.url.path,
        method=request.method,
        request=request,
        details={"errors": error_details},
    )

    # Note: We could get person_id from request state if needed
    # person_id = getattr(request.state, "person_id", None)

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(
            success=False,
            error_code="VALIDATION_ERROR",
            error_type="validation_error",
            message="Request validation failed",
            details={"errors": error_details},
        ).model_dump_json_safe(),
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle standard HTTPExceptions.

    Args:
        request (Request): FastAPI request object
        exc (HTTPException): The exception

    Returns:
        JSONResponse: Standardized error response
    """
    logger.warning(
        "⚠️ HTTPException: {} - {}".format(exc.status_code, exc.detail),
        extra={
            "path": request.url.path,
            "method": request.method,
            "status_code": exc.status_code,
        },
    )

    # Track the error in the monitoring system
    track_error(
        error_type="http_error",
        error_code=f"HTTP_{exc.status_code}",
        status_code=exc.status_code,
        message=str(exc.detail),
        path=request.url.path,
        method=request.method,
        request=request,
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False,
            error_code=f"HTTP_{exc.status_code}",
            error_type="http_error",
            message=str(exc.detail),
        ).model_dump_json_safe(),
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle all other exceptions.

    Args:
        request (Request): FastAPI request object
        exc (Exception): The exception

    Returns:
        JSONResponse: Standardized error response
    """
    logger.error(
        "❌ Unhandled exception: {}".format(str(exc)),
        extra={
            "path": request.url.path,
            "method": request.method,
            "traceback": traceback.format_exc(),
        },
    )

    # In production, don't include traceback in response
    is_debug = os.getenv("DEBUG_MODE", "false").lower() == "true"
    details = None

    if is_debug:
        details = get_error_details(exc, include_traceback=True)
    else:
        details = get_error_details(exc, include_traceback=False)

    # Track the error in the monitoring system
    track_error(
        error_type="server_error",
        error_code="SERVER_ERROR",
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message="An unexpected error occurred",
        path=request.url.path,
        method=request.method,
        request=request,
        details=details,
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            success=False,
            error_code="SERVER_ERROR",
            error_type="server_error",
            message="An unexpected error occurred",
            details=details,
        ).model_dump_json_safe(),
    )


def add_exception_handlers(app: FastAPI) -> None:
    """Add all exception handlers to the FastAPI app.

    Args:
        app (FastAPI): The FastAPI application
    """
    app.add_exception_handler(PeepsAPIException, peepsapi_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(PydanticValidationError, validation_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
