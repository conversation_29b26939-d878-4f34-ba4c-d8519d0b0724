"""User activity service for authentication.

This module provides functionality for tracking and querying user activity,
including login times, device usage, and session information.
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from peepsapi.auth.services.device_service import DeviceService
from peepsapi.models import UTCDateTime
from peepsapi.utils.logging import get_logger

# Configure logger
logger = get_logger(__name__)


class UserActivityService:
    """Service for tracking user activity."""

    def __init__(self):
        """Initialize the user activity service."""
        self.device_service = DeviceService()

    def get_last_login_time(self, person_id: UUID) -> Optional[UTCDateTime]:
        """Get the last login time for a person.

        Args:
            person_id (UUID): The person's ID

        Returns:
            Optional[UTCDateTime]: The last login time, or None if not found
        """
        return self.device_service.get_last_login_time(person_id)

    def get_device_activity(self, person_id: UUID) -> List[Dict[str, Any]]:
        """Get device activity history for a person.

        Args:
            person_id (UUID): The person's ID

        Returns:
            List[Dict[str, Any]]: List of device activity records
        """
        return self.device_service.get_device_activity(person_id)


# Create a global instance of the user activity service
user_activity_service = UserActivityService()
