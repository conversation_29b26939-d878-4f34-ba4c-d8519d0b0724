"""Authentication service for FIDO2 flows.

This module provides core authentication functionality for FIDO2 flows,
including session token creation, cookie setting, and credential verification.
"""

from typing import Optional
from uuid import UUID

from fastapi import Cookie, HTTPException, Request, Response, status

from peepsapi import config
from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import PasskeyCredential
from peepsapi.auth.models.request_response_models import (
    RegistrationVerifyRequest,
    RegistrationVerifyResponse,
)
from peepsapi.auth.services.azure_ad_service import azure_ad_service
from peepsapi.auth.services.challenge_service import ChallengeMode, challenge_service
from peepsapi.auth.services.token_service import token_service
from peepsapi.auth.utils.constants import (
    AZURE_AD_COOKIE_NAME,
    JWT_EXPIRATION_SECONDS,
    OVERRIDE_PERSON_ID_COOKIE_NAME,
    SESSION_TOKEN_COOKIE_NAME,
)
from peepsapi.crud.models.person import Person
from peepsapi.models import now
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.logging import get_logger

# Configure logger
logger = get_logger(__name__)

# FIDO2 configuration will be loaded in the AuthService class
# This avoids validation at import time, which can cause issues with the initialization order

# Authentication flow:
# 1. AuthMiddleware sets request.state.person_id from JWT or Azure AD token
# 2. For JWT auth, request.state.override_person_id is set to None
# 3. For Azure AD auth, request.state.override_person_id is set from x-peeps-id cookie if present, otherwise None
# 4. get_current_person returns override_person_id if available, otherwise person_id


class AuthService:
    """Service for authentication operations."""

    def __init__(self):
        """Initialize the authentication service."""
        # Load FIDO2 configuration
        self.rp_id = config.get("WEBAUTHN_RP_ID")
        self.rp_origin = config.get("WEBAUTHN_RP_ORIGIN")

    async def authenticate_request(
        self,
        request: Request,
        session_token: Optional[str] = Cookie(None, alias=SESSION_TOKEN_COOKIE_NAME),
        azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
    ) -> bool:
        """Authenticate a request using JWT or Azure AD tokens.

        This function can be used as a FastAPI dependency to authenticate a request.
        It checks for JWT and Azure AD tokens, validates them, and sets the request state.

        Args:
            request (Request): The FastAPI request object
            session_token (Optional[str]): The JWT session token from cookies
            azure_ad_token (Optional[str]): The Azure AD session token from cookies

        Returns:
            bool: True if authentication was successful, False otherwise
        """
        # Check if the user is already authenticated via middleware
        if hasattr(request.state, "person_id"):
            logger.debug(
                f"🔧 Request already authenticated via middleware", request=request
            )
            return True

        # Check if the user is authenticated
        is_authenticated = False

        # Check JWT token
        if session_token:
            logger.debug(f"🔧 JWT token found, validating...", request=request)
            jwt_payload = token_service.validate_token(session_token)
            if jwt_payload:
                # Update token usage
                token_service.update_token_usage(session_token, request)

                logger.info(
                    f"✅ JWT token valid",
                    request=request,
                    person_id=jwt_payload.get("sub"),
                    extra={"auth_source": "jwt"},
                )
                is_authenticated = True
                # Add person ID to request state
                request.state.person_id = jwt_payload["sub"]
                request.state.override_person_id = (
                    None  # For JWT auth, override_person_id is None
                )
                request.state.auth_source = "jwt"
            else:
                logger.warning(f"⚠️ JWT token validation failed", request=request)

        # Check Azure AD token if JWT authentication failed
        if not is_authenticated and azure_ad_token:
            logger.debug(f"🔧 Azure AD token found, validating...", request=request)
            azure_ad_payload = azure_ad_service.validate_session_token(azure_ad_token)
            if azure_ad_payload:
                logger.info(
                    f"✅ Azure AD token valid",
                    request=request,
                    person_id=azure_ad_payload.get("sub"),
                    extra={
                        "email": azure_ad_payload.get("email"),
                        "auth_source": "azure_ad",
                    },
                )
                is_authenticated = True

                # Add person ID to request state
                request.state.person_id = azure_ad_payload["sub"]
                request.state.auth_source = "azure_ad"
                request.state.user_email = azure_ad_payload.get("email")
                request.state.user_name = azure_ad_payload.get("name")

                # Check if there's a person ID cookie that should override the Azure AD user ID
                person_id_cookie = request.cookies.get(OVERRIDE_PERSON_ID_COOKIE_NAME)
                if person_id_cookie:
                    logger.info(
                        f"✅ {OVERRIDE_PERSON_ID_COOKIE_NAME} cookie found, overriding person ID",
                        request=request,
                        person_id=azure_ad_payload.get("sub"),
                        override_person_id=person_id_cookie,
                    )
                    request.state.override_person_id = person_id_cookie
                else:
                    request.state.override_person_id = None
            else:
                logger.warning(f"⚠️ Azure AD token validation failed", request=request)

        return is_authenticated

    async def get_current_person(
        self,
        request: Request,
    ) -> UUID:
        """Get the current authenticated person from the request state.

        This function can be used as a FastAPI dependency to get the current person
        based on the authentication state. It implements the following logic:
        1. Check if person_id is set in the request state (required)
        2. If override_person_id is available, current_person is set to it
        3. Otherwise, current_person is set to person_id

        Args:
            request (Request): The FastAPI request object

        Returns:
            str: The current person ID to use for the request

        Raises:
            HTTPException: If no person_id is found in the request state
        """
        # Check if person_id is set in the request state (required)
        if not hasattr(request.state, "person_id"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # If override_person_id is available, use it as current_person
        if (
            hasattr(request.state, "override_person_id")
            and request.state.override_person_id is not None
        ):
            return UUID(request.state.override_person_id)

        # Otherwise, use person_id as current_person
        return UUID(request.state.person_id)

    async def get_auth_source(
        self,
        request: Request,
    ) -> str:
        """
        Get the current authenticated person's authentication source from the request state.

        This function can be used as a FastAPI dependency to get the current authentication source
        based on the authentication state. It implements the following logic:
        1. Check if auth_source is set in the request state (required)
        2. If override_auth_source is available, auth_source is set to it
        3. Otherwise, auth_source is set to auth_source

        Args:
            request (Request): The FastAPI request object

        Returns:
            str: The current authentication source to use for the request

        Raises:
            HTTPException: If no auth_source is found in the request state
        """
        # Check if auth_source is set in the request state (required)
        if not hasattr(request.state, "auth_source"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # If override_auth_source is available, use it
        if (
            hasattr(request.state, "override_auth_source")
            and request.state.override_auth_source is not None
        ):
            return request.state.override_auth_source

        # Otherwise, use auth_source
        return request.state.auth_source

    async def verify_registration_challenge(
        self,
        request: RegistrationVerifyRequest,
        request_obj: Request,
        response: Response,
        device_info: DeviceInfo,
        credential_container: CosmosContainer,
        people_container: CosmosContainer,
        session_tokens_container: CosmosContainer,
        mode: ChallengeMode,
    ) -> RegistrationVerifyResponse:
        """Verify a WebAuthn registration challenge.

        Args:
            request (RegistrationVerifyRequest): The registration verification request
            request_obj (Request): The FastAPI request object
            device_info (DeviceInfo): The device information
            credential_container: The passkey credentials container dependency
            people_container: The people container dependency
            session_tokens_container: The session tokens container dependency
            _ (None): Rate limit check dependency

        Returns:
            RegistrationVerifyResponse: The registration verification response
        """
        logger.info(
            f"🔐 {'Registration' if mode == ChallengeMode.REGISTRATION else 'Recovery'} verification request",
            request=request_obj,
            extra={"client_ip": device_info.ip, "user_agent": device_info.user_agent},
        )

        invite_token = token_service.validate_invite_token(request.token, mode)

        # Verify challenge
        credential: PasskeyCredential = await challenge_service.verify_challenge(
            mode=mode,
            credential_data=request.credential,
            device_info=device_info,
        )
        # Save credential to database
        # credential_container.replace_model(model=credential)
        credential_container.create_model(model=credential)
        # Mark recovery token as used
        token_service.mark_token_as_used(request.token, mode)
        logger.info(f"🔑 Token marked as used: {request.token}")
        # Update user record to indicate they have a passkey
        update_fields = {
            "has_passkey": True,
            "primary_identifier_type": invite_token.identifier_type,
            "primary_identifier_value": invite_token.identifier_value,
        }
        if mode == ChallengeMode.REGISTRATION:
            update_fields["member_since"] = now()
        people_container.patch_model(
            item=invite_token.person_id,
            partition_key=invite_token.person_id,
            update_fields=update_fields,
            model_class=Person,
        )
        logger.info(f"👤 User record updated: {invite_token.person_id}")
        # Create and set session token
        logger.debug(
            f"🔧 Creating and setting session token for {invite_token.person_id}"
        )
        session_token = await token_service.create_token(
            person_id=invite_token.person_id,
            device_info=device_info,
            credential_id=credential.id,
        )
        logger.debug(f"🔧 Created session token: {session_token}")
        # Store session token
        session_tokens_container.create_model(session_token)

        # Set cookie
        response.set_cookie(
            key=SESSION_TOKEN_COOKIE_NAME,
            value=session_token.token,
            httponly=True,  # JavaScript cannot access the cookie
            secure=True,  # Requires HTTPS
            samesite="strict",  # Only sent in same-site navigation (no cross-origin)
            max_age=JWT_EXPIRATION_SECONDS,
        )

        logger.info(
            f"✅ {'Registration' if mode == ChallengeMode.REGISTRATION else 'Recovery'} successful",
            request=request_obj,
            person_id=invite_token.person_id,
        )

        return RegistrationVerifyResponse(
            credential_id=credential.id,
            session_token=session_token.token,
            success=True,
            message=f"{'Registration' if mode == ChallengeMode.REGISTRATION else 'Recovery'} successful",
            person_id=invite_token.person_id,
        )


# Create a global instance of the auth service
auth_service = AuthService()
