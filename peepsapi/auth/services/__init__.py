"""Services package for authentication.

This package contains service modules for authentication functionality.
"""

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.azure_ad_service import azure_ad_service
from peepsapi.auth.services.challenge_service import challenge_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.rate_limiter import (
    login_rate_limiter,
    registration_rate_limiter,
)
from peepsapi.auth.services.security_service import audit_logger, brute_force_protection
from peepsapi.auth.services.token_service import token_service
from peepsapi.auth.services.user_activity_service import user_activity_service

__all__ = [
    "auth_service",
    "azure_ad_service",
    "challenge_service",
    "device_service",
    "login_rate_limiter",
    "registration_rate_limiter",
    "audit_logger",
    "brute_force_protection",
    "token_service",
    "user_activity_service",
]
