"""Rate limiter for authentication endpoints.

This module provides rate limiting functionality for authentication endpoints
to prevent brute force attacks and abuse.
"""

import logging
import time
from collections import defaultdict
from datetime import timedelta
from typing import Dict, List, Optional, Tuple

from fastapi import HTTPException, Request, status

from peepsapi.auth.services.security_service import audit_logger
from peepsapi.models import UTCDateTime, now
from peepsapi.models.datetime import to_iso_string
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class RateLimiter:
    """Rate limiter for authentication endpoints.

    This class implements a simple in-memory rate limiter that tracks
    requests by IP address and/or user ID.
    """

    def __init__(
        self, name: str = "default", window_seconds: int = 60, max_requests: int = 10
    ):
        """Initialize the rate limiter.

        Args:
            name (str): Name of this rate limiter instance for logging and monitoring
            window_seconds (int): The time window in seconds
            max_requests (int): The maximum number of requests allowed in the window
        """
        self.name = name
        self.window_seconds = window_seconds
        self.max_requests = max_requests
        self.ip_requests: Dict[str, List[float]] = defaultdict(list)
        self.user_requests: Dict[str, List[float]] = defaultdict(list)
        self.blocked_ips: Dict[str, UTCDateTime] = {}
        self.blocked_users: Dict[str, UTCDateTime] = {}
        self.block_duration = timedelta(minutes=15)

        # Statistics
        self.total_requests = 0
        self.blocked_requests = 0
        self.last_reset = now()

    def _clean_old_requests(self, requests: List[float]) -> List[float]:
        """Remove requests older than the window.

        Args:
            requests (List[float]): The list of request timestamps

        Returns:
            List[float]: The filtered list of request timestamps
        """
        now = time.time()
        return [r for r in requests if now - r < self.window_seconds]

    def is_rate_limited(
        self,
        request: Request,
        user_id: Optional[str] = None,
        endpoint: Optional[str] = None,
    ) -> Tuple[bool, str]:
        """Check if a request is rate limited.

        Args:
            request (Request): The FastAPI request object
            user_id (Optional[str]): The user ID, if available
            endpoint (Optional[str]): The endpoint being accessed, for logging

        Returns:
            Tuple[bool, str]: A tuple of (is_limited, reason)
        """
        now_time = time.time()
        now_dt = now()
        client_ip = request.client.host if request.client else "127.0.0.1"
        user_agent = request.headers.get("user-agent", "Unknown")
        path = request.url.path if hasattr(request, "url") else endpoint or "unknown"

        # Update statistics
        self.total_requests += 1

        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            if now_dt < self.blocked_ips[client_ip]:
                remaining = (self.blocked_ips[client_ip] - now_dt).total_seconds()
                reason = f"IP address blocked for {int(remaining)} seconds"

                # Log the rate limit event
                logger.warning(
                    f"Rate limit exceeded: {reason}",
                    extra={
                        "rate_limiter": self.name,
                        "client_ip": client_ip,
                        "user_id": user_id,
                        "path": path,
                        "user_agent": user_agent,
                        "reason": reason,
                    },
                )

                # Log to audit log
                audit_logger.log_event(
                    event_type="rate_limit_exceeded",
                    user_id=user_id,
                    ip_address=client_ip,
                    user_agent=user_agent,
                    details={
                        "rate_limiter": self.name,
                        "path": path,
                        "reason": reason,
                        "remaining_seconds": int(remaining),
                    },
                    success=False,
                )

                self.blocked_requests += 1
                return True, reason
            else:
                del self.blocked_ips[client_ip]

        # Check if user is blocked
        if user_id and user_id in self.blocked_users:
            if now_dt < self.blocked_users[user_id]:
                remaining = (self.blocked_users[user_id] - now_dt).total_seconds()
                reason = f"User blocked for {int(remaining)} seconds"

                # Log the rate limit event
                logger.warning(
                    f"Rate limit exceeded: {reason}",
                    extra={
                        "rate_limiter": self.name,
                        "client_ip": client_ip,
                        "user_id": user_id,
                        "path": path,
                        "user_agent": user_agent,
                        "reason": reason,
                    },
                )

                # Log to audit log
                audit_logger.log_event(
                    event_type="rate_limit_exceeded",
                    user_id=user_id,
                    ip_address=client_ip,
                    user_agent=user_agent,
                    details={
                        "rate_limiter": self.name,
                        "path": path,
                        "reason": reason,
                        "remaining_seconds": int(remaining),
                    },
                    success=False,
                )

                self.blocked_requests += 1
                return True, reason
            else:
                del self.blocked_users[user_id]

        # Clean old requests
        self.ip_requests[client_ip] = self._clean_old_requests(
            self.ip_requests[client_ip]
        )
        if user_id:
            self.user_requests[user_id] = self._clean_old_requests(
                self.user_requests[user_id]
            )

        # Check IP rate limit
        if len(self.ip_requests[client_ip]) >= self.max_requests:
            self.blocked_ips[client_ip] = now_dt + self.block_duration
            reason = "Too many requests from this IP address"

            # Log the rate limit event
            logger.warning(
                f"Rate limit exceeded: {reason}",
                extra={
                    "rate_limiter": self.name,
                    "client_ip": client_ip,
                    "user_id": user_id,
                    "path": path,
                    "user_agent": user_agent,
                    "reason": reason,
                    "block_duration_minutes": self.block_duration.total_seconds() / 60,
                },
            )

            # Log to audit log
            audit_logger.log_event(
                event_type="rate_limit_exceeded",
                user_id=user_id,
                ip_address=client_ip,
                user_agent=user_agent,
                details={
                    "rate_limiter": self.name,
                    "path": path,
                    "reason": reason,
                    "request_count": len(self.ip_requests[client_ip]),
                    "max_requests": self.max_requests,
                    "window_seconds": self.window_seconds,
                    "block_duration_minutes": self.block_duration.total_seconds() / 60,
                },
                success=False,
            )

            self.blocked_requests += 1
            return True, reason

        # Check user rate limit
        if user_id and len(self.user_requests[user_id]) >= self.max_requests:
            self.blocked_users[user_id] = now_dt + self.block_duration
            reason = "Too many requests for this user"

            # Log the rate limit event
            logger.warning(
                f"Rate limit exceeded: {reason}",
                extra={
                    "rate_limiter": self.name,
                    "client_ip": client_ip,
                    "user_id": user_id,
                    "path": path,
                    "user_agent": user_agent,
                    "reason": reason,
                    "block_duration_minutes": self.block_duration.total_seconds() / 60,
                },
            )

            # Log to audit log
            audit_logger.log_event(
                event_type="rate_limit_exceeded",
                user_id=user_id,
                ip_address=client_ip,
                user_agent=user_agent,
                details={
                    "rate_limiter": self.name,
                    "path": path,
                    "reason": reason,
                    "request_count": len(self.user_requests[user_id]),
                    "max_requests": self.max_requests,
                    "window_seconds": self.window_seconds,
                    "block_duration_minutes": self.block_duration.total_seconds() / 60,
                },
                success=False,
            )

            self.blocked_requests += 1
            return True, reason

        # Add request to tracking
        self.ip_requests[client_ip].append(now_time)
        if user_id:
            self.user_requests[user_id].append(now_time)

        return False, ""

    def check_rate_limit(
        self,
        request: Request,
        user_id: Optional[str] = None,
        endpoint: Optional[str] = None,
    ) -> None:
        """Check if a request is rate limited and raise an exception if it is.

        Args:
            request (Request): The FastAPI request object
            user_id (Optional[str]): The user ID, if available
            endpoint (Optional[str]): The endpoint being accessed, for logging

        Raises:
            HTTPException: If the request is rate limited
        """
        is_limited, reason = self.is_rate_limited(request, user_id, endpoint)
        if is_limited:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=reason,
                headers={
                    "Retry-After": str(self.block_duration.total_seconds()),
                    "X-RateLimit-Limit": str(self.max_requests),
                    "X-RateLimit-Window": str(self.window_seconds),
                },
            )

    def get_statistics(self) -> Dict:
        """Get statistics for this rate limiter.

        Returns:
            Dict: Statistics for this rate limiter
        """
        current_time = now()
        uptime_seconds = (current_time - self.last_reset).total_seconds()

        # Count active blocks
        active_ip_blocks = sum(
            1 for exp in self.blocked_ips.values() if exp > current_time
        )
        active_user_blocks = sum(
            1 for exp in self.blocked_users.values() if exp > current_time
        )

        # Count tracked IPs and users
        tracked_ips = len(self.ip_requests)
        tracked_users = len(self.user_requests)

        # Calculate request rates
        requests_per_minute = (
            self.total_requests / (uptime_seconds / 60) if uptime_seconds > 0 else 0
        )
        blocks_per_minute = (
            self.blocked_requests / (uptime_seconds / 60) if uptime_seconds > 0 else 0
        )
        block_rate = (
            (self.blocked_requests / self.total_requests * 100)
            if self.total_requests > 0
            else 0
        )

        return {
            "name": self.name,
            "window_seconds": self.window_seconds,
            "max_requests": self.max_requests,
            "block_duration_minutes": self.block_duration.total_seconds() / 60,
            "total_requests": self.total_requests,
            "blocked_requests": self.blocked_requests,
            "active_ip_blocks": active_ip_blocks,
            "active_user_blocks": active_user_blocks,
            "tracked_ips": tracked_ips,
            "tracked_users": tracked_users,
            "uptime_seconds": uptime_seconds,
            "requests_per_minute": round(requests_per_minute, 2),
            "blocks_per_minute": round(blocks_per_minute, 2),
            "block_rate_percent": round(block_rate, 2),
            "last_reset": to_iso_string(self.last_reset),
        }

    def reset_statistics(self) -> None:
        """Reset statistics for this rate limiter."""
        self.total_requests = 0
        self.blocked_requests = 0
        self.last_reset = now()


# Global rate limiter instances
auth_rate_limiter = RateLimiter(name="auth", window_seconds=60, max_requests=5)
registration_rate_limiter = RateLimiter(
    name="registration", window_seconds=300, max_requests=10
)
login_rate_limiter = RateLimiter(name="login", window_seconds=60, max_requests=5)

# List of all rate limiters for monitoring
all_rate_limiters = [
    auth_rate_limiter,
    registration_rate_limiter,
    login_rate_limiter,
]


def get_all_rate_limiter_statistics() -> Dict:
    """Get statistics for all rate limiters.

    Returns:
        Dict: Statistics for all rate limiters
    """
    stats = {
        "rate_limiters": [limiter.get_statistics() for limiter in all_rate_limiters],
        "total_requests": sum(limiter.total_requests for limiter in all_rate_limiters),
        "total_blocked_requests": sum(
            limiter.blocked_requests for limiter in all_rate_limiters
        ),
        "generated_at": to_iso_string(now()),
    }

    # Calculate overall block rate
    if stats["total_requests"] > 0:
        stats["overall_block_rate_percent"] = round(
            (stats["total_blocked_requests"] / stats["total_requests"]) * 100, 2
        )
    else:
        stats["overall_block_rate_percent"] = 0

    return stats


def reset_all_rate_limiter_statistics() -> None:
    """Reset statistics for all rate limiters."""
    for limiter in all_rate_limiters:
        limiter.reset_statistics()
