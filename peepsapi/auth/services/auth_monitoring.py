"""Authentication monitoring service.

This module provides functionality for monitoring authentication activities,
including tracking login attempts, success rates, and authentication methods.
It complements the audit_logger by providing aggregated statistics and analytics.
"""

from datetime import timedelta
from typing import Dict
from uuid import UUID

from fastapi import Depends, HTTPException, status

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.rate_limiter import get_all_rate_limiter_statistics
from peepsapi.models import now, to_iso_string
from peepsapi.services.cosmos_containers import get_audit_logs_container
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class AuthMonitoring:
    """Service for monitoring authentication activities.

    This service provides analytics and statistics based on authentication events.
    It complements the audit_logger by aggregating data and identifying patterns.
    """

    def __init__(self):
        """Initialize the authentication monitoring service."""
        # Initialize the audit logs container
        self.audit_logs_container = get_audit_logs_container()

        # Cache for analytics to avoid frequent database queries
        self.cache_expiry = now()
        self.cache_ttl = timedelta(minutes=5)  # Cache data for 5 minutes
        self.cached_stats = None

        logger.info("🛠️ Authentication monitoring service initialized")

    def get_statistics(self) -> Dict:
        """Get authentication statistics from audit logs.

        Returns:
            Dict: Authentication statistics
        """
        # Check if we have a valid cached result
        if self.cached_stats and now() < self.cache_expiry:
            return self.cached_stats

        # Query audit logs for authentication events
        try:
            # Get events from the last 24 hours
            time_24h_ago = to_iso_string(now() - timedelta(hours=24))

            # Query for authentication attempts
            query = f"""
            SELECT * FROM c
            WHERE c.event_type = 'authentication_attempt'
            AND c.timestamp > '{time_24h_ago}'
            """

            auth_events = list(
                self.audit_logs_container.query_items(
                    query=query, enable_cross_partition_query=True
                )
            )

            # Aggregate statistics
            auth_methods = {}
            success_by_method = {}
            failure_by_method = {}
            hourly_stats = {}
            ip_stats = {}
            recent_events = []

            for event in auth_events:
                # Extract auth method (default to "passkey" for backward compatibility)
                auth_method = event.get("details", {}).get("auth_method", "passkey")

                # Count by method
                if auth_method not in auth_methods:
                    auth_methods[auth_method] = 0
                    success_by_method[auth_method] = 0
                    failure_by_method[auth_method] = 0

                auth_methods[auth_method] += 1

                # Count successes and failures
                if event.get("success", False):
                    success_by_method[auth_method] += 1
                else:
                    failure_by_method[auth_method] += 1

                # Track by hour
                timestamp = event.get("timestamp", "")
                hour = timestamp[:13] + ":00:00" if timestamp else ""

                if hour:
                    if hour not in hourly_stats:
                        hourly_stats[hour] = {"total": 0, "success": 0, "failure": 0}

                    hourly_stats[hour]["total"] += 1
                    if event.get("success", False):
                        hourly_stats[hour]["success"] += 1
                    else:
                        hourly_stats[hour]["failure"] += 1

                # Track by IP
                ip = event.get("ip_address", "unknown")
                if ip not in ip_stats:
                    ip_stats[ip] = {"attempts": 0, "failures": 0}

                ip_stats[ip]["attempts"] += 1
                if not event.get("success", False):
                    ip_stats[ip]["failures"] += 1

                # Add to recent events
                recent_events.append(
                    {
                        "timestamp": event.get("timestamp", ""),
                        "auth_method": auth_method,
                        "success": event.get("success", False),
                        "ip_address": event.get("ip_address", "unknown"),
                        "identifier": event.get(
                            "email", event.get("phone_number", "unknown")
                        ),
                    }
                )

            # Calculate success rates
            success_rates = {}
            for method, attempts in auth_methods.items():
                if attempts > 0:
                    success_rates[method] = (success_by_method[method] / attempts) * 100
                else:
                    success_rates[method] = 0

            # Identify suspicious IPs (high failure rate)
            suspicious_ips = []
            for ip, stats in ip_stats.items():
                attempts = stats["attempts"]
                failures = stats["failures"]
                if (
                    attempts >= 5 and failures / attempts > 0.5
                ):  # More than 50% failure rate
                    suspicious_ips.append(
                        {
                            "ip_address": ip,
                            "attempts": attempts,
                            "failures": failures,
                            "failure_rate": (failures / attempts) * 100,
                        }
                    )

            # Sort recent events by timestamp (newest first)
            recent_events.sort(key=lambda x: x.get("timestamp", ""), reverse=True)

            # Get rate limiting statistics
            rate_limit_stats = get_all_rate_limiter_statistics()

            # Get rate limit events from the last 24 hours
            query = f"""
            SELECT * FROM c
            WHERE c.event_type = 'rate_limit_exceeded'
            AND c.timestamp > '{time_24h_ago}'
            """

            rate_limit_events = list(
                self.audit_logs_container.query_items(
                    query=query, enable_cross_partition_query=True
                )
            )

            # Process rate limit events
            rate_limit_by_limiter = {}
            rate_limit_by_path = {}
            recent_rate_limit_events = []

            for event in rate_limit_events:
                # Extract rate limiter name
                limiter_name = event.get("details", {}).get("rate_limiter", "unknown")
                path = event.get("details", {}).get("path", "unknown")

                # Count by limiter
                if limiter_name not in rate_limit_by_limiter:
                    rate_limit_by_limiter[limiter_name] = 0
                rate_limit_by_limiter[limiter_name] += 1

                # Count by path
                if path not in rate_limit_by_path:
                    rate_limit_by_path[path] = 0
                rate_limit_by_path[path] += 1

                # Add to recent events
                recent_rate_limit_events.append(
                    {
                        "timestamp": event.get("timestamp", ""),
                        "rate_limiter": limiter_name,
                        "path": path,
                        "ip_address": event.get("ip_address", "unknown"),
                        "user_id": event.get("user_id", "unknown"),
                        "reason": event.get("details", {}).get("reason", "unknown"),
                    }
                )

            # Sort recent events by timestamp (newest first)
            recent_rate_limit_events.sort(
                key=lambda x: x.get("timestamp", ""), reverse=True
            )

            # Create result
            result = {
                "authentication": {
                    "total_attempts": auth_methods,
                    "successful_attempts": success_by_method,
                    "failed_attempts": failure_by_method,
                    "success_rates": success_rates,
                    "recent_events": recent_events[:10],  # Last 10 events
                    "suspicious_ips": suspicious_ips,
                    "hourly_stats": hourly_stats,
                },
                "rate_limiting": {
                    "statistics": rate_limit_stats,
                    "events_24h": len(rate_limit_events),
                    "by_limiter": rate_limit_by_limiter,
                    "by_path": rate_limit_by_path,
                    "recent_events": recent_rate_limit_events[:10],  # Last 10 events
                },
                "generated_at": to_iso_string(now()),
            }

            # Cache the result
            self.cached_stats = result
            self.cache_expiry = now() + self.cache_ttl

            return result

        except Exception as e:
            logger.error(f"❌ Error getting authentication statistics: {str(e)}")
            # Return empty statistics on error
            return {
                "error": f"Failed to retrieve statistics: {str(e)}",
                "total_attempts": {},
                "successful_attempts": {},
                "failed_attempts": {},
                "success_rates": {},
                "recent_events": [],
                "suspicious_ips": [],
                "hourly_stats": {},
                "generated_at": to_iso_string(now()),
            }

    async def get_statistics_protected(
        self, person_id: UUID = Depends(auth_service.get_current_person)
    ) -> Dict:
        """Get authentication statistics (protected endpoint).

        Args:
            person_id (UUID): The authenticated person's ID

        Returns:
            Dict: Authentication statistics

        Raises:
            HTTPException: If the user is not authenticated
        """
        if not person_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
            )

        return self.get_statistics()


# Create a singleton instance
auth_monitoring = AuthMonitoring()
