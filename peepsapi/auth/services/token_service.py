"""Token service for authentication.

This module provides functionality for managing authentication tokens,
including creation, validation, revocation, and tracking of token usage.
"""

import datetime
import secrets
import traceback
from typing import Dict, List, Optional
from uuid import UUID

import jwt
from fastapi import Request, Response

from peepsapi import config
from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import InviteToken, SessionToken
from peepsapi.auth.services.challenge_service import ChallengeMode
from peepsapi.auth.utils.constants import (
    JWT_ALGORITHM,
    JWT_EXPIRATION_DAYS,
    JWT_EXPIRATION_SECONDS,
    SESSION_TOKEN_COOKIE_NAME,
)
from peepsapi.models import UTCDateTime, now

# Using centralized container getters
from peepsapi.services.cosmos_containers import (
    get_invite_tokens_container,
    get_passkey_credentials_container,
    get_people_container,
    get_session_tokens_container,
)
from peepsapi.utils.logging import get_logger

# Configure logger
logger = get_logger(__name__)


class TokenService:
    """Service for token management operations."""

    def __init__(self):
        """Initialize the token service."""
        jwt_secret = config.get("JWT_SECRET")
        if not jwt_secret:
            raise ValueError("JWT_SECRET must be configured in environment variables")
        self.jwt_secret = jwt_secret
        self.session_tokens_container = get_session_tokens_container()
        self.invite_tokens_container = get_invite_tokens_container()
        self.people_container = get_people_container()
        self.passkey_credentials_container = get_passkey_credentials_container()

    def find_token_by_id(
        self, token_id: str, mode: ChallengeMode
    ) -> InviteToken | None:
        """Find an invite token by its ID.

        Args:
            token_id (str): The ID of the token to find
            mode (ChallengeMode): The mode of the challenge

        Returns:
            InviteToken | None: The invite token if found, None otherwise
        """
        # Find token in database
        query = f"SELECT * FROM c WHERE c.token = @token AND c.is_recovery = @recovery"
        params = [
            {"name": "@token", "value": token_id},
            {"name": "@recovery", "value": mode == ChallengeMode.RECOVERY},
        ]

        try:
            # Execute the query
            try:
                invite_tokens: List[
                    InviteToken
                ] = self.invite_tokens_container.query_models(
                    query=query, parameters=params, model_class=InviteToken
                )
            except Exception as query_err:
                logger.error(
                    f"❌ Failed to execute token query: {query} and params: {params} - errors: {query_err}"
                )
                return None
            # Check if token was found
            if not invite_tokens:
                logger.debug(f"📝 No token found with value: {token_id[:5]}...")
                return None

            # Get token data
            try:
                invite_token: InviteToken = next(iter(invite_tokens))
                logger.debug(f"✅ Found token with ID: {invite_token.id[:5]}...")
            except Exception as token_err:
                logger.error(
                    f"❌ Failed to process token: {invite_token.id[:5]} error: {token_err}"
                )
                return None
        except Exception as e:
            logger.error(f"❌ Unexpected error querying token database: {e}")
            logger.error(f"❌ Token exception type: {type(e)}")
            logger.error(f"❌ Token traceback: {traceback.format_exc()}")
            return None

        return invite_token

    def validate_invite_token(
        self, token: str, mode: ChallengeMode
    ) -> InviteToken | None:
        """Validate an invite registration or recovery token.

        Args:
            token (str): The token to validate
            mode (ChallengeMode): The mode of the challenge

        Returns:
            (InviteToken, str) | None: The invite token and person ID if token is valid, None otherwise
        """
        logger.debug(
            f"🧪 Validate {'recovery' if mode == ChallengeMode.RECOVERY else 'registration'} token called with token: {token[:5]}..."
        )

        invite_token = self.find_token_by_id(token, mode)

        if not invite_token:
            return None

        # Check if token is expired or used
        try:
            current_time = now()

            # Check expiration
            try:
                if invite_token.expires_at < current_time:
                    logger.debug(
                        f"⚠️ Token expired at {invite_token.expires_at.isoformat()}, current time is {current_time.isoformat()}"
                    )
                    return None
            except Exception as exp_err:
                logger.error(f"❌ Failed to check token expiration: {exp_err}")
                logger.error(f"❌ Token expiration value: {invite_token.expires_at}")
                return None

            # Check if token is used
            try:
                if invite_token.is_used:
                    logger.debug("⚠️ Token has already been used")
                    return None
            except Exception as used_err:
                logger.error(f"❌ Failed to check if token is used: {used_err}")
                return None
        except Exception as validation_err:
            logger.error(f"❌ Unexpected error validating token: {validation_err}")
            return None
        return invite_token

    def mark_token_as_used(self, token: str, mode: ChallengeMode) -> bool:
        """Mark a token as used.

        Args:
            token (str): The token to mark as used
            mode (ChallengeMode): The mode of the challenge

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.debug(
                f"🎯 Marking {'recovery' if mode == ChallengeMode.RECOVERY else 'registration'} token as used: {token[:5]}..."
            )

            invite_token: InviteToken | None = self.find_token_by_id(token, mode)

            if not invite_token:
                return False

            try:
                # Check if token is already used
                if invite_token.is_used:
                    logger.debug(
                        f"📝 Token is already marked as used: {invite_token.id}"
                    )
                    return True  # Already used, so return success

                # Save updated token to database
                try:
                    # We're using a dictionary here, not a Pydantic model, so we use replace_item
                    self.invite_tokens_container.patch_model(
                        item=invite_token.id,
                        partition_key=invite_token.id,
                        update_fields={"is_used": True, "used_at": now().isoformat()},
                        model_class=InviteToken,
                    )
                    logger.debug(f"✅ Token marked as used: {invite_token.id}")
                except Exception as update_err:
                    logger.error(
                        f"❌ Failed to update token: {invite_token.id} as used, error: {update_err}"
                    )
                    return False

                return True
            except Exception as token_err:
                logger.error(
                    f"❌ Failed to process token data for marking as used: {token_err}"
                )
                return False
        except Exception as e:
            logger.error(f"❌ Unexpected error marking token as used: {e}")
            logger.error(f"❌ Exception type: {type(e)}")
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False

    async def create_token(
        self,
        person_id: UUID,
        device_info: DeviceInfo,
        credential_id: Optional[str] = None,
        additional_claims: Optional[Dict] = None,
    ) -> SessionToken:
        """Create a new JWT session token.

        Args:
            person_id (str): The person's ID
            device_info (DeviceInfo): Information about the user's device
            credential_id (Optional[str]): The credential ID to associate with this token
            additional_claims (Optional[Dict]): Additional claims to include in the token

        Returns:
            SessionToken: The session record
        """
        # Current time and expiration time
        current_time = now()
        expires_at = current_time + datetime.timedelta(days=JWT_EXPIRATION_DAYS)

        # Ensure we have a credential_id if it's in device_info
        final_credential_id = credential_id or getattr(
            device_info, "credential_id", None
        )

        # Create token payload
        payload = {
            "sub": str(person_id),
            "jti": secrets.token_hex(16),
            "iat": current_time.timestamp(),
            "exp": expires_at.timestamp(),
        }

        # Add any additional claims
        if additional_claims:
            payload.update(additional_claims)

        # Create JWT token
        token = jwt.encode(payload, self.jwt_secret, algorithm=JWT_ALGORITHM)

        # If we have a credential_id, ensure it's also set in device_info for consistency
        if final_credential_id and not getattr(device_info, "credential_id", None):
            device_info.credential_id = final_credential_id

        # Create session record
        session = SessionToken(
            id=payload["jti"],
            person_id=person_id,
            token=token,
            device_info=device_info,
            credential_id=final_credential_id,
            created_at=current_time,
            expires_at=expires_at,
            is_active=True,
        )
        return session

    async def create_and_set_token(
        self,
        response: Response,
        person_id: UUID,
        device_info: DeviceInfo,
        login_time: Optional[UTCDateTime] = None,
        session_tokens_container=None,
        credential_id: Optional[str] = None,
        additional_claims: Optional[Dict] = None,
    ) -> SessionToken:
        """Create a session token and set it as a cookie.

        Args:
            response (Response): The FastAPI response object
            person_id (str): The person's ID
            device_info (DeviceInfo): Information about the user's device
            login_time (Optional[UTCDateTime]): The login time to record
            session_tokens_container: Optional container for session tokens
            credential_id (Optional[str]): The credential ID to associate with this token
            additional_claims (Optional[Dict]): Additional claims to include in the token

        Returns:
            SessionToken: The JWT session record
        """
        # Create session token
        session = await self.create_token(
            person_id, device_info, credential_id, additional_claims
        )

        # Record login time if provided
        if login_time:
            session.login_time = login_time

        # Use provided container or default
        container = session_tokens_container or self.session_tokens_container

        # Save session to database using create_model
        container.create_model(session)

        # Set session token cookie
        response.set_cookie(
            key=SESSION_TOKEN_COOKIE_NAME,
            value=session.token,
            httponly=True,  # JavaScript cannot access the cookie
            secure=True,  # Requires HTTPS
            samesite="strict",  # Only sent in same-site navigation (no cross-origin)
            max_age=JWT_EXPIRATION_SECONDS,  # Already converted to seconds in constants
        )

        return session

    async def validate_token(self, token: str) -> Optional[Dict]:
        """Validate a JWT token.

        Args:
            token (str): The token to validate

        Returns:
            Optional[Dict]: The decoded token payload if valid, None otherwise
        """
        try:
            # Decode and validate the token
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=[JWT_ALGORITHM],
                options={"verify_signature": True},
            )

            # Check if token is expired
            exp = payload.get("exp")
            if not exp or UTCDateTime.from_timestamp(exp) < now():
                logger.warning(f"❌ Token expired", extra={"exp": exp})
                return None

            # Check if token has been revoked
            token_id = payload.get("jti")
            if token_id:
                query = "SELECT * FROM c WHERE c.id = @id AND c.is_revoked = true"
                params = [{"name": "@id", "value": token_id}]

                try:
                    revoked_tokens = self.session_tokens_container.query_models(
                        query=query,
                        parameters=params,
                        model_class=SessionToken,
                    )
                    if revoked_tokens:
                        logger.warning(
                            f"❌ Token has been revoked", extra={"token_id": token_id}
                        )
                        return None
                except Exception as e:
                    logger.error(f"❌ Error checking token revocation: {str(e)}")
                    return None

            return payload

        except jwt.InvalidTokenError as e:
            logger.warning(f"❌ Invalid token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ Error validating token: {str(e)}")
            return None

    async def decode_token(self, token: str) -> Optional[Dict]:
        """Decode a JWT token without validation.

        Args:
            token (str): The JWT token to decode

        Returns:
            Optional[Dict]: The decoded token payload if valid, None otherwise
        """
        try:
            return jwt.decode(token, self.jwt_secret, algorithms=[JWT_ALGORITHM])
        except jwt.InvalidTokenError:
            return None

    async def update_token_usage(self, token: str, request: Request) -> bool:
        """Update the last_used_at field for a token.

        Args:
            token (str): The JWT token
            request (Request): The FastAPI request object

        Returns:
            bool: True if the token was updated, False otherwise
        """
        try:
            # Decode the token to get the jti
            payload = jwt.decode(token, self.jwt_secret, algorithms=[JWT_ALGORITHM])
            token_id = payload.get("jti")

            if not token_id:
                logger.warning("Token does not have a jti claim")
                return False

            # Find the token in the database
            query = "SELECT * FROM c WHERE c.id = @token_id"
            params = [{"name": "@token_id", "value": token_id}]

            token_results = list(
                self.session_tokens_container.query_items(
                    query=query,
                    parameters=params,
                    enable_cross_partition_query=True,
                )
            )

            if not token_results:
                logger.warning(f"Token {token_id} not found in database")
                return False

            # Update the token
            token_data = token_results[0]
            token_data["last_used_at"] = now().isoformat()

            # Add IP and user agent to device_info if not already present
            if "device_info" not in token_data:
                token_data["device_info"] = {}

            # Update IP and user agent if they've changed
            client_ip = request.client.host if request.client else "127.0.0.1"
            user_agent = request.headers.get("user-agent", "Unknown")

            if token_data["device_info"].get("ip") != client_ip:
                token_data["device_info"]["last_ip"] = token_data["device_info"].get(
                    "ip"
                )
                token_data["device_info"]["ip"] = client_ip

            if token_data["device_info"].get("user_agent") != user_agent:
                token_data["device_info"]["last_user_agent"] = token_data[
                    "device_info"
                ].get("user_agent")
                token_data["device_info"]["user_agent"] = user_agent

            # Save the updated token
            self.session_tokens_container.replace_item(token_id, token_data)
            return True
        except Exception as e:
            logger.error(f"❌ Error updating token usage", extra={"error": str(e)})
            return False

    async def revoke_token(self, token: str) -> bool:
        """Revoke a specific token.

        Args:
            token (str): The JWT token to revoke

        Returns:
            bool: True if the token was revoked, False otherwise
        """
        try:
            # Decode the token to get the jti
            payload = jwt.decode(token, self.jwt_secret, algorithms=[JWT_ALGORITHM])
            token_id = payload.get("jti")
            person_id = payload.get("sub")

            if not token_id:
                logger.warning("Token does not have a jti claim")
                return False

            # Find the token in the database
            query = "SELECT * FROM c WHERE c.id = @token_id"
            params = [{"name": "@token_id", "value": token_id}]

            token_results = list(
                self.session_tokens_container.query_items(
                    query=query,
                    parameters=params,
                    enable_cross_partition_query=True,
                )
            )

            if not token_results:
                logger.warning(f"Token {token_id} not found in database")
                return False

            # Update the token
            token_data = token_results[0]
            token_data["is_active"] = False

            # Get the device ID if available
            device_id = token_data.get("credential_id")

            # Save the updated token
            self.session_tokens_container.replace_item(token_id, token_data)
            logger.info(f"Token {token_id} revoked successfully")

            # If we have a device ID and person ID, check if this was the only token for this device
            if device_id and person_id:
                try:
                    # Check if there are any other active tokens for this device
                    query = """
                    SELECT COUNT(1) as count FROM c
                    WHERE c.person_id = @person_id
                    AND c.is_active = true
                    AND c.credential_id = @device_id
                    """
                    params = [
                        {"name": "@person_id", "value": person_id},
                        {"name": "@device_id", "value": device_id},
                    ]

                    count_results = list(
                        self.session_tokens_container.query_items(
                            query=query,
                            parameters=params,
                            enable_cross_partition_query=True,
                        )
                    )

                    active_token_count = (
                        count_results[0]["count"] if count_results else 0
                    )

                    # If no active tokens remain, mark the device as inactive
                    if active_token_count == 0:
                        passkey_credentials_container = (
                            get_passkey_credentials_container()
                        )

                        # Use a query instead of direct read to avoid partition key issues
                        query = "SELECT * FROM c WHERE c.id = @device_id"
                        params = [{"name": "@device_id", "value": device_id}]

                        # Execute the query
                        device_results = list(
                            passkey_credentials_container.query_items(
                                query=query,
                                parameters=params,
                                enable_cross_partition_query=True,
                            )
                        )

                        # Check if we found the device
                        if not device_results:
                            logger.warning(f"Device not found: {device_id}")
                        else:
                            # Device found
                            device_data = device_results[0]

                            # Only update if it belongs to the correct person
                            if device_data["person_id"] == person_id:
                                # Mark as inactive
                                device_data["is_active"] = False
                                passkey_credentials_container.replace_item(
                                    device_data["id"], device_data
                                )
                                logger.info(
                                    f"Marked device {device_id} as inactive - no active tokens remain"
                                )
                            else:
                                logger.warning(
                                    f"Device {device_id} belongs to person {device_data['person_id']}, not {person_id}"
                                )
                except Exception as device_error:
                    logger.error(
                        f"❌ Error updating device status",
                        extra={"error": str(device_error)},
                    )
                    # Continue even if device update fails - token revocation is the primary goal

            return True
        except Exception as e:
            logger.error(f"❌ Error revoking token", extra={"error": str(e)})
            return False

    async def revoke_all_tokens(self, person_id: UUID) -> int:
        """Revoke all tokens for a person.

        Args:
            person_id (UUID): The person's ID

        Returns:
            int: The number of tokens revoked
        """
        try:
            # Find all tokens for the person
            query = (
                "SELECT * FROM c WHERE c.person_id = @person_id AND c.is_active = true"
            )
            params = [{"name": "@person_id", "value": str(person_id)}]

            token_results = list(
                self.session_tokens_container.query_items(
                    query=query,
                    parameters=params,
                    enable_cross_partition_query=True,
                )
            )

            if not token_results:
                logger.info(f"No active tokens found for person {person_id}")
                return 0

            # Revoke all tokens
            count = 0
            for token_data in token_results:
                token_data["is_active"] = False
                self.session_tokens_container.replace_item(token_data["id"], token_data)
                count += 1

            # Also mark all devices as inactive in the passkey_credentials container
            try:
                # Find all devices for the person
                query = "SELECT * FROM c WHERE c.person_id = @person_id AND c.is_active = true"
                params = [{"name": "@person_id", "value": person_id}]

                device_results = list(
                    self.passkey_credentials_container.query_items(
                        query=query,
                        parameters=params,
                        enable_cross_partition_query=True,
                    )
                )

                # Mark all devices as inactive
                device_count = 0
                for device_data in device_results:
                    device_data["is_active"] = False
                    passkey_credentials_container.replace_item(
                        device_data["id"], device_data
                    )
                    device_count += 1

                logger.info(
                    f"Marked {device_count} devices as inactive for person {person_id}"
                )
            except Exception as device_error:
                logger.error(f"Error updating device status: {device_error}")
                # Continue even if device update fails - token revocation is the primary goal

            logger.info(f"Revoked {count} tokens for person {person_id}")
            return count
        except Exception as e:
            logger.error(
                f"❌ Error revoking all tokens for person {person_id}",
                extra={"error": str(e)},
            )
            return 0

    async def revoke_device_tokens(self, person_id: UUID, device_id: str) -> int:
        """Revoke all tokens for a specific device.

        Args:
            person_id (UUID): The person's ID
            device_id (str): The device ID (credential ID)

        Returns:
            int: The number of tokens revoked
        """
        try:
            # Find all tokens for the person and device using credential_id field
            query = """
            SELECT * FROM c
            WHERE c.person_id = @person_id
            AND c.is_active = true
            AND c.credential_id = @device_id
            """
            params = [
                {"name": "@person_id", "value": person_id},
                {"name": "@device_id", "value": device_id},
            ]

            token_results = list(
                self.session_tokens_container.query_items(
                    query=query,
                    parameters=params,
                    enable_cross_partition_query=True,
                )
            )

            if not token_results:
                logger.info(
                    f"No active tokens found for person {person_id} and device {device_id}"
                )
                return 0

            # Revoke all tokens
            count = 0
            for token_data in token_results:
                token_data["is_active"] = False
                try:
                    self.session_tokens_container.replace_item(
                        token_data["id"], token_data
                    )
                    count += 1
                except Exception as token_error:
                    logger.error(
                        f"❌ Error revoking token {token_data.get('id')}",
                        extra={"error": str(token_error)},
                    )

            # Also mark the device as inactive in the passkey_credentials container
            try:
                # Use a query instead of direct read to avoid partition key issues
                query = "SELECT * FROM c WHERE c.id = @device_id"
                params = [{"name": "@device_id", "value": device_id}]

                # Execute the query
                device_results = list(
                    self.passkey_credentials_container.query_items(
                        query=query,
                        parameters=params,
                        enable_cross_partition_query=True,
                    )
                )

                # Check if we found the device
                if not device_results:
                    logger.warning(f"Device not found: {device_id}")
                    return count  # Return the token count, device update is secondary

                # Device found
                device_data = device_results[0]

                # Only update if it belongs to the correct person
                if device_data["person_id"] == person_id:
                    # Mark as inactive
                    device_data["is_active"] = False
                    passkey_credentials_container.replace_item(
                        device_data["id"], device_data
                    )
                    logger.info(f"Marked device {device_id} as inactive")
                else:
                    logger.warning(
                        f"Device {device_id} belongs to person {device_data['person_id']}, not {person_id}"
                    )
            except Exception as device_error:
                logger.error(f"Error updating device status: {device_error}")
                # Continue even if device update fails - token revocation is the primary goal

            logger.info(
                f"Revoked {count} tokens for person {person_id} and device {device_id}"
            )
            return count
        except Exception as e:
            logger.error(
                f"❌ Error revoking tokens for device {device_id}",
                extra={"error": str(e)},
            )
            return 0

    async def _is_token_active(self, token_id: str) -> bool:
        """Check if a token is active.

        Args:
            token_id (str): The token ID (jti claim)

        Returns:
            bool: True if the token is active, False otherwise
        """
        if not token_id:
            return False

        try:
            # Find the token in the database
            query = "SELECT c.is_active FROM c WHERE c.id = @token_id"
            params = [{"name": "@token_id", "value": token_id}]

            token_results = list(
                self.session_tokens_container.query_items(
                    query=query,
                    parameters=params,
                    enable_cross_partition_query=True,
                )
            )

            if not token_results:
                return False

            return token_results[0].get("is_active", False)
        except Exception as e:
            logger.error(
                f"❌ Error checking if token is active", extra={"error": str(e)}
            )
            return False

    async def _generate_device_fingerprint(self, device_info: DeviceInfo) -> str:
        """Generate a device fingerprint from device information.

        Args:
            device_info (DeviceInfo): Information about the user's device

        Returns:
            str: A device fingerprint
        """
        # Extract device information
        os_info = device_info.os or "unknown"
        browser_info = device_info.browser or "unknown"
        type_info = device_info.type or "unknown"

        # Create a fingerprint from device information
        fingerprint_parts = [
            os_info,
            browser_info,
            type_info,
        ]

        # Add a salt to make it harder to forge
        fingerprint_parts.append(secrets.token_hex(8))

        # Join the parts and create a hash
        # We're not using the joined string directly to avoid exposing device info
        _ = ":".join(fingerprint_parts)  # This helps with future debugging
        return secrets.token_hex(16)


# Create a global instance of the token service
token_service = TokenService()
