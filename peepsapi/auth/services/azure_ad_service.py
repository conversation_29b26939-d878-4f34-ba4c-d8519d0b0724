"""Azure AD authentication service.

This module provides functionality for authenticating users with Azure AD.
It handles the OAuth 2.0 flow for Azure AD authentication and token validation.
"""

import asyncio
import json
from typing import Dict, Optional, Tuple

import aiohttp
import jwt
from fastapi import <PERSON>ie, HTTPException, Request, Response, status

from peepsapi import config
from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import SessionToken
from peepsapi.auth.services.token_service import TokenService
from peepsapi.auth.utils.constants import AZURE_AD_COOKIE_MAX_AGE, AZURE_AD_COOKIE_NAME
from peepsapi.models import now
from peepsapi.utils.logging import get_logger

# Note: Authentication events are logged by audit_logger
# The auth_monitoring service provides analytics based on these logs

logger = get_logger(__name__)


class AzureADService:
    """Service for Azure AD authentication."""

    def __init__(self):
        """Initialize the Azure AD service."""
        self.client_id = None
        self.client_secret = None
        self.tenant_id = None
        self.redirect_uri = None
        self.authority = None
        self.token_endpoint = None
        self.jwks_uri = None
        self.jwks = None
        self.initialized = False
        self.token_service = TokenService()
        # We'll initialize in init() instead
        self._initialization_lock = asyncio.Lock()

    async def init(self):
        """Initialize the Azure AD service with configuration values."""
        async with self._initialization_lock:
            if self.initialized:
                return

            try:
                # Get configuration values
                self.client_id = config.get("AZURE_AD_CLIENT_ID")
                self.client_secret = config.get("AZURE_AD_CLIENT_SECRET")
                self.tenant_id = config.get("AZURE_AD_TENANT_ID")

                # Check if all required values are present
                if not all([self.client_id, self.client_secret, self.tenant_id]):
                    logger.warning(
                        "Azure AD configuration is incomplete. SSO will be disabled."
                    )
                    return

                # Set up authority and endpoints
                self.authority = f"https://login.microsoftonline.com/{self.tenant_id}"
                self.token_endpoint = f"{self.authority}/oauth2/v2.0/token"

                # Get OpenID configuration
                async with aiohttp.ClientSession() as session:
                    openid_config_url = (
                        f"{self.authority}/.well-known/openid-configuration"
                    )
                    async with session.get(openid_config_url) as response:
                        response.raise_for_status()
                        openid_config = await response.json()

                    # Get JWKS URI for token validation
                    self.jwks_uri = openid_config.get("jwks_uri")
                    if self.jwks_uri:
                        async with session.get(self.jwks_uri) as jwks_response:
                            jwks_response.raise_for_status()
                            self.jwks = await jwks_response.json()

                self.initialized = True
                logger.info("✅ Azure AD service initialized successfully")
            except Exception as e:
                logger.error(
                    f"❌ Failed to initialize Azure AD service: {str(e)}",
                    extra={"error": str(e)},
                )
                # Set initialized to False to force retry on next request
                self.initialized = False
                raise

    async def get_login_url(self, request: Request, state: str = None) -> str:
        """Get the Azure AD login URL.

        Args:
            request (Request): The FastAPI request object
            state (str, optional): State parameter for CSRF protection

        Returns:
            str: The login URL
        """
        if not self.initialized:
            asyncio.run(self.init())

        if not self.initialized:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Azure AD SSO is not configured",
            )

        # Determine the redirect URI based on the request
        host = request.headers.get("host", "localhost")
        scheme = request.headers.get("x-forwarded-proto", "https")
        self.redirect_uri = f"{scheme}://{host}/auth/azure-ad/callback"

        # Build the authorization URL
        auth_url = (
            f"{self.authority}/oauth2/v2.0/authorize"
            f"?client_id={self.client_id}"
            f"&response_type=code"
            f"&redirect_uri={self.redirect_uri}"
            f"&response_mode=query"
            f"&scope=openid%20profile%20email"
        )

        # Add state parameter if provided
        if state:
            auth_url += f"&state={state}"

        return auth_url

    async def handle_callback(
        self, request: Request, response: Response
    ) -> Tuple[str, Dict]:
        """Handle the Azure AD callback.

        Args:
            request (Request): The FastAPI request object
            response (Response): The FastAPI response object

        Returns:
            Tuple[str, Dict]: The JWT token and user info

        Raises:
            HTTPException: If authentication fails
        """
        if not self.initialized:
            await self.init()

        if not self.initialized:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Azure AD SSO is not configured",
            )

        # Get the authorization code from the query parameters
        code = request.query_params.get("code")
        if not code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Authorization code is missing",
            )

        # Determine the redirect URI based on the request
        host = request.headers.get("host", "localhost")
        scheme = request.headers.get("x-forwarded-proto", "https")
        self.redirect_uri = f"{scheme}://{host}/auth/azure-ad/callback"

        # Exchange the authorization code for tokens
        try:
            token_data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "code": code,
                "redirect_uri": self.redirect_uri,
                "grant_type": "authorization_code",
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.token_endpoint, data=token_data
                ) as token_response:
                    token_response.raise_for_status()
                    tokens = await token_response.json()
                    # Extract the ID token
                    id_token = tokens.get("id_token")

                    if not id_token:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="ID token is missing from the response",
                        )

                    # Validate the ID token
                    user_info = await self.validate_token(id_token)

                    # Create a session token
                    session_token = await self.create_session_token(user_info)

                    # Set the session token as a cookie
                    response.set_cookie(
                        key=AZURE_AD_COOKIE_NAME,
                        value=session_token.token,
                        httponly=True,  # JavaScript cannot access the cookie
                        secure=True,  # Requires HTTPS
                        samesite="strict",  # Only sent in same-site navigation (no cross-origin)
                        max_age=AZURE_AD_COOKIE_MAX_AGE,
                    )
                    return session_token.token, user_info

        except aiohttp.ClientResponseError as e:
            logger.error(
                "❌ Failed to exchange authorization code for tokens", exc_info=e
            )
            # Failed authentication is logged by the callback handler

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to authenticate with Azure AD",
            )

    async def validate_token(self, token: str) -> Optional[Dict]:
        """Validate an Azure AD token.

        Args:
            token (str): The token to validate

        Returns:
            Optional[Dict]: The decoded token payload if valid, None otherwise
        """
        # Try to initialize if not already initialized
        if not self.initialized:
            try:
                await self.init()
            except Exception as e:
                logger.error(
                    f"❌ Failed to initialize Azure AD service during token validation: {str(e)}"
                )
                return None

        if not self.initialized:
            return None

        try:
            # Decode the token header to get the key ID
            header = jwt.get_unverified_header(token)
            logger.debug(f"🔧 Token header: {header}")

            if not header or "kid" not in header:
                logger.error("❌ Missing key ID in token header")
                return None

            # Find the matching key in JWKS
            key = None
            for jwk in self.jwks.get("keys", []):
                if jwk.get("kid") == header["kid"]:
                    key = jwk
                    break

            if not key:
                logger.error(f"❌ No matching key found for kid: {header.get('kid')}")
                return None

            # Convert JWK to PEM format
            public_key = jwt.algorithms.RSAAlgorithm.from_jwk(json.dumps(key))

            # Verify and decode the token
            logger.debug("🔧 Attempting to decode token with public key")
            payload = jwt.decode(
                token,
                public_key,
                algorithms=["RS256"],
                audience=self.client_id,
                issuer=f"{self.authority}/v2.0",
            )
            return payload

        except jwt.ExpiredSignatureError:
            logger.error("❌ Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.error(f"❌ Invalid token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ Error validating token: {str(e)}")
            return None

    async def validate_session_token(self, token: str) -> Optional[Dict]:
        """Validate a session token.

        Args:
            token (str): The session token to validate

        Returns:
            Optional[Dict]: The decoded token payload if valid, None otherwise
        """
        logger.debug(f"🔧 Validating session token: {token[:10]}...")
        try:
            # Decode and validate the token using token service
            payload = await self.token_service.validate_token(token)

            if not payload:
                return None

            # Check if the token is from Azure AD
            auth_source = payload.get("auth_source")
            if auth_source != "azure_ad":
                return None

            return payload
        except jwt.ExpiredSignatureError:
            logger.debug("⚠️ Session token has expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.debug(f"⚠️ Invalid session token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ Error validating session token: {str(e)}")
            return None

    async def get_current_user(
        self, azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME)
    ) -> Optional[Dict]:
        """Get the current user from the Azure AD session token.

        This function can be used as a FastAPI dependency to require Azure AD authentication
        for protected endpoints.

        Args:
            azure_ad_token (Optional[str]): The Azure AD session token cookie

        Returns:
            Optional[Dict]: The user information if authentication is successful, None otherwise
        """
        if not azure_ad_token:
            logger.debug("🔧 No Azure AD token provided")
            return None

        logger.debug("🔧 Getting current user from session token")
        result = await self.validate_session_token(azure_ad_token)
        logger.debug(f"🔧 Session token validation result: {result}")
        return result

    async def create_session_token(self, user_info: Dict) -> SessionToken:
        """Create a session token for the user.

        Args:
            user_info (Dict): The user information from the ID token

        Returns:
            SessionToken: The session token
        """
        # Create device info for Azure AD login
        device_info = DeviceInfo(
            name="Azure AD Login",
            type="web",
            os="Unknown",
            browser="Unknown",
            ip="0.0.0.0",
            created_at=now(),
            last_used_at=now(),
            is_active=True,
        )
        # Use token service to create token with Azure AD specific claims
        session_token = await self.token_service.create_token(
            person_id=user_info.get("oid"),  # Use object ID as person_id
            device_info=device_info,
            additional_claims={
                "name": user_info.get("name"),
                "email": user_info.get("email") or user_info.get("preferred_username"),
                "auth_source": "azure_ad",
            },
        )

        return session_token


# Create a singleton instance
azure_ad_service = AzureADService()
