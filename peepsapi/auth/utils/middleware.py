"""Authentication middleware for FastAPI.

This module provides middleware for authenticating requests using JWT tokens
and Azure AD authentication, and managing user sessions.
"""

import time
from typing import Callable, List, Optional

from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse, RedirectResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON><PERSON><PERSON>pp

from peepsapi.auth.services.azure_ad_service import (
    AZURE_AD_COOKIE_NAME,
    azure_ad_service,
)
from peepsapi.auth.services.token_service import token_service
from peepsapi.auth.utils.constants import (
    OVERRIDE_AUTH_SOURCE_COOKIE_NAME,
    OVERRIDE_PERSON_ID_COOKIE_NAME,
    SESSION_TOKEN_COOKIE_NAME,
)
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


def is_path_match(path, pattern):
    """Check if a path matches a pattern more precisely than startswith.

    This ensures that entire path segments match, not just the beginning of the path.
    For example, /auth/invite should not match /auth/validate-invite.

    Args:
        path (str): The path to check
        pattern (str): The pattern to match against

    Returns:
        bool: True if the path matches the pattern, False otherwise
    """
    # If the pattern ends with a slash, it's a prefix match for a directory
    if pattern.endswith("/"):
        return path.startswith(pattern)

    # Otherwise, ensure the path either exactly matches the pattern
    # or the pattern is followed by a slash
    return path == pattern or path.startswith(pattern + "/")


class AuthMiddleware(BaseHTTPMiddleware):
    """Middleware for authenticating requests using JWT tokens or Azure AD.

    This middleware checks for JWT tokens in the Authorization header or
    session_token cookie and validates them. It also checks for Azure AD
    session tokens. It updates the request state with the authenticated
    user ID if available.
    """

    def __init__(
        self,
        app: ASGIApp,
        public_paths: List[str],
        exclude_path_prefixes: Optional[List[str]] = None,
    ):
        """Initialize the middleware.

        Args:
            app (ASGIApp): The ASGI application
            public_paths (List[str]): Paths that don't require authentication
            exclude_path_prefixes (Optional[List[str]]): Path prefixes to exclude from middleware processing
        """
        super().__init__(app)
        self.public_paths = public_paths
        self.exclude_path_prefixes = exclude_path_prefixes or []
        self._initialized = False

    async def _ensure_initialized(self):
        """Ensure Azure AD service is initialized."""
        if not self._initialized:
            try:
                await azure_ad_service.init()
                self._initialized = True
            except Exception as e:
                logger.error(f"❌ Failed to initialize auth middleware: {str(e)}")

    async def call_next(self, request: Request, call_next: Callable):
        """Process the request and measure processing time.

        Args:
            request (Request): The incoming request
            call_next (Callable): The next middleware or route handler

        Returns:
            Response: The response from the next middleware or route handler
        """
        # Process the request and get the response
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)

        return response

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process the request through the middleware.

        Args:
            request (Request): The incoming request
            call_next (Callable): The next middleware or route handler

        Returns:
            Response: The response from the next middleware or route handler
        """
        # Skip middleware for excluded path prefixes
        path = request.url.path
        if any(path.startswith(excluded) for excluded in self.exclude_path_prefixes):
            logger.info(f"📝 Skipping middleware for excluded path: {path}")
            return await self.call_next(request, call_next)

        # Ensure services are initialized
        await self._ensure_initialized()

        # Try JWT authentication first (highest priority)
        jwt_token = None
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            jwt_token = auth_header.replace("Bearer ", "")
        else:
            jwt_token = request.cookies.get(SESSION_TOKEN_COOKIE_NAME)

        if jwt_token:
            logger.info(f"🔑 Attempting JWT authentication for {path}")
            # Validate JWT token
            payload = await token_service.validate_token(jwt_token)
            if payload:
                logger.info(f"✅ JWT authentication successful for {path}")
                # Add person ID to request state
                request.state.person_id = payload["sub"]
                # For JWT auth, override_person_id is None
                request.state.override_person_id = None
                request.state.auth_source = "jwt"

                return await self.call_next(request, call_next)

        # Try Azure AD authentication if JWT authentication failed
        azure_ad_token = request.cookies.get(AZURE_AD_COOKIE_NAME)

        if azure_ad_token:
            logger.info(f"🔑 Attempting Azure AD authentication for {path}")
            # Validate Azure AD session token
            payload = await azure_ad_service.validate_session_token(azure_ad_token)

            if payload:
                logger.info(f"✅ Azure AD authentication successful for {path}")
                # Check if there's a person ID cookie that should override the Azure AD user ID
                # Only check for person ID cookie in the context of valid Azure AD auth
                person_id_cookie = request.cookies.get(OVERRIDE_PERSON_ID_COOKIE_NAME)
                override_auth_source_cookie = request.cookies.get(
                    OVERRIDE_AUTH_SOURCE_COOKIE_NAME
                )

                # Add person ID to request state
                request.state.person_id = payload["sub"]
                request.state.auth_source = "azure_ad"
                request.state.user_email = payload.get("email")
                request.state.user_name = payload.get("name")

                # Set override_person_id - use x-peeps-id cookie if present, otherwise None
                if person_id_cookie:
                    request.state.override_person_id = person_id_cookie
                else:
                    request.state.override_person_id = None

                # Set override_auth_source if x-peeps-auth-source cookie is present
                if override_auth_source_cookie:
                    request.state.override_auth_source = override_auth_source_cookie
                else:
                    request.state.override_auth_source = None

                return await self.call_next(request, call_next)

        # Allow public paths without authentication
        if any(path == public for public in self.public_paths):
            logger.info(f"📝 Allowing public path: {path}")
            return await self.call_next(request, call_next)

        # Failed authentication attempts are logged by audit_logger in other components

        # Determine if this is an API request or a UI request
        # API requests should return 401, UI requests should redirect to login
        is_api_request = False
        accept_header = request.headers.get("accept", "")

        # Check if this is an API request based on the Accept header or path
        if not path.startswith("/dashboard/"):
            is_api_request = True

        # Avoid redirect loops - if we're already on the login page, don't redirect
        if path == "/dashboard/login":
            logger.info(f"📝 Already on login page, allowing request to proceed")
            return await call_next(request)

        # For API requests, return 401 as before
        if is_api_request:
            logger.info(f"🔐 Unauthorized API request to {path}, returning 401")
            return JSONResponse(
                status_code=401,
                content={"detail": "Not authenticated"},
                headers={"WWW-Authenticate": "Bearer"},
            )

        # For UI requests, redirect to login page
        logger.info(f"🔐 Unauthorized UI request to {path}, redirecting to login page")
        return RedirectResponse(
            url="/dashboard/login",
            status_code=302,  # Temporary redirect
        )


def add_auth_middleware(
    app: FastAPI,
    public_paths: List[str],
    exclude_path_prefixes: Optional[List[str]] = None,
) -> None:
    """Add authentication middleware to a FastAPI application.

    Args:
        app (FastAPI): The FastAPI application
        public_paths (List[str]): Paths that don't require authentication (required)
        exclude_path_prefixes (Optional[List[str]]): Path prefixes to exclude from middleware processing
    """
    app.add_middleware(
        AuthMiddleware,
        public_paths=public_paths,
        exclude_path_prefixes=exclude_path_prefixes,
    )
