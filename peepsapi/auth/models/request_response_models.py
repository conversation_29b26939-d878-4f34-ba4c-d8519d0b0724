"""Request and response models for authentication endpoints.

This module contains Pydantic models for request and response data used in
authentication endpoints, including registration, login, and recovery flows.
"""
from typing import Dict, List, Optional
from uuid import UUID

import phonenumbers
from pydantic import EmailStr, Field, field_validator

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import BaseModelWithExtra, IdentifierType
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


def validate_phone_number(cls, value):
    if value is None:
        return value
    try:
        # If the number starts with '+' assume it's complete with country code
        if value.startswith("+"):
            parsed = phonenumbers.parse(value, None)
        else:
            # Fall back to default region (e.g., US) if not prefixed with +
            parsed = phonenumbers.parse(value, "US")

        if not phonenumbers.is_valid_number(parsed):
            raise ValueError("Invalid phone number")
        return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.E164)
    except Exception:
        raise ValueError(f"Invalid phone number format: {e}")


class InviteRequest(BaseModelWithExtra):
    """Request model for creating an invitation."""

    email: Optional[EmailStr] = Field(None, description="Email address for invitation")
    phone_number: Optional[str] = Field(None, description="Phone number for invitation")
    expires_in_days: Optional[int] = 7

    @field_validator("phone_number")
    def validate_phone_numbers(cls, value):
        return validate_phone_number(cls, value)

    @property
    def identifier_type(self) -> str:
        """Determine the type of identifier provided."""
        if self.email:
            return "email"
        elif self.phone_number:
            return "phone"
        else:
            raise ValueError("Either email or phone_number must be provided")

    @property
    def identifier_value(self) -> str:
        """Get the value of the identifier."""
        if self.email:
            return self.email
        elif self.phone_number:
            return self.phone_number
        else:
            raise ValueError("Either email or phone_number must be provided")


class InviteResponse(BaseModelWithExtra):
    """Response model for invitation creation."""

    token: str
    invite_url: str
    expires_at: str  # ISO format string for datetime


class DeviceInfoRequest(BaseModelWithExtra):
    """Request model for device information.

    This model is used for API requests and will be converted to the standard
    DeviceInfo model internally.
    """

    name: str = Field(..., min_length=1, max_length=100)
    type: str = Field(..., min_length=1, max_length=50)
    os: Optional[str] = Field(None, max_length=50)
    browser: Optional[str] = Field(None, max_length=50)
    ip: Optional[str] = None

    def to_device_info(self, created_at: UTCDateTime) -> DeviceInfo:
        """Convert this request model to a DeviceInfo model.

        Args:
            created_at: The creation timestamp for the device info

        Returns:
            DeviceInfo: A standardized device info model
        """
        return DeviceInfo(
            name=self.name,
            type=self.type,
            os=self.os,
            browser=self.browser,
            ip=self.ip,
            created_at=created_at,
            last_used_at=created_at,
            is_active=True,
        )


class ChallengeResponse(BaseModelWithExtra):
    """Response model for challenge."""

    options: Dict


class AuthenticationCredentialResponseData(BaseModelWithExtra):
    """Model for authentication credential data."""

    authenticator_data: bytes
    client_data_json: bytes
    signature: bytes
    user_handle: bytes


class AuthenticationCredentialData(BaseModelWithExtra):
    """Model for authentication credential data."""

    id: str
    raw_id: str
    type: str
    response: AuthenticationCredentialResponseData


class AuthenticationVerifyRequest(BaseModelWithExtra):
    """Request model for authentication verification."""

    credential: AuthenticationCredentialData


class AuthenticationVerifyResponse(BaseModelWithExtra):
    """Response model for authentication verification."""

    person_id: UUID
    profile_completed: bool
    session_token: str


class DeviceInfoResponse(DateTimeModelMixin, BaseModelWithExtra):
    """Response model for device information.

    This model is used for API responses and is derived from the standard
    DeviceInfo model.
    """

    id: str
    name: str
    type: str
    os: Optional[str] = None
    browser: Optional[str] = None
    is_active: bool = True
    registered_at: Optional[UTCDateTime] = None
    last_used_at: Optional[UTCDateTime] = None

    @classmethod
    def from_device_info(
        cls, device_id: str, device_info: DeviceInfo
    ) -> "DeviceInfoResponse":
        """Create a response model from a DeviceInfo model.

        Args:
            device_id: The ID of the device (usually the credential ID)
            device_info: The DeviceInfo model

        Returns:
            DeviceInfoResponse: A response model for the API
        """
        return cls(
            id=device_id,
            name=device_info.name,
            type=device_info.type,
            os=device_info.os,
            browser=device_info.browser,
            is_active=device_info.is_active,
            registered_at=device_info.created_at,
            last_used_at=device_info.last_used_at,
        )


class DeviceListResponse(BaseModelWithExtra):
    """Response model for device listing."""

    devices: List[DeviceInfoResponse]


# Recovery models
class IdentifierBasedRequest(BaseModelWithExtra):
    """Request model for initiating account recovery."""

    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None

    @field_validator("phone_number")
    def validate_phone_number(cls, value):
        return validate_phone_number(cls, value)

    @property
    def identifier_type(self) -> str:
        """Determine the type of identifier provided."""
        if self.email:
            return IdentifierType.EMAIL
        elif self.phone_number:
            return IdentifierType.PHONE
        else:
            raise ValueError("Either email or phone_number must be provided")

    @property
    def identifier_value(self) -> str:
        """Get the value of the identifier."""
        if self.email:
            return self.email
        elif self.phone_number:
            return self.phone_number
        else:
            raise ValueError("Either email or phone_number must be provided")

    @property
    def has_identifier(self) -> bool:
        """Check if any identifier is provided."""
        return bool(self.email or self.phone_number)


class RecoveryVerifyRequest(BaseModelWithExtra):
    """Request model for verifying a recovery code."""

    token: str
    identifier_type: IdentifierType
    email: Optional[EmailStr] = Field(None, description="Email address for recovery")
    phone_number: Optional[str] = Field(None, description="Phone number for recovery")

    @field_validator("phone_number")
    def validate_phone_number(cls, value):
        return validate_phone_number(cls, value)


class RecoveryResponse(BaseModelWithExtra):
    """Response model for account recovery."""

    success: bool
    message: str
    expires_at: Optional[str] = None  # ISO format string for datetime
    token: Optional[str] = None
    person_id: Optional[UUID] = None


class RecoveryRegistrationVerifyRequest(BaseModelWithExtra):
    """Request model for verifying a registration during recovery."""

    token: str
    credential: Dict
    is_recovery: bool = Field(True, alias="isRecovery")


class RegistrationChallengeRequest(BaseModelWithExtra):
    """Request model for creating a registration challenge."""

    token: str = Field(..., min_length=32, max_length=64)  # Based on token generation


class RegistrationCredentialResponseData(BaseModelWithExtra):
    """Model for registration credential data."""

    attestation_object: bytes
    client_data_json: bytes


class RegistrationCredentialData(BaseModelWithExtra):
    """Model for registration credential data."""

    id: str
    raw_id: str
    type: str
    response: RegistrationCredentialResponseData


class RegistrationVerifyRequest(BaseModelWithExtra):
    """Request model for registration verification."""

    credential: RegistrationCredentialData
    token: str = Field(..., min_length=32, max_length=64)  # Based on token generation


class RegistrationResponse(BaseModelWithExtra):
    """Response model for registration challenge."""

    options: dict
    challenge_id: str
    person_id: UUID


class RegistrationVerifyResponse(BaseModelWithExtra):
    """Response model for registration verification."""

    credential_id: str
    session_token: str
    success: bool
    message: str
    person_id: UUID
