"""Models package for authentication.

This package contains model modules for authentication data structures.
"""

from .device import DeviceInfo
from .passkey import (
    AuthenticationChallenge,
    InviteToken,
    PasskeyCredential,
    RegistrationChallenge,
    SessionToken,
)

__all__ = [
    # Device models
    "DeviceInfo",
    # Passkey authentication models
    "AuthenticationChallenge",
    "InviteToken",
    "PasskeyCredential",
    "RegistrationChallenge",
    "SessionToken",
]
