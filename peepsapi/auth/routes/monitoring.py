"""Authentication monitoring routes.

This module provides API endpoints for monitoring authentication activities,
including login attempts, success rates, authentication methods, and rate limiting.
"""

from typing import Dict
from uuid import UUID

from fastapi import APIRouter, Depends

from peepsapi.auth.services.auth_monitoring import auth_monitoring
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.rate_limiter import (
    get_all_rate_limiter_statistics,
    reset_all_rate_limiter_statistics,
)
from peepsapi.utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


@router.get("/monitoring/auth-stats")
async def get_auth_statistics(
    stats: Dict = Depends(auth_monitoring.get_statistics_protected),
):
    """Get authentication statistics.

    This endpoint requires authentication and returns detailed statistics
    about authentication attempts, success rates, and suspicious activities.

    Args:
        stats (Dict): The authentication statistics (from dependency)

    Returns:
        Dict: Authentication statistics
    """
    return stats


@router.get("/monitoring/rate-limit-stats")
async def get_rate_limit_statistics(
    _: UUID = Depends(auth_service.get_current_person),
):
    """Get rate limiting statistics.

    This endpoint requires authentication and returns detailed statistics
    about rate limiting, including request counts, block rates, and active blocks.

    Args:
        _ (str): The current authenticated person (for authorization)

    Returns:
        Dict: Rate limiting statistics
    """
    return get_all_rate_limiter_statistics()


@router.post("/monitoring/reset-rate-limit-stats")
async def reset_rate_limit_statistics(
    _: UUID = Depends(auth_service.get_current_person),
):
    """Reset rate limiting statistics.

    This endpoint requires authentication and resets all rate limiting statistics.
    This does not affect active blocks, only the counters.

    Args:
        _ (str): The current authenticated person (for authorization)

    Returns:
        Dict: Success message
    """
    reset_all_rate_limiter_statistics()
    return {"success": True, "message": "Rate limiting statistics reset successfully"}
