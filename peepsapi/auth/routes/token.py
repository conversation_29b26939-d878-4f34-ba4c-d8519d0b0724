"""Token management routes for authentication.

This module provides API endpoints for token management, including
token revocation and token information.
"""

from typing import Optional
from uuid import UUID

from fastapi import APIRouter, <PERSON><PERSON>, Depends, HTTPException, Request, Response, status

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.token_service import token_service
from peepsapi.auth.utils.constants import SESSION_TOKEN_COOKIE_NAME
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.logging import get_logger

router = APIRouter(tags=["token"])

# Configure logger
logger = get_logger(__name__)


@router.post("/revoke-token")
@handle_exceptions(error_code_prefix="TOKEN")
async def revoke_token(
    response: Response,
    request: Request,
    session_token: Optional[str] = Cookie(None, alias=SESSION_TOKEN_COOKIE_NAME),
    current_person: UUID = Depends(auth_service.get_current_person),
):
    """Revoke the current session token.

    Args:
        response (Response): The FastAPI response object
        request (Request): The FastAPI request object
        session_token (Optional[str]): The session token cookie
        current_person (str): The current authenticated person

    Returns:
        Dict: The result of token revocation
    """
    if not session_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No session token provided",
        )

    # Revoke the token
    success = token_service.revoke_token(session_token)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke token",
        )

    # Clear the session token cookie with secure attributes
    response.delete_cookie(
        key=SESSION_TOKEN_COOKIE_NAME,
        httponly=True,  # JavaScript cannot access the cookie
        secure=True,  # Requires HTTPS
        samesite="strict",  # Only sent in same-site navigation (no cross-origin)
    )

    logger.info(
        "✅ Token revoked successfully",
        request=request,
        person_id=current_person,
    )

    return {"success": True, "message": "Token revoked successfully"}


@router.post("/revoke-all-tokens")
@handle_exceptions(error_code_prefix="TOKEN")
async def revoke_all_tokens(
    response: Response,
    request: Request,
    current_person: UUID = Depends(auth_service.get_current_person),
):
    """Revoke all tokens for the current user.

    Args:
        response (Response): The FastAPI response object
        request (Request): The FastAPI request object
        current_person (UUID): The current authenticated person

    Returns:
        Dict: The result of token revocation
    """
    # Revoke all tokens for the user
    count = token_service.revoke_all_tokens(current_person)

    # Clear the session token cookie with secure attributes
    response.delete_cookie(
        key=SESSION_TOKEN_COOKIE_NAME,
        httponly=True,  # JavaScript cannot access the cookie
        secure=True,  # Requires HTTPS
        samesite="strict",  # Only sent in same-site navigation (no cross-origin)
    )

    logger.info(
        f"✅ Revoked {count} tokens for user",
        request=request,
        person_id=current_person,
    )

    return {"success": True, "message": f"Revoked {count} tokens successfully"}
