"""Registration routes for authentication.

This module provides API endpoints for user registration, including
invite token validation and WebAuthn registration.
"""

from fastapi import APIRouter, Depends, Request, Response
from fastapi.responses import JSONResponse

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.request_response_models import (
    RegistrationChallengeRequest,
    RegistrationResponse,
    RegistrationVerifyRequest,
    RegistrationVerifyResponse,
)
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.challenge_service import ChallengeMode, challenge_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.rate_limiter import registration_rate_limiter
from peepsapi.auth.services.token_service import token_service
from peepsapi.models.base import recursive_clean
from peepsapi.services.cosmos_containers import (
    get_passkey_credentials_container,
    get_people_container,
    get_session_tokens_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ValidationError
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/register", tags=["registration"])
logger = get_logger(__name__)


@router.post("/challenge", response_model=RegistrationResponse)
@handle_exceptions(error_code_prefix="REGISTRATION")
async def create_registration_challenge(
    request: RegistrationChallengeRequest,
    request_obj: Request,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Create a WebAuthn registration challenge.

    Args:
        request (RegistrationChallengeRequest): The registration challenge request
        request_obj (Request): The FastAPI request object
        device_info (DeviceInfo): The device information
        _ (None): Rate limit check dependency

    Returns:
        RegistrationResponse: The registration challenge response
    """
    logger.info(
        "🔐 Registration challenge request",
        request=request_obj,
        extra={"client_ip": device_info.ip, "user_agent": device_info.user_agent},
    )

    invite_token = token_service.validate_invite_token(
        request.token, ChallengeMode.REGISTRATION
    )

    if not invite_token:
        raise ValidationError("Invalid token")

    # Create challenge using unified method
    options, challenge = await challenge_service.create_challenge(
        mode=ChallengeMode.REGISTRATION,
        person_id=invite_token.person_id,
        invite_token=invite_token,
    )

    # Return the registration options
    return JSONResponse(content=recursive_clean(dict(options)))


@router.post("/verify", response_model=RegistrationVerifyResponse)
@handle_exceptions(error_code_prefix="REGISTRATION")
async def verify_registration_challenge(
    request: RegistrationVerifyRequest,
    request_obj: Request,
    response: Response,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    credential_container: CosmosContainer = Depends(get_passkey_credentials_container),
    people_container: CosmosContainer = Depends(get_people_container),
    session_tokens_container: CosmosContainer = Depends(get_session_tokens_container),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
) -> RegistrationVerifyResponse:
    """Verify WebAuthn registration challenge.

    Args:
        request (RegistrationVerifyRequest): The registration verification request
        request_obj (Request): The FastAPI request object
        response (Response): The FastAPI response object
        credential_container: The passkey credentials container dependency
        people_container: The people container dependency
        session_tokens_container: The session tokens container dependency
        device_info (DeviceInfo): The device information
        _ (None): Rate limit check dependency

    Returns:
        Dict: The result of registration verification
    """
    return await auth_service.verify_registration_challenge(
        request=request,
        request_obj=request_obj,
        response=response,
        device_info=device_info,
        credential_container=credential_container,
        people_container=people_container,
        session_tokens_container=session_tokens_container,
        mode=ChallengeMode.REGISTRATION,
    )
