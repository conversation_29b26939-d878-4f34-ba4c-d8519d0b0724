"""Azure AD authentication routes.

This module provides API endpoints for Azure AD authentication,
including login, callback, and logout.
"""

from typing import Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.responses import RedirectResponse

from peepsapi.auth.services.azure_ad_service import (
    AZURE_AD_COOKIE_MAX_AGE,
    AZURE_AD_COOKIE_NAME,
    azure_ad_service,
)
from peepsapi.auth.services.security_service import audit_logger
from peepsapi.utils.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


@router.get("/azure-ad/login")
async def azure_ad_login(request: Request):
    """Initiate Azure AD login flow.

    Args:
        request (Request): The FastAPI request object

    Returns:
        RedirectResponse: Redirect to Azure AD login page
    """
    try:
        # Ensure Azure AD service is initialized
        await azure_ad_service.init()
        # Generate a login URL
        login_url = await azure_ad_service.get_login_url(request)
        # Redirect to Azure AD login page
        return RedirectResponse(url=login_url)
    except Exception as e:
        logger.error(f"❌ Error initiating Azure AD login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate Azure AD login: {str(e)}",
        )


@router.get("/azure-ad/callback")
async def azure_ad_callback(request: Request, response: Response):
    """Handle Azure AD callback.

    Args:
        request (Request): The FastAPI request object
        response (Response): The FastAPI response object

    Returns:
        RedirectResponse: Redirect to the home page after successful authentication
    """
    try:
        # Handle the callback
        token, user_info = await azure_ad_service.handle_callback(request, response)
        # Log successful authentication
        client_ip = request.client.host if request.client else "unknown"
        try:
            # Add auth method to details instead of as a parameter
            audit_logger.log_authentication_attempt(
                identifier=user_info.get("email", "unknown"),
                ip_address=client_ip,
                user_agent=request.headers.get("User-Agent", ""),
                success=True,
                reason="Azure AD authentication",
            )
        except Exception as e:
            logger.error(f"❌ Error logging authentication attempt: {str(e)}")
            # Non-critical error, continue with authentication

        # Create a redirect response
        redirect = RedirectResponse(
            url="/dashboard/", status_code=status.HTTP_303_SEE_OTHER
        )

        # Set the cookie directly on the redirect response to ensure it's included
        # Extract domain from host for proper cookie setting
        host = request.headers.get("host", "localhost")

        # For local testing, don't set a domain at all
        is_local = "localhost" in host or "local." in host
        domain = None

        # Only set domain for non-local environments
        if not is_local and "." in host:
            # Extract domain from host (e.g., dev.peepsapp.ai -> peepsapp.ai)
            domain_parts = host.split(".")
            if len(domain_parts) >= 2:
                # For subdomains, use the main domain (e.g., dev.peepsapp.ai -> peepsapp.ai)
                if len(domain_parts) > 2:
                    domain = ".".join(domain_parts[-2:])
                else:
                    domain = host

        logger.info(
            f"🔧 Host: {host}, Is local: {is_local}, Domain for cookie: {domain}"
        )

        # Set the cookie directly on the redirect response
        # For local testing, we'll use a more permissive SameSite policy
        # In production, you should use "lax" or "strict"
        # is_local = "localhost" in host or "local." in host

        redirect.set_cookie(
            key=AZURE_AD_COOKIE_NAME,
            value=token,  # Use the token from the callback
            httponly=True,  # JavaScript cannot access the cookie
            secure=True,  # Requires HTTPS
            samesite="strict",  # Only sent in same-site navigation (no cross-origin)
            max_age=AZURE_AD_COOKIE_MAX_AGE,
            domain=domain,
            path="/",
        )

        logger.info(
            f"🔐 Setting Azure AD cookie on redirect response with domain: {domain}"
        )
        logger.info(f"🔐 Cookie value (first 10 chars): {token[:10]}...")

        return redirect
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"❌ Error handling Azure AD callback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to authenticate with Azure AD: {str(e)}",
        )


@router.get("/azure-ad/logout")
async def azure_ad_logout(request: Request):
    """Log out from Azure AD.

    Args:
        request (Request): The FastAPI request object

    Returns:
        RedirectResponse: Redirect to dashboard after successful logout
    """
    # Extract domain from host for proper cookie clearing
    host = request.headers.get("host", "localhost")

    # For local testing, don't set a domain at all
    is_local = "localhost" in host or "local." in host
    domain = None

    # Only set domain for non-local environments
    if not is_local and "." in host:
        # Extract domain from host (e.g., dev.peepsapp.ai -> peepsapp.ai)
        domain_parts = host.split(".")
        if len(domain_parts) >= 2:
            # For subdomains, use the main domain (e.g., dev.peepsapp.ai -> peepsapp.ai)
            if len(domain_parts) > 2:
                domain = ".".join(domain_parts[-2:])
            else:
                domain = host

    logger.info(f"🔐 Clearing cookies with domain: {domain}")

    # Create a redirect response
    redirect = RedirectResponse(url="/", status_code=status.HTTP_303_SEE_OTHER)

    # Clear the Azure AD cookie
    redirect.delete_cookie(
        key=AZURE_AD_COOKIE_NAME,
        httponly=True,  # JavaScript cannot access the cookie
        secure=True,  # Requires HTTPS
        samesite="strict",  # Only sent in same-site navigation (no cross-origin)
        domain=domain,
        path="/",
    )

    logger.info("Redirecting to / after successful logout")
    return redirect


@router.get("/azure-ad/me")
async def azure_ad_me(
    user: Optional[Dict] = Depends(azure_ad_service.get_current_user),
):
    """Get the current user's information.

    Args:
        user (Optional[Dict]): The current user's information

    Returns:
        dict: The user's information

    Raises:
        HTTPException: If the user is not authenticated
    """
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
        )
    return user
