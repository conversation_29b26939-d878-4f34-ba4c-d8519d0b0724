"""Recovery routes for authentication.

This module provides API endpoints for account recovery, including
initiating recovery, verifying recovery codes, and registering new credentials.
"""
import secrets
import traceback
from typing import List, cast

from fastapi import APIRouter, Depends, Request, Response
from fastapi.responses import JSONResponse

from peepsapi import config
from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import InviteToken
from peepsapi.auth.models.request_response_models import (
    IdentifierBasedRequest,
    RecoveryResponse,
    RegistrationChallengeRequest,
    RegistrationResponse,
    RegistrationVerifyRequest,
    RegistrationVerifyResponse,
)
from peepsapi.auth.services import token_service
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.challenge_service import ChallengeMode, challenge_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.rate_limiter import registration_rate_limiter
from peepsapi.auth.services.security_service import audit_logger
from peepsapi.auth.utils.constants import RECOVERY_TOKEN_EXPIRATION
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.people_services import people_service
from peepsapi.models import now
from peepsapi.models.base import IdentifierType, recursive_clean
from peepsapi.services.cosmos_containers import (
    get_invite_tokens_container,
    get_passkey_credentials_container,
    get_people_container,
    get_session_tokens_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.constants import SYSTEM_USER_ID
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ValidationError
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/recover", tags=["recovery"])
logger = get_logger(__name__)
recovery_salt = config.get("RECOVERY_SALT", "peepsapi-recovery-salt")


def generate_recovery_token(
    identifier_type: IdentifierType, identifier_value: str
) -> InviteToken:
    """Generate a recovery token for the given identifier.

    Args:
        identifier_type (IdentifierType): The type of identifier ("email" or "phone")
        identifier_value (str): The value of the identifier

    Returns:
        InviteToken: InviteToken if person is found
    """
    logger.debug(f"🎯 Generate recovery token for {identifier_type}={identifier_value}")

    person: Person = None
    try:
        people: List[Person] = people_service.get_people_by_identifier(
            identifier_type, identifier_value
        )

        person = next(iter(people))

    except Exception as e:
        logger.error(f"❌ Unexpected error in generate_recovery_token: {e}")
        logger.error(f"❌ Exception type: {type(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return None

    try:
        # Generate a recovery token
        token = secrets.token_urlsafe(32)
        current_time = now()
        expires_at = current_time + RECOVERY_TOKEN_EXPIRATION
        logger.debug(
            f"🔐 Generated token: {token[:5]}... expires at {expires_at.isoformat()}"
        )
        # Create recovery token record with the identifier the user provided
        token_id = secrets.token_hex(16)
        invite_token: InviteToken = InviteToken(
            id=token_id,
            token=token,
            person_id=person.id,
            created_by=SYSTEM_USER_ID,  # Recovery tokens are created by the system
            identifier_value=identifier_value,
            identifier_type=identifier_type,
            created_at=current_time.isoformat(),
            expires_at=expires_at.isoformat(),
            is_used=False,
            is_recovery=True,  # Mark as recovery token
        )

        # Save token to database
        try:
            invite_tokens_container: CosmosContainer = get_invite_tokens_container()
            invite_tokens_container.create_model(invite_token)
            logger.debug(
                f"✅ Token stored in database with ID: {invite_token.id[:5]}..."
            )
        except Exception as db_err:
            logger.error(
                f"❌ Failed to store token in database ID: {invite_token.id} err: {db_err}"
            )
            # TODO Continue anyway for testing purposes - we'll return the token even if storage fails
            # This allows testing the flow without a working database
            logger.warning("⚠️ Continuing without storing token in database")

        logger.debug(f"📤 Returning token data: {invite_token.id[:5]}...")
        return invite_token
    except Exception as e:
        logger.error(f"❌ Unexpected error creating recovery token: {e}")
        logger.error(f"❌ Exception type: {type(e)}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return None


@router.post("/initiate", response_model=RecoveryResponse)
@handle_exceptions(error_code_prefix="RECOVERY")
async def initiate_recovery(
    request: IdentifierBasedRequest,
    request_obj: Request,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Initiate account recovery.

    Args:
        request (IdentifierBasedRequest): The recovery request
        request_obj (Request): The FastAPI request object
        device_info (DeviceInfo): The device information
        _ (None): Rate limit check dependency

    Returns:
        RecoveryResponse: The recovery response
    """
    logger.info(
        "🔐 Recovery initiation request",
        request=request_obj,
        extra={"client_ip": device_info.ip, "user_agent": device_info.user_agent},
    )

    logger.info(
        f"🔐 Initiating recovery for {request.identifier_type}",
        request=request_obj,
        extra={
            "identifier_type": request.identifier_type,
            "client_ip": device_info.ip,
            "user_agent": device_info,
        },
    )

    # Generate recovery token
    invite_token = generate_recovery_token(
        identifier_type=request.identifier_type,
        identifier_value=request.identifier_value,
    )

    if not invite_token:
        logger.debug(
            "🔐 No recovery token generated",
            request=request_obj,
            extra={
                "identifier_type": request.identifier_type,
                "identifier_value": request.identifier_value,
            },
        )
        return RecoveryResponse(
            success=False,
            message="No account found with this identifier",
            error_code="NO_ACCOUNT_FOUND",
        )

    invite_token = cast(InviteToken, invite_token)
    logger.debug(
        "🔐 Recovery token generated",
        request=request_obj,
        extra={"token": invite_token.id[:5] + "..."},
    )

    # For security reasons, don't reveal that the user doesn't exist
    # Instead, return a generic success message
    response = RecoveryResponse(
        success=True,
        message="If an account exists with this identifier, a recovery email or SMS has been sent.",
    )

    # Log recovery attempt
    audit_logger.log_recovery_attempt(
        identifier=request.identifier_value,
        ip_address=device_info.ip,
        user_agent=device_info.user_agent,
        success=True,
    )

    if invite_token:
        logger.debug("🔧 Updating response with token data", request=request_obj)
        response.token = invite_token.token
        response.person_id = invite_token.person_id
        response.expires_at = invite_token.expires_at

    # In a real implementation, send an email or SMS with the recovery token
    # For now, just return the token in the response
    return response


@router.post("/challenge", response_model=RegistrationResponse)
@handle_exceptions(error_code_prefix="RECOVERY")
async def create_recovery_challenge(
    request: RegistrationChallengeRequest,
    request_obj: Request,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Create a WebAuthn recovery challenge.

    Args:
        request (RegistrationChallengeRequest): The recovery registration challenge request
        request_obj (Request): The FastAPI request object
        device_info (DeviceInfo): The device information
        _ (None): Rate limit check dependency

    Returns:
        Dict: The registration challenge response
    """
    logger.info(
        "🔐 Recovery challenge request",
        request=request_obj,
        extra={"client_ip": device_info.ip, "user_agent": device_info.user_agent},
    )

    # Validate recovery token
    invite_token = token_service.validate_invite_token(
        request.token, ChallengeMode.RECOVERY
    )
    if not invite_token:
        logger.warning(
            "Invalid or expired recovery token",
            request=request_obj,
            extra={"token": request.token[:5] + "..." if request.token else None},
        )
        raise ValidationError(
            message="Invalid or expired recovery token",
            error_code="INVALID_TOKEN",
        )

    invite_token = cast(InviteToken, invite_token)
    # Log successful recovery token validation
    audit_logger.log_recovery_attempt(
        identifier=invite_token.identifier_value,
        user_id=invite_token.person_id,
        ip_address=device_info.ip,
        user_agent=device_info.user_agent,
        success=True,
        reason="Recovery token validated",
    )
    display_name = invite_token.identifier_value

    # Fallback to ID if no email or phone is available
    if not display_name:
        display_name = invite_token.person_id

    # Create challenge using unified method
    options, challenge = await challenge_service.create_challenge(
        mode=ChallengeMode.RECOVERY,
        person_id=invite_token.person_id,
        person_name=display_name,
        display_name=display_name,
        invite_token=invite_token,
    )

    logger.info(
        "✅ Recovery Registration challenge created successfully",
        request=request_obj,
        person_id=invite_token.person_id,
    )

    return JSONResponse(content=recursive_clean(dict(options)))


@router.post("/verify", response_model=RegistrationVerifyResponse)
@handle_exceptions(error_code_prefix="RECOVERY")
async def verify_recovery_challenge(
    request: RegistrationVerifyRequest,
    response: Response,
    request_obj: Request,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    credential_container: CosmosContainer = Depends(get_passkey_credentials_container),
    people_container: CosmosContainer = Depends(get_people_container),
    session_tokens_container: CosmosContainer = Depends(get_session_tokens_container),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Verify WebAuthn recovery challenge.

    Args:
        request (RegistrationVerifyRequest): The recovery registration verification request
        response (Response): The FastAPI response object
        request_obj (Request): The FastAPI request object
        credential_container: The passkey credentials container dependency
        people_container: The people container dependency
        session_tokens_container: The session tokens container dependency
        device_info (DeviceInfo): The device information
        _ (None): Rate limit check dependency

    Returns:
        Dict: The result of registration verification
    """
    return await auth_service.verify_registration_challenge(
        request=request,
        request_obj=request_obj,
        response=response,
        device_info=device_info,
        credential_container=credential_container,
        people_container=people_container,
        session_tokens_container=session_tokens_container,
        mode=ChallengeMode.RECOVERY,
    )
