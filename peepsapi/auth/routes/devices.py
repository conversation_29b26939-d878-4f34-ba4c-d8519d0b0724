"""Device management routes for authentication.

This module provides API endpoints for managing authentication devices,
including listing, renaming, and removing devices.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import PasskeyCredential
from peepsapi.auth.models.request_response_models import (
    DeviceInfoResponse,
    DeviceListResponse,
)
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.models import now
from peepsapi.services.cosmos_containers import get_passkey_credentials_container

router = APIRouter()


@router.get("/devices", response_model=DeviceListResponse)
async def list_devices(
    current_person: UUID = Depends(auth_service.get_current_person),
    credential_container=Depends(get_passkey_credentials_container),
):
    """List all devices for the current user.

    Args:
        current_person (UUID): The current authenticated person
        credential_container: The passkey credentials container dependency

    Returns:
        DeviceListResponse: The list of devices
    """
    # Find all credentials for the current user
    query = "SELECT * FROM c WHERE c.person_id = @person_id"
    params = [{"name": "@person_id", "value": str(current_person)}]

    credential_results = list(
        credential_container.query_items(
            query=query,
            parameters=params,
            enable_cross_partition_query=True,
        )
    )

    # Convert credentials to DeviceInfoResponse objects
    devices = []
    for cred in credential_results:
        credential = PasskeyCredential(**cred)

        # Use the from_device_info method to create the response
        device_response = DeviceInfoResponse.from_device_info(
            device_id=credential.id, device_info=credential.device_info
        )
        devices.append(device_response)

    return DeviceListResponse(devices=devices)


@router.put("/devices/{device_id}/rename")
async def rename_device(
    device_id: str,
    name: str,
    current_person: UUID = Depends(auth_service.get_current_person),
    credential_container=Depends(get_passkey_credentials_container),
):
    """Rename a device.

    Args:
        device_id (str): The ID of the device to rename
        name (str): The new name for the device
        current_person (UUID): The current authenticated person
        credential_container: The passkey credentials container dependency

    Returns:
        Dict: The result of the operation
    """
    # Find the credential
    try:
        credential_data = credential_container.read_item(
            item=device_id, partition_key=device_id
        )
        credential = PasskeyCredential(**credential_data)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found",
        )

    # Check if the credential belongs to the current user
    if credential.person_id != current_person:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to rename this device",
        )

    # Update the credential's device_info name
    if hasattr(credential, "device_info") and credential.device_info:
        credential.device_info.name = name
    else:
        # Handle legacy credentials that might not have device_info
        current_time = now()
        credential.device_info = DeviceInfo(
            name=name,
            type="unknown",
            os="Unknown",
            browser="Unknown",
            ip="0.0.0.0",
            created_at=current_time,
            last_used_at=current_time,
            is_active=True,
        )

    credential_container.replace_model(model=credential)

    return {"success": True, "message": "Device renamed successfully"}


# Device deletion endpoint has been removed


@router.put("/devices/{device_id}/toggle-status")
async def toggle_device_status(
    device_id: str,
    current_person: UUID = Depends(auth_service.get_current_person),
    credential_container=Depends(get_passkey_credentials_container),
):
    """Toggle the active status of a device.

    Args:
        device_id (str): The ID of the device to toggle
        current_person (UUID): The current authenticated person
        credential_container: The passkey credentials container dependency

    Returns:
        Dict: The result of the operation
    """
    # Find the credential
    try:
        credential_data = credential_container.read_item(
            item=device_id, partition_key=device_id
        )
        credential = PasskeyCredential(**credential_data)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found",
        )

    # Check if the credential belongs to the current user
    if credential.person_id != current_person:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to modify this device",
        )

    # Check if this is the only active credential for the user
    if credential.is_active:
        query = "SELECT * FROM c WHERE c.person_id = @person_id AND c.is_active = true"
        params = [{"name": "@person_id", "value": str(current_person)}]

        active_credentials = list(
            credential_container.query_items(
                query=query,
                parameters=params,
                enable_cross_partition_query=True,
            )
        )

        if len(active_credentials) <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate the only active device. Add another device first.",
            )

    # Toggle the active status
    if hasattr(credential, "device_info") and credential.device_info:
        credential.device_info.is_active = not credential.device_info.is_active
        status_message = (
            "activated" if credential.device_info.is_active else "deactivated"
        )
    else:
        # For legacy credentials without device_info
        credential.is_active = not credential.is_active
        status_message = "activated" if credential.is_active else "deactivated"

    credential_container.replace_model(model=credential)

    return {"success": True, "message": f"Device {status_message} successfully"}
