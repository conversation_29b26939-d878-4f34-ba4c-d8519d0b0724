"""Invitation routes for authentication.

This module provides API endpoints for creating and managing invitations.
"""
import secrets
from datetime import timed<PERSON><PERSON>
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Request

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import InviteToken
from peepsapi.auth.models.request_response_models import InviteRequest, InviteResponse
from peepsapi.auth.services.auth_service import auth_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.rate_limiter import registration_rate_limiter
from peepsapi.auth.services.security_service import audit_logger
from peepsapi.crud.models.person import Email, Person, PhoneNumber
from peepsapi.crud.services.people_services import people_service
from peepsapi.models import from_iso_string, now, to_iso_string
from peepsapi.models.base import IdentifierType
from peepsapi.services.cosmos_containers import get_invite_tokens_container
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(tags=["invite"])


@router.post("/invite", response_model=InviteResponse)
@handle_exceptions(error_code_prefix="INVITE")
async def create_invite(
    invite_request: InviteRequest,
    request: Request,
    current_person: UUID = Depends(auth_service.get_current_person),
    invite_container: CosmosContainer = Depends(get_invite_tokens_container),
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    _: None = Depends(registration_rate_limiter.check_rate_limit),
):
    """Create an invitation for a new user.

    The person ID comes from the auth_service.get_current_person dependency,
    which gets the current_person from the request state. This is set by the middleware from:
    1. JWT token (highest priority)
    2. Azure AD token with x-peeps-id cookie (if present)
    3. Azure AD token (lowest priority)

    Args:
        invite_request (InviteRequest): The invitation request
        request (Request): The FastAPI request object
        current_person (UUID): The current authenticated person (from auth_service.get_current_person)
        invite_container: The invite tokens container dependency
        device_info (DeviceInfo): The device information
        _ (None): Rate limit check dependency

    Returns:
        InviteResponse: The created invitation
    """
    logger.info(
        f"🔐 Creating invite for {invite_request.identifier_type}",
        request=request,
        person_id=current_person,
        extra={
            "identifier_type": invite_request.identifier_type,
            "expires_in_days": invite_request.expires_in_days,
            "client_ip": device_info.ip,
            "user_agent": device_info.user_agent,
        },
    )

    # Check if identifier is already invited
    query = "SELECT * FROM c WHERE c.identifier_value = @value AND c.is_used = false"
    params = [{"name": "@value", "value": invite_request.identifier_value}]

    existing_invites: list[InviteToken] = invite_container.query_models(
        query=query,
        parameters=params,
        model_class=InviteToken,
    )
    existing_invite: InviteToken = None
    if existing_invites:
        existing_invite = next(iter(existing_invites), None)
    current_time = now()
    if existing_invite is not None:
        # Check if the token is unexpired
        expires_at = existing_invite.expires_at
        if isinstance(expires_at, str):
            expires_at = from_iso_string(expires_at)
        # If token is not expired, extend its expiry
        if expires_at > current_time:
            # Calculate new expiry date
            existing_invite.expires_at = current_time + timedelta(
                days=invite_request.expires_in_days
            )
            existing_invite.created_by = current_person
            logger.info(
                "🔧 Extending existing invite expiry",
                request=request,
                person_id=current_person,
                extra={
                    "invite_id": existing_invite.id,
                    "old_expires_at": to_iso_string(expires_at),
                    "new_expires_at": to_iso_string(existing_invite.expires_at),
                    "created_by": current_person,
                },
            )
            # Patch the invite with updated fields
            invite_container.patch_model(
                item=existing_invite.id,
                partition_key=existing_invite.id,
                update_fields={
                    "expires_at": existing_invite.expires_at,
                    "updated_by": current_person,
                    "updated_at": now(),
                },
                model_class=InviteToken,
            )
            return create_invite_response(
                existing_invite, invite_request, request, current_person, device_info
            )

        # If token is expired, we'll create a new one (fall through to the code below)
        logger.info(
            "📝 Invite token has expired",
            request=request,
            person_id=current_person,
            extra={
                "invite_id": existing_invite.id,
                "identifier_type": existing_invite.identifier_type,
                "identifier_value": existing_invite.identifier_value,
            },
        )

    # First, check if the identifier already exists in person records
    people: List[Person] = people_service.get_people_by_identifier(
        identifier_type=invite_request.identifier_type,
        identifier_value=invite_request.identifier_value,
    )

    if people:
        # Person already exists, return appropriate error
        logger.warning(
            f"⚠️ Identifier already used on platform: {invite_request.identifier_type}",
            request=request,
            person_id=current_person,
            extra={"identifier_type": invite_request.identifier_type},
        )
        raise ValidationError(
            message=f"This {invite_request.identifier_type} is already registered with an existing account",
            error_code="IDENTIFIER_EXISTS",
        )

    # Generate a new invite token
    token = secrets.token_urlsafe(32)
    expires_at = current_time + timedelta(days=invite_request.expires_in_days)

    logger.info(
        "🔐 Creating new invite token",
        request=request,
        person_id=current_person,
        extra={"expires_at": to_iso_string(expires_at)},
    )

    # Create shell person profile
    person = Person(
        invited_by_id=current_person,
        primary_identifier_type=invite_request.identifier_type,
        primary_identifier_value=invite_request.identifier_value,
        member_since=now(),
        emails=[
            Email(
                address=invite_request.identifier_value,
                type="personal",
                active_since=now(),
            )
        ]
        if invite_request.identifier_type == IdentifierType.EMAIL
        else [],
        phone_numbers=[
            PhoneNumber(
                number=invite_request.identifier_value,
                type="personal",
                active_since=now(),
            )
        ]
        if invite_request.identifier_type == IdentifierType.PHONE
        else [],
    )
    # Save shell person profile
    people_service.create_person(person)
    logger.info(f"👤 Shell person created", extra={"person_id": person.id})
    # Create invite token record
    invite = InviteToken(
        id=secrets.token_hex(16),
        token=token,
        identifier_value=invite_request.identifier_value,
        identifier_type=invite_request.identifier_type,
        person_id=person.id,
        created_by=current_person,
        created_at=current_time,
        expires_at=expires_at,
        is_used=False,
    )

    # Save invite to database
    invite_container.create_model(invite)
    logger.info(
        "🔐 Invite token created",
        extra={"invite_id": invite.id, "person_id": invite.person_id},
    )
    return create_invite_response(
        invite, invite_request, request, current_person, device_info
    )


def create_invite_response(
    invite_token: InviteToken,
    invite_request: InviteRequest,
    request: Request,
    current_person: UUID,
    device_info: DeviceInfo,
) -> InviteResponse:
    """Create an invite response.

    Args:
        token (Token): The invite token
        request (Request): The FastAPI request object
        device_info (DeviceInfo): The device information

    Returns:
        InviteResponse: The invite response
    """
    audit_logger.log_invite_attempt(
        identifier_type=invite_request.identifier_type,
        identifier_value=invite_request.identifier_value,
        current_person=current_person,
        ip_address=device_info.ip,
        user_agent=device_info.user_agent,
        success=True,
    )

    # Get protocol from X-Forwarded-Proto header, default to https
    protocol = request.headers.get("x-forwarded-proto", "https")
    host = request.headers.get("host", "")

    # Generate invite URL using protocol and host
    invite_url = (
        f"{protocol}://{host}/auth/register/challenge?token={invite_token.token}"
    )

    logger.info(f"invite url being sent: {invite_url}")

    return InviteResponse(
        token=invite_token.token,
        invite_url=invite_url,
        expires_at=to_iso_string(invite_token.expires_at),
    )
