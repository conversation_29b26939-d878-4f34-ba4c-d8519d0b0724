"""Login routes for authentication.

This module provides API endpoints for user login, including
WebAuthn authentication challenge and verification.
"""

from typing import List

from fastapi import APIRouter, Depends, Request, Response
from fastapi.responses import JSONResponse

from peepsapi.auth.models.device import DeviceInfo
from peepsapi.auth.models.passkey import PasskeyCredential
from peepsapi.auth.models.request_response_models import (
    AuthenticationVerifyRequest,
    AuthenticationVerifyResponse,
    ChallengeResponse,
    IdentifierBasedRequest,
)
from peepsapi.auth.services.challenge_service import ChallengeMode, challenge_service
from peepsapi.auth.services.device_service import device_service
from peepsapi.auth.services.rate_limiter import login_rate_limiter
from peepsapi.auth.services.security_service import brute_force_protection
from peepsapi.auth.services.token_service import token_service
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.people_services import people_service
from peepsapi.models.base import recursive_clean
from peepsapi.services.cosmos_containers import get_people_container
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import RateLimitError, ValidationError
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/login", tags=["login"])
logger = get_logger(__name__)


@router.post("/challenge", response_model=ChallengeResponse)
@handle_exceptions(error_code_prefix="AUTH")
async def create_authentication_challenge(
    request: IdentifierBasedRequest,
    request_obj: Request,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    people_container: CosmosContainer = Depends(get_people_container),
    _: None = Depends(login_rate_limiter.check_rate_limit),
):
    """Create a WebAuthn authentication challenge.

    Args:
        request (IdentifierBasedRequest): The authentication challenge request
        request_obj (Request): The FastAPI request object
        device_info (DeviceInfo): The device information
        people_container (CosmosContainer): The people container
        _ (None): Rate limit check dependency

    Returns:
        ChallengeResponse: The authentication challenge response
    """
    # Check for brute force protection
    is_blocked, reason = brute_force_protection.is_blocked(request_obj)
    if is_blocked:
        raise RateLimitError(
            message=reason,
            error_code="BRUTE_FORCE_PROTECTION",
        )

    # Log authentication attempt
    logger.info(
        "🔐 Authentication challenge request",
        request=request_obj,
        extra={
            "identifier_type": request.identifier_type,
            "client_ip": device_info.ip,
            "user_agent": device_info,
        },
    )

    # Find user by email or phone number
    if not request.has_identifier:
        raise ValidationError(
            message="Email or phone number is required",
            error_code="MISSING_IDENTIFIER",
        )

    people: List[Person] = people_service.get_people_by_identifier(
        request.identifier_type, request.identifier_value
    )

    if not people:
        # For security reasons, don't reveal that the user doesn't exist
        # Instead, return a generic error message with a 400 status code
        logger.warning(
            "⚠️ User not found during authentication challenge",
            request=request_obj,
            extra={"identifier_type": request.identifier_type},
        )
        raise ValidationError(
            message="Invalid credentials",
            error_code="INVALID_CREDENTIALS",
        )

    if len(people) > 1:
        logger.warning(
            f"⚠️ Multiple users found for identifier {request.identifier_type}",
            request=request_obj,
        )

    person = next(iter(people), None)

    if not person.has_passkey:
        # For security reasons, don't reveal that the user doesn't have passkey credentials
        # Instead, return a generic error message with a 400 status code
        logger.warning(
            "⚠️ User has no passkey",
            request=request_obj,
            person_id=person.id,
        )
        raise ValidationError(
            message="User need to Register or Recover account",
            error_code="LOGIN_PASSKEY_NOT_FOUND",
        )

    # Find user's credentials
    credential_list = challenge_service.find_credentials_by_id_or_person_id(
        person_id=person.id,
    )

    if not credential_list:
        # For security reasons, don't reveal that the user doesn't have credentials
        # Instead, return a generic error message with a 400 status code
        logger.warning(
            "⚠️ No credentials found for user",
            request=request_obj,
            person_id=person.id,
        )
        raise ValidationError(
            message="Invalid credentials",
            error_code="INVALID_CREDENTIALS",
        )

    # Create authentication options
    options, challenge = await challenge_service.create_challenge(
        mode=ChallengeMode.AUTHENTICATION, person_id=person.id
    )

    logger.info(
        "✅ Authentication challenge created successfully",
        request=request_obj,
        person_id=person.id,
    )

    return JSONResponse(content=recursive_clean(dict(options)))


@router.post("/verify", response_model=AuthenticationVerifyResponse)
@handle_exceptions(error_code_prefix="AUTH")
async def verify_authentication_challenge(
    request: AuthenticationVerifyRequest,
    response: Response,
    request_obj: Request,
    device_info: DeviceInfo = Depends(device_service.extract_device_info),
    people_container: CosmosContainer = Depends(get_people_container),
    _: None = Depends(login_rate_limiter.check_rate_limit),
):
    """Verify WebAuthn authentication challenge.

    Args:
        request (AuthenticationVerifyRequest): The request body
        response (Response): The response object
        request_obj (Request): The raw request object
        device_info (DeviceInfo): The device information
        people_container (CosmosContainer): The people container
        _ (None): Rate limit check dependency

    Returns:
        AuthenticationVerifyResponse: The response
    """
    logger.info(
        "🔐 Authentication verification request",
        request=request_obj,
        extra={"client_ip": device_info.ip, "user_agent": device_info.user_agent},
    )

    credential: PasskeyCredential = await challenge_service.verify_challenge(
        mode=ChallengeMode.AUTHENTICATION,
        credential_data=request.credential,
        device_info=device_info,
    )

    # Create session token
    session_token = await token_service.create_and_set_token(
        response=response,
        person_id=credential.person_id,
        device_info=device_info,
        credential_id=credential.id,
    )

    # Get person to check profile completion
    person: Person = people_container.read_model(
        id=credential.person_id, partition_key=credential.person_id, model_class=Person
    )
    profile_completed = bool(
        person
        and person.name
        and person.last_name
        and person.current_role
        and person.current_company
        and person.location
        and (person.emails or person.phone_numbers)  # At least one contact method
    )

    return AuthenticationVerifyResponse(
        person_id=credential.person_id,
        profile_completed=profile_completed,
        session_token=session_token.token,
    )
