from typing import Type, TypeVar, Union
from uuid import UUID

from azure.cosmos.exceptions import CosmosResourceNotFoundError

from peepsapi.crud.models.base import DeleteMixin, DeleteReason, soft_delete_fields
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.error_handling import AuthorizationError, ResourceNotFoundError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


D = TypeVar("D", bound=DeleteMixin)


async def soft_delete_item(
    id: UUID,
    partition_key: Union[UUID, str],
    author_person_id: UUID,
    actor_person_id: UUID,
    actor_auth_source: str,
    model_class: Type[D],
    model_class_container: Union[CosmosContainer, CosmosContainerAsync],
):
    """
    Soft-delete a user-created content item.

    Args:
        id (UUID): The unique identifier of the content item to be soft-deleted.
        author_person_id (UUID): The unique identifier of the author of the content.
        actor_person_id (UUID): The unique identifier of the person performing the deletion.
        model_class (Type[T]): The model class representing the content.
        model_class_container (CosmosContainer): The Cosmos DB container for the content.

    Raises:
        ResourceNotFoundError: If the content item is not found in the database.

    Logs:
        Logs the success or failure of the soft-delete operation.
    """
    logger.info(f"🎯 Soft-deleting {model_class.__name__} {id}")

    if author_person_id != actor_person_id and actor_auth_source != "azure_ad":
        raise AuthorizationError()

    # Remove the post
    try:
        update_fields = soft_delete_fields(
            deleted_by=actor_person_id,
            reason=DeleteReason.BY_AUTHOR
            if author_person_id == actor_person_id
            else DeleteReason.BY_MODERATOR,
        )
        # Call patch_model with await if async, else sync
        if isinstance(model_class_container, CosmosContainerAsync):
            await model_class_container.patch_model(
                item=id,
                partition_key=partition_key,
                update_fields=update_fields,
                model_class=model_class,
            )
        else:
            model_class_container.patch_model(
                item=id,
                partition_key=partition_key,
                update_fields=update_fields,
                model_class=model_class,
            )
        logger.info(f"✅ Successfully soft-deleted {model_class.__name__} {id}")
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ {model_class.__name__} {id} not found for deletion")
        raise ResourceNotFoundError(
            message="Post not found",
            error_code="POST_NOT_FOUND",
        )
