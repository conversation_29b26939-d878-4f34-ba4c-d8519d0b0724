import os
import uuid
from typing import List, Optional, TypeVar
from uuid import UUID

from azure.cosmos.exceptions import CosmosHttpResponseError
from fastapi import HTTPException

from peepsapi.crud.models.base import ReactionMixin
from peepsapi.crud.models.reaction import Reaction, ReactionInput, ReactionsResponse
from peepsapi.crud.services.pagination_service import get_page
from peepsapi.crud.services.people_services import get_unique_person_previews
from peepsapi.crud.services.soft_delete_service import soft_delete_item
from peepsapi.crud.utils.constants import REACTION_NAMESPACE
from peepsapi.models.datetime import UTCDateTime
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.error_handling import ServerError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

R = TypeVar("R", bound=ReactionMixin)


def gen_reaction_id(target_id, author_person_id):
    src = "-".join(sorted([str(author_person_id), str(target_id)]))
    return uuid.uuid5(REACTION_NAMESPACE, src)


async def set_reactions(
    items: List[R],
    person_id: UUID,
    person_reactions_container: CosmosContainerAsync,
) -> None:
    """
    Set reactions for a list of items based on the reactions stored in the database.

    Args:
        items (List[R]): List of items to set reactions for.
        person_id (UUID): The unique identifier of the person whose reactions are being fetched.
        person_reactions_container (CosmosContainerAsync): The Cosmos DB container for person reactions.

    Returns:
        None: Updates the `reaction_type` attribute of each item in the `items` list.
    """
    item_ids = [str(item.id) for item in items]
    if not item_ids:
        return

    in_params = ", ".join([f"@id{i}" for i in range(len(item_ids))])
    query = f"SELECT c.target_id, c.type FROM c WHERE c.target_id IN ({in_params}) AND c.deleted = false"

    parameters = [
        {"name": f"@id{i}", "value": item_id} for i, item_id in enumerate(item_ids)
    ]

    reactions = person_reactions_container.query_items(
        query=query,
        parameters=parameters,
        partition_key=str(person_id),
    )  # type: ignore

    reaction_map = {r["target_id"]: r["type"] async for r in reactions}  # type: ignore
    from peepsapi.crud.models.base import reaction_types

    for item in items:
        # Ensure reaction_counts exists
        if not hasattr(item, "reaction_counts") or item.reaction_counts is None:
            item.reaction_counts = {}
        # Set missing reaction_types to 0
        for rt in reaction_types:
            if rt not in item.reaction_counts:
                item.reaction_counts[rt] = 0

        id = str(item.id)
        if id not in reaction_map:
            continue

        item.reaction_type = reaction_map[id]


async def get_reactions_page(
    target_id: UUID,
    next_page_token: Optional[str],
    reactions_container: CosmosContainerAsync,
    people_container: CosmosContainerAsync,
    where_clause: Optional[str] = None,
    where_clause_parameters: Optional[List[dict]] = [],
    get_person_previews: bool = True,
    fields: Optional[List[str]] = None,
) -> ReactionsResponse:
    """
    Get a page of reactions for a specific target (post or comment).
    """
    items, next_page = await get_page(
        partition_key=target_id,
        next_page_token=next_page_token,
        model_class=Reaction,
        model_class_container=reactions_container,
        page_size=int(os.getenv("REACTIONS_LIST_PAGE_SIZE", 10)),
        where_clause=where_clause,
        where_clause_parameters=where_clause_parameters or [],
        skip_soft_deleted=True,
        fields=fields,
    )

    people = []
    if get_person_previews and people_container:
        people = await get_unique_person_previews(
            people_ids={i.author_person_id for i in items},
            people_container=people_container,
        )

    return ReactionsResponse(
        people=people,
        items=items,
        next_page=next_page,
    )


async def create_reaction(
    target_id: UUID,
    target_parent_id: UUID,
    reaction_input: ReactionInput,
    current_person_id: UUID,
    reactions_container: CosmosContainerAsync,
    person_reactions_container: CosmosContainerAsync,
    target_container: CosmosContainer,
) -> Reaction:
    """Create a new reaction, updating counters and handling upsert logic."""
    count_incr_ops = []
    target_item = target_container.read_model(
        id=target_id, partition_key=target_parent_id
    )
    if not target_item:
        raise HTTPException(
            status_code=404,
            detail="Target not found",
        )
    if not target_item.get("reaction_counts"):
        count_incr_ops.append(
            {
                "op": "add",
                "path": "/reaction_counts",
                "value": {},
            }
        )
    count_incr_ops.append(
        {
            "op": "incr",
            "path": f"/reaction_counts/{reaction_input.type}",
            "value": 1,
        }
    )
    reaction = Reaction(
        id=gen_reaction_id(target_id=target_id, author_person_id=current_person_id),
        created_at=UTCDateTime.now(),
        target_id=target_id,
        author_person_id=current_person_id,
        **reaction_input.model_dump(),
    )
    try:
        await person_reactions_container.create_model(
            model=reaction, model_class=Reaction, quiet=True
        )
        await reactions_container.create_model(
            model=reaction, model_class=Reaction, quiet=True
        )
        target_container.patch_model(
            item=target_id,
            partition_key=target_parent_id,
            patch_operations=count_incr_ops,
        )
        logger.info(f"✅ Successfully created reaction {reaction.id}")
    except CosmosHttpResponseError as e:
        if e.status_code != 409:
            raise
        logger.warning("⚠️ Reaction already exist. It will be updated.")
        # Read the existing reaction to check if it was soft-deleted
        existing_reaction = await reactions_container.read_model(
            id=reaction.id,
            partition_key=reaction.target_id,
            model_class=Reaction,
        )
        was_soft_deleted = getattr(existing_reaction, "deleted", False)
        update_fields = {
            "type": reaction.type,
            "deleted": False,
            "deleted_at": None,
            "deleted_by": None,
            "delete_reason": None,
            "updated_at": reaction.created_at,
        }
        await reactions_container.patch_model(
            item=reaction.id,
            partition_key=reaction.target_id,
            update_fields=update_fields,
            model_class=Reaction,
        )
        await person_reactions_container.patch_model(
            item=reaction.id,
            partition_key=reaction.author_person_id,
            update_fields=update_fields,
            model_class=Reaction,
        )
        # If it was soft-deleted, increment the counter
        if was_soft_deleted:
            target_container.patch_model(
                item=target_id,
                partition_key=target_parent_id,
                patch_operations=count_incr_ops,
            )
            logger.info(
                f"✅ Restored soft-deleted reaction and incremented counter for {reaction.id}"
            )
        else:
            logger.info(f"✅ Successfully updated reaction {reaction.id}")
    except Exception as e:
        logger.error(
            "❌ Error creating or updating reaction", extra={"error": str(e)}, exc_info=e
        )
        raise ServerError(
            message="Error creating or updating reaction",
            error_code="CREATE_ERROR",
            details={"error": str(e)},
        )
    return reaction


async def delete_reaction(
    target_parent_id: UUID,
    target_id: UUID,
    current_person_id: UUID,
    auth_source: str,
    reactions_container: CosmosContainerAsync,
    person_reactions_container: CosmosContainerAsync,
    target_container: CosmosContainer,
) -> dict:
    """Delete a reaction by the current user on a post or comment."""
    reaction_id = gen_reaction_id(
        target_id=target_id, author_person_id=current_person_id
    )

    # Fetch the target model
    target_item = target_container.read_model(
        id=target_id, partition_key=target_parent_id
    )
    if not target_item:
        raise HTTPException(status_code=404, detail="Target not found")

    # Fetch the reaction to get its type
    reaction: Reaction = await reactions_container.read_model(
        id=reaction_id,
        partition_key=target_id,
        model_class=Reaction,
    )  # type: ignore
    if not reaction:
        raise HTTPException(status_code=404, detail="Reaction not found")

    count_decr_ops = [
        {
            "op": "incr",
            "path": f"/reaction_counts/{reaction.type}",
            "value": -1,
        }
    ]

    try:
        # Soft delete in reactions_container
        await soft_delete_item(
            id=reaction_id,
            partition_key=target_id,
            author_person_id=current_person_id,
            actor_person_id=current_person_id,
            actor_auth_source=auth_source,
            model_class=Reaction,
            model_class_container=reactions_container,
        )
        # Soft delete in person_reactions_container
        await soft_delete_item(
            id=reaction_id,
            partition_key=current_person_id,
            author_person_id=current_person_id,
            actor_person_id=current_person_id,
            actor_auth_source=auth_source,
            model_class=Reaction,
            model_class_container=person_reactions_container,
        )
        target_container.patch_model(
            item=target_id,
            partition_key=target_parent_id,
            patch_operations=count_decr_ops,
        )
        logger.info(f"✅ Successfully deleted reaction {reaction_id}")
        return {"success": True}
    except Exception as e:
        logger.error("❌ Error deleting reaction", extra={"error": str(e)}, exc_info=e)
        raise ServerError(
            message="Error deleting reaction",
            error_code="DELETE_ERROR",
            details={"error": str(e)},
        )
