from typing import List, Optional, Tu<PERSON>, Type, TypeVar, Union
from uuid import UUID

from peepsapi.crud.models.base import PaginationMixin
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.logging import get_logger
from peepsapi.utils.pagination import (
    chronological_pagination_build_query,
    chronological_pagination_encode_next_page_token,
)

logger = get_logger(__name__)


P = TypeVar("P", bound=PaginationMixin)


async def get_page(
    partition_key: Union[UUID, str],
    next_page_token: Optional[str],
    model_class: Type[P],
    model_class_container: Union[CosmosContainer, CosmosContainerAsync],
    page_size,
    where_clause: Optional[str] = None,
    where_clause_parameters: List[dict] = [],
    skip_soft_deleted=False,
    fields: Optional[List[str]] = None,
) -> Tuple[List[P], Optional[str]]:
    """
    Retrieve a paginated list of user-created content items.

    Args:
        partition_key (Union[UUID, str]): The partition key for the Cosmos DB query.
        next_page_token (Optional[str]): Token indicating where to continue pagination.
        model_class (Type[P]): The model class representing the content.
        model_class_container (CosmosContainer): The Cosmos DB container for the content.
        page_size (int): Number of items to return per page.
        where_clause (Optional[str], optional): Additional SQL WHERE clause for filtering. Defaults to None.
        where_clause_parameters (List[dict], optional): Parameters for the WHERE clause. Defaults to [].
        skip_soft_deleted (bool, optional): Whether to skip soft-deleted items. Defaults to False.
        fields (Optional[List[str]], optional): Specific fields to retrieve. Defaults to None.

    Returns:
        Tuple[List[P], Optional[str]]:
            - List of paginated content items.
            - Token for the next page of results, or None if there are no more results.
    """
    # Build query
    query, parameters = chronological_pagination_build_query(
        where_clause=where_clause,
        where_clause_parameters=where_clause_parameters,
        skip_soft_deleted=skip_soft_deleted,
        fields=fields,
        page_size=page_size,
        next_page_token=next_page_token,
        order_field="created_at",
    )

    # Execute query
    if isinstance(model_class_container, CosmosContainerAsync):
        items: List[P] = await model_class_container.query_models(
            query=query,
            parameters=parameters,
            model_class=model_class,
            partition_key=partition_key,
        )  # type: ignore
    elif isinstance(model_class_container, CosmosContainer):
        items: List[P] = model_class_container.query_models(
            query=query,
            parameters=parameters,
            model_class=model_class,
            partition_key=partition_key,
        )  # type: ignore
    else:
        raise TypeError(
            "model_class_container must be CosmosContainer or CosmosContainerAsync"
        )

    # Prepare next_page_token
    if len(items) == page_size:
        last = items[-1]
        next_page = chronological_pagination_encode_next_page_token(
            last.created_at, last.id
        )
    else:
        next_page = None

    return (
        items,
        next_page,
    )
