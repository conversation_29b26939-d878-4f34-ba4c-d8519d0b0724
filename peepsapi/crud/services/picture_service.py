"""Service for managing pictures in CosmosDB and Azure Blob."""

from io import BytesIO
from typing import Any, Optional, Tuple, Type, TypeVar, Union, overload
from uuid import UUID, uuid4

from PIL import Image
from pydantic import BaseModel

from peepsapi.crud.models.picture import Picture
from peepsapi.crud.utils.constants import (
    ALLOWED_IMAGE_TYPES,
    DEFAULT_PICTURE_PATH,
    DEFAULT_VERSION,
    MAX_UPLOAD_SIZE_MB,
    PICTURE_CONTAINER_NAME,
    PICTURE_PATH_TEMPLATE,
)
from peepsapi.models import now
from peepsapi.services.azure_blob import download_blob, upload_blob
from peepsapi.services.cosmos_containers import get_pictures_container
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.error_handling import ResourceNotFoundError, ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)
# Type variable for Pydantic models
T = TypeVar("T", bound=BaseModel)


def build_picture_url_by_id(parent_class: Type[T], parent_id: Union[UUID, str]) -> str:
    entity = parent_class.__name__.lower()  # e.g., "person"
    return PICTURE_PATH_TEMPLATE.format(entity=entity, entity_id=str(parent_id))


def build_picture_url(obj: T) -> str:
    entity = obj.__class__.__name__.lower()  # e.g., "person"
    entity_id = getattr(obj, "id", None)

    if not entity_id:
        raise ValueError("Object must have an 'id' or 'uuid' attribute")

    return PICTURE_PATH_TEMPLATE.format(entity=entity, entity_id=entity_id)


class PictureService:
    """Service layer for picture management."""

    def __init__(self):
        self.container: CosmosContainer = get_pictures_container()

    def extract_image_metadata(self, image_bytes: bytes) -> dict:
        """Extract metadata from an image using Pillow."""
        with Image.open(BytesIO(image_bytes)) as img:
            width, height = img.size
            metadata = {
                "width": width,
                "height": height,
                "format": img.format,
                "mode": img.mode,
                "is_animated": getattr(img, "is_animated", False),
            }
            try:
                exif = img.getexif()
                # Convert EXIF tag numbers to strings
                metadata["exif"] = (
                    {str(k): str(v) for k, v in exif.items()} if exif else {}
                )
            except Exception:  # pragma: no cover - best effort
                metadata["exif"] = {}
            metadata["gps"] = metadata["exif"].get("GPSInfo")
            try:
                metadata["colors"] = img.getcolors(maxcolors=256)
            except Exception:  # pragma: no cover - best effort
                metadata["colors"] = None
        return metadata

    def _get_latest(self, parent_class: Type[T], parent_id: UUID) -> Optional[Picture]:
        query = """
            SELECT * FROM c
            WHERE c.deleted = false AND c.parent_type = @parent_type
            ORDER BY c.version DESC
        """
        parameters = [{"name": "@parent_type", "value": parent_class.__name__.lower()}]
        records: Picture = self.container.query_models(
            query=query,
            model_class=Picture,
            parameters=parameters,
            partition_key=parent_id,
        )
        record = next(iter(records), None)
        return record

    def upload_picture(
        self,
        image_file: bytes,
        parent_class: Type[T],
        parent_id: UUID,
        content_type: str,
        original_filename: Optional[str] = None,
    ) -> Picture:
        """Upload a picture and store metadata."""
        parent_type = parent_class.__name__.lower()
        if content_type not in ALLOWED_IMAGE_TYPES:
            raise ValidationError(
                message=f"Uploaded file must be one of: {ALLOWED_IMAGE_TYPES}",
                error_code="InvalidFileType",
            )
        if len(image_file) > MAX_UPLOAD_SIZE_MB * 1024 * 1024:
            raise ValidationError(
                message=f"Uploaded file exceeds {MAX_UPLOAD_SIZE_MB} size limit",
                error_code="FileTooLarge",
            )
        metadata = self.extract_image_metadata(image_file)
        picture_id = uuid4()
        blob_path = f"{parent_type}/{parent_id}/{picture_id}"
        upload_blob(PICTURE_CONTAINER_NAME, blob_path, image_file, content_type)
        existing = self._get_latest(parent_class, parent_id)
        version = existing.version + 1 if existing else DEFAULT_VERSION
        if existing:
            self.container.patch_model(
                item=existing.id,
                partition_key=existing.parent_id,
                update_fields={"deleted": True},
            )

        record = Picture(
            id=picture_id,
            parent_type=parent_type,
            parent_id=parent_id,
            blob_storage_path=blob_path,
            size_bytes=len(image_file),
            width=metadata.get("width"),
            height=metadata.get("height"),
            thumbnail_paths={},
            upload_timestamp=now(),
            deleted=False,
            version=version,
            original_filename=original_filename,
            content_type=content_type,
            format=metadata.get("format"),
            mode=metadata.get("mode"),
            is_animated=metadata.get("is_animated", False),
            exif=metadata.get("exif", {}),
            gps=metadata.get("gps"),
            colors=metadata.get("colors"),
        )
        self.container.create_model(record)
        return record

    def get_picture(
        self, parent_class: Type[T], parent_id: UUID
    ) -> Tuple[Picture, bytes]:
        record = self._get_latest(parent_class, parent_id)
        if not record:
            default_path = DEFAULT_PICTURE_PATH[parent_class.__name__.lower()]["full"]
            try:
                logger.warning(
                    "Returning default picture",
                    extra={
                        "parent_type": parent_class.__name__.lower(),
                        "parent_id": parent_id,
                    },
                )
                return record, download_blob(PICTURE_CONTAINER_NAME, default_path)
            except Exception:  # pragma: no cover - best effort for missing default
                logger.warning(
                    "Default picture not found",
                    extra={"parent_type": parent_class.__name__.lower()},
                )
                return None, b""
        return record, download_blob(PICTURE_CONTAINER_NAME, record.blob_storage_path)

    def get_picture_metadata(
        self, parent_class: Type[T], parent_id: UUID
    ) -> Optional[Picture]:
        return self._get_latest(parent_class, parent_id)

    def delete_picture(self, parent_class: Type[T], parent_id: UUID) -> None:
        # Note: keep blob for now, only soft delete record
        record = self._get_latest(parent_class, parent_id)
        if not record:
            raise ResourceNotFoundError(
                message="Picture not found", error_code="PictureNotFound"
            )
        self.container.patch_model(
            item=record.id,
            partition_key=record.parent_id,
            update_fields={
                "deleted": True,
                "delete_timestamp": now(),
            },
        )
