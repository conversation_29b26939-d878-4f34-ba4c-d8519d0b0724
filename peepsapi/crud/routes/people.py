"""API endpoints for person management.

This module provides CRUD operations for people, including:
- Listing all people
- Getting a specific person by ID
- Looking up a person by email or phone number
- Creating a new person
- Updating an existing person
- Deleting a person
"""

from typing import List, Optional
from uuid import UUID

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from fastapi import APIRouter, Depends, File, UploadFile, status

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.crud.models.person import Person
from peepsapi.crud.models.picture import Picture
from peepsapi.crud.services.connection_service import (
    get_connection_status_info,
    update_person_connection_preview,
)
from peepsapi.crud.services.people_services import people_service
from peepsapi.crud.services.picture_service import (
    PictureService,
    build_picture_url_by_id,
)
from peepsapi.services.cosmos_containers import (
    get_connections_container,
    get_people_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import (
    ResourceNotFoundError,
    ServerError,
    ValidationError,
)
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/people", tags=["people"])
logger = get_logger(__name__)
picture_service = PictureService()


@router.get("/search", response_model=List[Person])
@handle_exceptions(error_code_prefix="PERSON")
async def search_people(
    name: Optional[str] = None,
    last_name: Optional[str] = None,
    email: Optional[str] = None,
    phone: Optional[str] = None,
    container=Depends(get_people_container),
):
    """Search for people by name, last name, email, or phone number.

    Users will only pass one search parameter at a time.
    All searches support partial matching, so you can search for part of a name,
    email address, or phone number.

    Args:
        name (Optional[str]): First name to search for (partial match supported)
        last_name (Optional[str]): Last name to search for (partial match supported)
        email (Optional[str]): Email to search for (partial match supported)
        phone (Optional[str]): Phone number to search for (partial match supported)
        container: The people container dependency

    Returns:
        List[Person]: List of matching people
    """

    # Determine which parameter was provided
    search_params = {
        "name": (name, "CONTAINS(LOWER(c.name), @value, true)", True),
        "last_name": (last_name, "CONTAINS(LOWER(c.last_name), @value, true)", True),
        "email": (
            email,
            "EXISTS(SELECT VALUE e FROM e IN c.emails WHERE CONTAINS(LOWER(e.address), @value, true))",
            True,
        ),
        "phone": (
            phone,
            "EXISTS(SELECT VALUE p FROM p IN c.phone_numbers WHERE CONTAINS(p.number, @value, true))",
            True,
        ),
    }

    # Find the first non-None parameter
    for param_name, (param_value, condition, lowercase) in search_params.items():
        if param_value:
            # Normalize the value
            search_value = param_value.strip()
            if not search_value:
                continue

            logger.debug(
                "🔍 Searching people by {}={}".format(param_name, search_value),
                extra={"param_name": param_name, "search_value": search_value},
            )

            # Build the query
            cosmos_query = "SELECT * FROM c WHERE {}".format(condition)

            # Prepare the parameter value (lowercase if needed)
            param_value = search_value.lower() if lowercase else search_value

            try:
                # Execute the query
                results = list(
                    container.query_items(
                        query=cosmos_query,
                        parameters=[{"name": "@value", "value": param_value}],
                        enable_cross_partition_query=True,
                    )
                )

                logger.info(f"✅ Search people returned {len(results)} results")

                # Convert results to Person objects
                people = [Person(**result) for result in results]

                return people
            except Exception as e:
                logger.error(
                    "Error executing query: {}".format(str(e)),
                    extra={"error": str(e), "query": cosmos_query},
                )
                raise ServerError(
                    message="Error searching for people",
                    error_code="SEARCH_ERROR",
                    details={"error": str(e)},
                )

    # If no valid parameters were provided, return an empty list
    logger.debug("🔧 No valid search parameters provided, returning empty list")
    return []


@router.get("/lookup/{identifier}", response_model=Person)
@handle_exceptions(error_code_prefix="PERSON")
async def lookup_person(
    identifier: str,
    container=Depends(get_people_container),
):
    """Look up a person by email or phone number.

    Args:
        identifier (str): The email or phone number to look up
        container: The people container dependency

    Returns:
        Person: The person record if found

    Raises:
        ResourceNotFoundError: If the person is not found
    """
    # Determine the type of identifier
    identifier_type = determine_identifier_type(identifier)

    logger.info(f"🎯 Looking up person by {identifier_type}: {identifier}")

    # Check if the identifier exists
    people: Optional[List[Person]] = people_service.get_people_by_identifier(
        identifier_type=identifier_type,
        identifier_value=identifier,
    )

    if not people:
        raise ResourceNotFoundError(
            message="Person with {} '{}' not found".format(identifier_type, identifier),
            error_code="PERSON_NOT_FOUND",
        )

    # Convert to Person model
    person = next(iter(people))

    logger.info(f"✅ Found person {person.id} by {identifier_type}")

    # Return the person data
    return person


@router.get("/", response_model=List[Person])
@handle_exceptions(error_code_prefix="PERSON")
def list_people(container: CosmosContainer = Depends(get_people_container)):
    """Get all people.

    Returns:
        List[Person]: A list of all people
    """
    try:
        people = list(container.read_all_items())
        logger.info(f"✅ Listed {len(people)} people")
        return people
    except Exception as e:
        logger.error("❌ Error listing people", extra={"error": str(e)})
        raise ServerError(
            message="Error listing people",
            error_code="LIST_ERROR",
            details={"error": str(e)},
        )


@router.post("/", response_model=Person)
@handle_exceptions(error_code_prefix="PERSON")
def create_person(person: Person):
    """Create a new person.

    Args:
        person (Person): The person to create

    Returns:
        Person: The created person
    """
    return people_service.create_person(person)


@router.get("/{person_id}", response_model=Person)
@handle_exceptions(error_code_prefix="PERSON")
def get_person(
    person_id: UUID,
    people_container: CosmosContainer = Depends(get_people_container),
    connections_container: CosmosContainer = Depends(get_connections_container),
    current_person: UUID = Depends(auth_service.get_current_person),
):
    """Get a specific person by ID.

    Args:
        person_id (str): The ID of the person to retrieve
        container: The Cosmos DB container dependency

    Returns:
        Person: The requested person

    Raises:
        ResourceNotFoundError: If the person is not found
    """
    logger.info(f"🎯 Getting person {person_id}")

    try:
        person: Person = people_container.read_model(
            id=person_id, partition_key=person_id, model_class=Person
        )
        connection_status, is_requestee = get_connection_status_info(
            current_person, person_id, connections_container
        )

        # status_info[1] == True if request.state.person_id is the requester
        person.connection_requestee = is_requestee
        person.connection_status = connection_status
        return person
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Person {person_id} not found")
        raise ResourceNotFoundError(
            message="Person not found",
            error_code="PERSON_NOT_FOUND",
        )


@router.put("/{person_id}", response_model=Person)
@handle_exceptions(error_code_prefix="PERSON")
def update_person(
    person_id: UUID,
    person: Person,
    container: CosmosContainer = Depends(get_people_container),
    container_connections: CosmosContainer = Depends(get_connections_container),
):
    """Update an existing person.

    Args:
        person_id (str): The ID of the person to update
        person (Person): The updated person data
        container: The Cosmos DB container dependency
        container_connections: The connections container dependency

    Returns:
        Person: The updated person

    Raises:
        ResourceNotFoundError: If the person is not found
    """
    logger.info(f"🎯 Updating person {person_id}")

    try:
        # Check if person exists
        container.read_item(item=person_id, partition_key=person_id)
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Person {person_id} not found for update")
        raise ResourceNotFoundError(
            message="Person not found",
            error_code="PERSON_NOT_FOUND",
        )

    # Update the person
    try:
        container.replace_model(model=person)
        logger.info(f"✅ Successfully updated person {person_id}")
    except Exception as e:
        logger.error(
            f"Error updating person: {e}",
            extra={"person_id": person_id, "error": e},
            exc_info=True,
        )
        raise ServerError(
            message="Error updating person",
            error_code="UPDATE_ERROR",
            details={"error": str(e)},
        )

    update_person_connection_preview(person, container_connections)
    return person


@router.post(
    "/{person_id}/profilePic",
    response_model=Picture,
    status_code=status.HTTP_201_CREATED,
    summary="Upload profile picture",
    description="Upload a new profile picture for the specified person.",
)
@handle_exceptions(error_code_prefix="PICTURE")
async def upload_profile_picture(
    person_id: UUID,
    image_file: UploadFile = File(...),
    people_container: CosmosContainer = Depends(get_people_container),
):
    """Upload a profile picture for a person."""
    logger.info(f"🎯 Uploading profile picture for {person_id}")

    people = people_container.read_model(
        id=person_id, partition_key=person_id, model_class=Person
    )
    if not people:
        logger.warning(f"⚠️ Person {person_id} not found for profile pic upload")
        raise ResourceNotFoundError(
            message="Person not found",
            error_code="PersonNotFound",
        )

    image_bytes = await image_file.read()
    content_type = image_file.content_type
    filename = image_file.filename

    try:
        picture = picture_service.upload_picture(
            image_file=image_bytes,
            parent_class=Person,
            parent_id=person_id,
            content_type=content_type,
            original_filename=filename,
        )
    except ValidationError as e:
        raise e
    except Exception as e:
        logger.error("❌ Error uploading picture", extra={"error": str(e)})
        raise ServerError(message="Error uploading picture", error_code="InternalError")

    try:
        people_container.patch_model(
            item=person_id,
            partition_key=person_id,
            update_fields={
                "profile_pic": build_picture_url_by_id(
                    parent_class=Person, parent_id=person_id
                )
            },
            model_class=Person,
        )
    except Exception as e:
        logger.error("❌ Failed to update person with picture", extra={"error": str(e)})
        raise ServerError(message="Error updating person", error_code="InternalError")

    logger.info(
        f"✅ Profile picture uploaded for {person_id}",
        extra={"picture_id": str(picture.id), "person_id": str(person_id)},
    )
    return picture


@router.delete("/{person_id}")
@handle_exceptions(error_code_prefix="PERSON")
def delete_person(person_id: UUID, container=Depends(get_people_container)):
    """Delete a person.

    Args:
        person_id (UUID): The ID of the person to delete
        container: The Cosmos DB container dependency

    Returns:
        dict: A confirmation message

    Raises:
        ResourceNotFoundError: If the person is not found
    """
    logger.info(f"🎯 Deleting person {person_id}")

    try:
        container.delete_item(item=person_id, partition_key=person_id)
        logger.info(f"✅ Successfully deleted person {person_id}")
        return {"ok": True}
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Person {person_id} not found for deletion")
        raise ResourceNotFoundError(
            message="Person not found",
            error_code="PERSON_NOT_FOUND",
        )


# Helper functions
def determine_identifier_type(identifier: str) -> str:
    """Determine if the identifier is an email or phone number.

    Args:
        identifier (str): The identifier to check

    Returns:
        str: "email" if the identifier is an email, "phone" if it's a phone number
    """
    # Simple email validation - check for @ symbol
    if "@" in identifier and "." in identifier.split("@")[1]:
        return "email"
    # Assume it's a phone number if it's not an email
    # In a production environment, you'd want more robust validation
    return "phone"
