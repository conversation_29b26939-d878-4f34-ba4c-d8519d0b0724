"""API endpoints for handling permanent redirects.

This module provides redirect endpoints for deprecated URL patterns,
ensuring backward compatibility while guiding users to the new endpoint structure.
"""

from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends
from fastapi.responses import RedirectResponse

from peepsapi.auth.services import auth_service

router = APIRouter(prefix="", tags=["redirects"])


@router.get("/people/{person_id}/connections/active")
@router.get("/connections/active")
async def redirect_connections_active(
    person_id: Optional[UUID] = None,
    current_person_id: UUID = Depends(auth_service.get_current_person),
):
    """
    Permanent redirect to /connections/{person_id}/active

    Handles two cases:
    - /people/{person_id}/connections/active -> uses person_id from URL
    - /connections/active -> uses current_person_id from authenticated user
    """
    target_person_id = person_id if person_id is not None else current_person_id
    return RedirectResponse(
        url=f"/connections/{target_person_id}/active",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )


@router.get("/people/{person_id}/posts")
async def redirect_people_posts(person_id: UUID):
    """
    Permanent redirect from /people/{person_id}/posts
    to /posts/{person_id}
    """
    return RedirectResponse(
        url=f"/posts/{person_id}",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )


@router.get("/people/{person_id}/posts/{post_id}")
async def redirect_people_post_by_id(person_id: UUID, post_id: UUID):
    """
    Permanent redirect from /people/{person_id}/posts/{post_id}
    to /posts/{person_id}/{post_id}
    """
    return RedirectResponse(
        url=f"/posts/{person_id}/{post_id}",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )


@router.delete("/people/{person_id}/posts/{post_id}")
async def redirect_people_delete_post(person_id: UUID, post_id: UUID):
    """
    Permanent redirect from DELETE /people/{person_id}/posts/{post_id}
    to DELETE /posts/{person_id}/{post_id}
    """
    return RedirectResponse(
        url=f"/posts/{person_id}/{post_id}",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )


# TODO: remove as the comment creation requires post_author_person_id
#       remove after the new endpoint is confirmed
# @router.post("/posts/{post_id}/comments")
# async def redirect_post_comments_create(post_id: UUID):
#     """
#     Permanent redirect from POST /posts/{post_id}/comments
#     to POST /comments/{post_id}
#     """
#     return RedirectResponse(
#         url=f"/comments/{post_id}",
#         status_code=308,  # 308 = Permanent Redirect (preserves method and body)
#     )


# TODO: remove as the comment creation requires post_author_person_id
#       remove after the new endpoint is confirmed
# @router.post("/posts/{post_id}/comments/{comment_id}")
# async def redirect_post_sub_comments_create(post_id: UUID, comment_id: UUID):
#     """
#     Permanent redirect from POST /posts/{post_id}/comments/{comment_id}
#     to POST /comments/{post_id}/{comment_id}
#     """
#     return RedirectResponse(
#         url=f"/comments/{post_id}/{comment_id}",
#         status_code=308,  # 308 = Permanent Redirect (preserves method and body)
#     )


@router.get("/posts/{post_id}/comments")
async def redirect_post_comments_get(post_id: UUID):
    """
    Permanent redirect from GET /posts/{post_id}/comments
    to GET /comments/{post_id}
    """
    return RedirectResponse(
        url=f"/comments/{post_id}",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )


@router.get("/posts/{post_id}/comments/{comment_id}")
async def redirect_post_sub_comments_get(post_id: UUID, comment_id: UUID):
    """
    Permanent redirect from GET /posts/{post_id}/comments/{comment_id}
    to GET /comments/{post_id}/{comment_id}
    """
    return RedirectResponse(
        url=f"/comments/{post_id}/{comment_id}",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )


@router.patch("/posts/{post_id}/comments/{comment_id}")
async def redirect_post_comments_update(post_id: UUID, comment_id: UUID):
    """
    Permanent redirect from PATCH /posts/{post_id}/comments/{comment_id}
    to PATCH /comments/{post_id}/{comment_id}
    """
    return RedirectResponse(
        url=f"/comments/{post_id}/{comment_id}",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )


@router.delete("/posts/{post_id}/comments/{comment_id}")
async def redirect_post_comments_delete(post_id: UUID, comment_id: UUID):
    """
    Permanent redirect from DELETE /posts/{post_id}/comments/{comment_id}
    to DELETE /comments/{post_id}/{comment_id}
    """
    return RedirectResponse(
        url=f"/comments/{post_id}/{comment_id}",
        status_code=308,  # 308 = Permanent Redirect (preserves method and body)
    )
