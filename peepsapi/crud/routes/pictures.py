"""Picture retrieval and deletion endpoints."""

from io import Bytes<PERSON>
from typing import Type
from uuid import UUID

from fastapi import APIRouter, Response, status
from fastapi.responses import StreamingResponse

from peepsapi.crud.models.picture import Picture
from peepsapi.crud.services.picture_service import PictureService
from peepsapi.crud.utils.constants import PICTURE_PARENT_MAP
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ResourceNotFoundError, ValidationError

router = APIRouter(prefix="/pictures", tags=["pictures"])
picture_service = PictureService()


def _resolve_parent(parent_type: str) -> Type:
    parent_type = parent_type.lower()
    if parent_type not in PICTURE_PARENT_MAP:
        raise ValidationError(
            message="Invalid parent type", error_code="InvalidParentType"
        )
    return PICTURE_PARENT_MAP[parent_type]


@router.get(
    "/{parent_type}/{parent_id}",
    summary="Retrieve picture",
    description="Return the latest uploaded picture bytes for the given parent or a default image if none exists.",
)
@handle_exceptions(error_code_prefix="PICTURE")
def get_picture(parent_type: str, parent_id: UUID) -> StreamingResponse:
    """Return the image bytes for the latest picture of the parent."""
    parent_cls = _resolve_parent(parent_type)
    metadata, picture_bytes = picture_service.get_picture(parent_cls, parent_id)
    content_type = metadata.content_type if metadata else "image/jpeg"
    return StreamingResponse(BytesIO(picture_bytes), media_type=content_type)


@router.get(
    "/{parent_type}/{parent_id}/metadata",
    response_model=Picture,
    summary="Get picture metadata",
    description="Fetch metadata for the latest picture of the given parent.",
)
@handle_exceptions(error_code_prefix="PICTURE")
def get_picture_metadata(parent_type: str, parent_id: UUID) -> Picture:
    """Retrieve metadata for the latest picture of the given parent."""
    parent_cls = _resolve_parent(parent_type)
    metadata = picture_service.get_picture_metadata(parent_cls, parent_id)
    if not metadata:
        raise ResourceNotFoundError(
            message="Picture not found", error_code="PictureNotFound"
        )
    return metadata


@router.delete(
    "/{parent_type}/{parent_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete picture",
    description="Soft delete the latest picture for the given parent.",
)
@handle_exceptions(error_code_prefix="PICTURE")
def delete_picture(parent_type: str, parent_id: UUID) -> Response:
    """Soft delete the latest picture for the given parent."""
    parent_cls = _resolve_parent(parent_type)
    picture_service.delete_picture(parent_cls, parent_id)
    return Response(status_code=status.HTTP_204_NO_CONTENT)
