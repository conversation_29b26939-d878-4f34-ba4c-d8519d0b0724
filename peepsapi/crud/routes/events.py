"""API endpoints for event management.

This module provides CRUD operations for events, including:
- Listing all events
- Getting a specific event
- Creating a new event
- Updating an existing event
- Deleting an event
"""

from typing import List

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from fastapi import APIRouter, Depends

from peepsapi.crud.models.event import Event
from peepsapi.services.cosmos_containers import get_events_container
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ResourceNotFoundError, ServerError
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/events", tags=["events"])
logger = get_logger(__name__)


@router.get("/", response_model=List[Event])
@handle_exceptions(error_code_prefix="EVENT")
def list_events(container=Depends(get_events_container)):
    """Get all events.

    Returns:
        List[Event]: A list of all events
    """
    logger.info("🎯 Listing all events")

    try:
        events = list(container.read_all_items())
        logger.info(f"✅ Found {len(events)} events")
        return events
    except Exception as e:
        logger.error("❌ Error listing events", extra={"error": str(e)})
        raise ServerError(
            message="Error listing events",
            error_code="LIST_ERROR",
            details={"error": str(e)},
        )


@router.get("/{event_id}", response_model=Event)
@handle_exceptions(error_code_prefix="EVENT")
def get_event(event_id: str, container=Depends(get_events_container)):
    """Get a specific event by ID.

    Args:
        event_id (str): The ID of the event to retrieve
        container: The Cosmos DB container dependency

    Returns:
        Event: The requested event

    Raises:
        ResourceNotFoundError: If the event is not found
    """
    logger.info(f"🎯 Getting event {event_id}")

    try:
        event = container.read_item(item=event_id, partition_key=event_id)
        return event
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Event {event_id} not found")
        raise ResourceNotFoundError(
            message="Event not found",
            error_code="EVENT_NOT_FOUND",
        )


@router.post("/", response_model=Event)
@handle_exceptions(error_code_prefix="EVENT")
def create_event(event: Event, container=Depends(get_events_container)):
    """Create a new event.

    Args:
        event (Event): The event to create
        container: The Cosmos DB container dependency

    Returns:
        Event: The created event
    """
    logger.info(f"🎯 Creating event {event.id if event.id else '[auto-generated]'}")

    try:
        result = container.create_model(event)
        logger.info(f"✅ Successfully created event {result['id']}")
        return event
    except Exception as e:
        logger.error("❌ Error creating event", extra={"error": str(e)})
        raise ServerError(
            message="Error creating event",
            error_code="CREATE_ERROR",
            details={"error": str(e)},
        )


@router.put("/{event_id}", response_model=Event)
@handle_exceptions(error_code_prefix="EVENT")
def update_event(event_id: str, event: Event, container=Depends(get_events_container)):
    """Update an existing event.

    Args:
        event_id (str): The ID of the event to update
        event (Event): The updated event data
        container: The Cosmos DB container dependency

    Returns:
        Event: The updated event

    Raises:
        ResourceNotFoundError: If the event is not found
    """
    logger.info(f"🎯 Updating event {event_id}")

    try:
        # Check if event exists
        container.read_item(item=event_id, partition_key=event_id)
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Event {event_id} not found for update")
        raise ResourceNotFoundError(
            message="Event not found",
            error_code="EVENT_NOT_FOUND",
        )

    # Update the event
    try:
        container.upsert_model(event)
        logger.info(f"✅ Successfully updated event {event_id}")
        return event
    except Exception as e:
        logger.error(f"❌ Error updating event {event_id}", extra={"error": str(e)})
        raise ServerError(
            message="Error updating event",
            error_code="UPDATE_ERROR",
            details={"error": str(e)},
        )


@router.delete("/{event_id}")
@handle_exceptions(error_code_prefix="EVENT")
def delete_event(event_id: str, container=Depends(get_events_container)):
    """Delete an event.

    Args:
        event_id (str): The ID of the event to delete
        container: The Cosmos DB container dependency

    Returns:
        dict: A confirmation message

    Raises:
        ResourceNotFoundError: If the event is not found
    """
    logger.info(f"🎯 Deleting event {event_id}")

    try:
        container.delete_item(item=event_id, partition_key=event_id)
        logger.info(f"✅ Successfully deleted event {event_id}")
        return {"ok": True}
    except CosmosResourceNotFoundError:
        logger.warning(f"🚨 Event {event_id} not found for deletion")
        raise ResourceNotFoundError(
            message="Event not found",
            error_code="EVENT_NOT_FOUND",
        )
