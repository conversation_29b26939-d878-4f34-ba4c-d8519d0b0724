from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException

from peepsapi.auth.services import auth_service
from peepsapi.crud.models.comment import Comment, CommentInput, CommentsResponse
from peepsapi.crud.routes.people import router
from peepsapi.crud.routes.posts import router
from peepsapi.crud.services.comment_service import (
    create_post_comment,
    get_comment_author_person_id,
    get_comments_page,
    soft_delete_comment,
)
from peepsapi.models.datetime import UTCDateTime
from peepsapi.services.cosmos_containers import (
    get_active_jobs_container,
    get_comments_container,
    get_people_container,
    get_person_reactions_container,
    get_posts_container,
)
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/comments", tags=["comments"])
logger = get_logger(__name__)


@router.post("/{post_author_person_id}/{post_id}", response_model=Comment)
@router.post(
    "/{post_author_person_id}/{post_id}/{parent_comment_id}", response_model=Comment
)
@handle_exceptions(error_code_prefix="COMMENT")
async def create_comment(
    post_author_person_id: UUID,
    post_id: UUID,
    comment_input: CommentInput,
    parent_comment_id: Optional[UUID] = None,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    posts_container=Depends(get_posts_container),
    comments_container=Depends(get_comments_container),
    active_jobs_container=Depends(get_active_jobs_container),
) -> Comment:
    """
    Create a comment or a sub-comment for a post.

    This endpoint allows an authenticated user to create a new comment on a post, or a sub-comment (reply) to an existing comment.
    If `parent_comment_id` is provided, the new comment will be treated as a sub-comment of the specified parent comment.

    Args:
        post_author_person_id (UUID): The UUID of the person who authored the post.
        post_id (UUID): The UUID of the post to which the comment is being added.
        comment_input (CommentInput): The input data for the comment, including content and any other required fields.
        parent_comment_id (Optional[UUID], optional): The UUID of the parent comment if this is a sub-comment. Defaults to None.
        current_person_id (UUID, optional): The UUID of the currently authenticated user, injected by dependency.
        comments_container: Dependency-injected container for comment operations.
        active_jobs_container: Dependency-injected container for active job operations.

    Returns:
        Comment: The newly created comment or sub-comment object.

    Raises:
        HTTPException: If the post or parent comment does not exist, or if the user is not authorized to comment.

    """
    return await create_post_comment(
        post_id=post_id,
        comment_input=comment_input,
        author_person_id=current_person_id,
        posts_container=posts_container,
        comments_container=comments_container,
        active_jobs_container=active_jobs_container,
        parent_comment_id=parent_comment_id,
        post_author_person_id=post_author_person_id,
    )


@router.get("/{post_id}", response_model=CommentsResponse)
@router.get("/{post_id}/{comment_id}", response_model=CommentsResponse)
@handle_exceptions(error_code_prefix="COMMENT")
async def get_comments(
    post_id: UUID,
    comment_id: Optional[UUID] = None,
    comments_container=Depends(get_comments_container),
    people_container=Depends(get_people_container),
    person_reactions_container: CosmosContainerAsync = Depends(
        get_person_reactions_container
    ),
    current_person_id: UUID = Depends(auth_service.get_current_person),
    next_page: Optional[str] = None,
):
    """
    Retrieve comments for a post.

    This endpoint returns either the root-level comments for a given post (when `comment_id` is None)
    or the sub-comments (replies) for a specific parent comment (when `comment_id` is provided).

    Args:
        post_id (UUID): The UUID of the post whose comments are being retrieved.
        comment_id (Optional[UUID], optional): The UUID of the parent comment to retrieve replies for. If None, retrieves root-level comments.
        comments_container: Dependency-injected container for comment operations.
        people_container: Dependency-injected container for people operations.
        person_reactions_container (CosmosContainerAsync): Dependency-injected container for person reactions.
        current_person_id (UUID): The UUID of the currently authenticated user, injected by dependency.
        next_page (Optional[str], optional): Token for pagination to retrieve the next page of results.

    Returns:
        CommentsResponse: A paginated response containing the list of comments or sub-comments.

    Raises:
        HTTPException: If the post or comment does not exist, or if the user is not authorized to view the comments.
    """
    if comment_id is None:
        where_clause = "c.parent_comment_id = null"
        where_clause_parameters = []
    else:
        where_clause = "c.parent_comment_id = @commentId"
        where_clause_parameters = [{"name": "@commentId", "value": comment_id}]
    return await get_comments_page(
        target_id=post_id,
        target_type="post",
        next_page_token=next_page,
        comments_container=comments_container,
        people_container=people_container,
        where_clause=where_clause,
        where_clause_parameters=where_clause_parameters,
        current_person_id=current_person_id,
        person_reactions_container=person_reactions_container,
    )


@router.patch("/{post_id}/{comment_id}", response_model=Comment)
@handle_exceptions(error_code_prefix="COMMENT")
async def update_comment(
    post_id: UUID,
    comment_id: UUID,
    comment_input: CommentInput,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    comments_container: CosmosContainer = Depends(get_comments_container),
):
    """Update a comment authored by the authenticated person."""
    # Check if the comment exists and is authored by the current person
    if str(current_person_id) != await get_comment_author_person_id(
        id=comment_id,
        target_id=post_id,
        target_type="post",
        comments_container=comments_container,
    ):
        raise HTTPException(status_code=403)

    update = comment_input.model_dump()
    update["updated_at"] = UTCDateTime.now()
    return comments_container.patch_model(
        item=comment_id,
        partition_key=post_id,
        update_fields=update,
        model_class=Comment,
    )


@router.delete("/{post_id}/{comment_id}")
@handle_exceptions(error_code_prefix="COMMENT")
async def delete_comment(
    post_id: UUID,
    comment_id: UUID,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    auth_source: str = Depends(auth_service.get_auth_source),
    comments_container: CosmosContainer = Depends(get_comments_container),
):
    """Soft-delete a comment."""
    author_person_id = await get_comment_author_person_id(
        id=comment_id,
        target_id=post_id,
        target_type="post",
        comments_container=comments_container,
    )

    if not author_person_id:
        raise HTTPException(status_code=404, detail="Comment not found")

    await soft_delete_comment(
        id=comment_id,
        target_id=post_id,
        target_type="post",
        author_person_id=UUID(author_person_id),
        actor_person_id=current_person_id,
        actor_auth_source=auth_source,
        comments_container=comments_container,
    )
