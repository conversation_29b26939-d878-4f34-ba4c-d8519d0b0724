from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends

from peepsapi.auth.services import auth_service
from peepsapi.crud.models.post import PostsResponse
from peepsapi.crud.services.feed_service import get_feed_page
from peepsapi.services.cosmos_containers import (
    get_feeds_container,
    get_people_container,
    get_person_reactions_container,
    get_posts_container,
)
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/feed", tags=["feed"])


@router.get("", response_model=PostsResponse)
async def get_feed(
    next_page: Optional[str] = None,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    feeds_container: CosmosContainerAsync = Depends(get_feeds_container),
    posts_container: CosmosContainer = Depends(get_posts_container),
    people_container: CosmosContainer = Depends(get_people_container),
    person_reactions_container: CosmosContainerAsync = Depends(
        get_person_reactions_container
    ),
):
    return await get_feed_page(
        owner_person_id=current_person_id,
        next_page_token=next_page,
        feeds_container=feeds_container,
        posts_container=posts_container,
        people_container=people_container,
        get_posts=True,
        current_person_id=current_person_id,
        person_reactions_container=person_reactions_container,
    )
