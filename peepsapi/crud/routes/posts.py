"""API endpoints for post management.

This module provides CRUD operations for posts, including:
- Listing all posts
- Getting a specific post
- Creating a new post
- Updating an existing post
- Deleting a post
"""

from typing import List, Optional
from uuid import UUID, uuid4

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from fastapi import APIRouter, Depends

from peepsapi.auth.services import auth_service
from peepsapi.crud.models.post import Post, PostInput, PostsResponse
from peepsapi.crud.routes.people import router
from peepsapi.crud.services.post_service import get_posts_page, soft_delete_post
from peepsapi.jobs import job_utils
from peepsapi.models.datetime import UTCDateTime
from peepsapi.models.job import AddPostToFeedParams, RemovePostFromFeedParams
from peepsapi.services.cosmos_containers import (
    get_active_jobs_container,
    get_people_container,
    get_person_reactions_container,
    get_posts_container,
)
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ServerError
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/posts", tags=["posts"])
logger = get_logger(__name__)


@router.post("/", response_model=Post)
@handle_exceptions(error_code_prefix="POST")
async def create_post(
    post_input: PostInput,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    posts_container: CosmosContainer = Depends(get_posts_container),
    active_jobs_container: CosmosContainerAsync = Depends(get_active_jobs_container),
):
    """Create a new post.

    Returns:
        Post: The created post
    """
    post = Post(
        id=uuid4(),
        author_person_id=current_person_id,
        created_at=UTCDateTime.now(),
        **post_input.model_dump(),
    )

    try:
        # Insert the post
        posts_container.create_model(model=post, model_class=Post)
        logger.info(f"✅ Successfully created post {post.id}")
    except Exception as e:
        logger.error("❌ Error creating post", extra={"error": str(e)})
        raise ServerError(
            message="Error creating post",
            error_code="CREATE_ERROR",
            details={"error": str(e)},
        )

    # Create a job to update feeds
    await job_utils.create(
        action="add_post_to_feed",
        params=AddPostToFeedParams(
            post_id=post.id,
            author_person_id=post.author_person_id,
            created_at=post.created_at,
        ),
        active_jobs_container=active_jobs_container,
    )

    return post


@router.get("/{person_id}", response_model=PostsResponse)
@handle_exceptions(error_code_prefix="POST")
async def get_posts_by_person(
    person_id: UUID,
    next_page: Optional[str] = None,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    posts_container: CosmosContainer = Depends(get_posts_container),
    people_container: CosmosContainer = Depends(get_people_container),
    person_reactions_container: CosmosContainerAsync = Depends(
        get_person_reactions_container
    ),
):
    """Get posts owned by a given person."""
    # NOTE: all posts are public for now
    return await get_posts_page(
        owner_person_id=person_id,
        next_page_token=next_page,
        posts_container=posts_container,
        people_container=people_container,
        skip_soft_deleted=False,
        current_person_id=current_person_id,
        person_reactions_container=person_reactions_container,
    )


@router.get("/{person_id}/{post_id}", response_model=PostsResponse)
@handle_exceptions(error_code_prefix="POST")
async def get_post_by_person_and_id(
    person_id: UUID,
    post_id: UUID,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    posts_container: CosmosContainer = Depends(get_posts_container),
    people_container: CosmosContainer = Depends(get_people_container),
    person_reactions_container: CosmosContainerAsync = Depends(
        get_person_reactions_container
    ),
):
    """Get a post of a person by ID."""
    # NOTE: all posts are public for now
    return await get_posts_page(
        owner_person_id=person_id,
        where_clause="c.id = @postId",
        where_clause_parameters=[{"name": "@postId", "value": post_id}],
        next_page_token=None,
        posts_container=posts_container,
        people_container=people_container,
        current_person_id=current_person_id,
        person_reactions_container=person_reactions_container,
    )


@router.patch("/{post_id}")
@handle_exceptions(error_code_prefix="POST")
async def update_post(
    post_id: UUID,
    post_input: PostInput,
    current_person_id: UUID = Depends(auth_service.get_current_person),
    posts_container: CosmosContainer = Depends(get_posts_container),
):
    """Update a post owned by the authenticated person."""
    update = post_input.model_dump()
    update["updated_at"] = UTCDateTime.now()
    return posts_container.patch_model(
        item=post_id,
        partition_key=current_person_id,
        update_fields=update,
        model_class=Post,
    )


@router.delete("/{person_id}/{post_id}")
@handle_exceptions(error_code_prefix="POST")
async def delete_post(
    person_id: UUID,
    post_id: UUID,
    auth_source: str = Depends(auth_service.get_auth_source),
    current_person_id: UUID = Depends(auth_service.get_current_person),
    posts_container: CosmosContainer = Depends(get_posts_container),
    active_jobs_container: CosmosContainerAsync = Depends(get_active_jobs_container),
):
    """Delete a post."""
    await soft_delete_post(
        id=post_id,
        author_person_id=person_id,
        actor_person_id=current_person_id,
        actor_auth_source=auth_source,
        posts_container=posts_container,
        active_jobs_container=active_jobs_container,
    )

    return {}
