"""
Admin endpoints for person cleanup and related admin operations.
"""

from uuid import UUID

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from fastapi import APIRouter, Depends, status

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.services.cosmos_containers import (
    get_comments_container,
    get_connections_container,
    get_feeds_container,
    get_people_container,
    get_person_reactions_container,
    get_posts_container,
    get_reactions_container,
)
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/admin", tags=["admin"])
logger = get_logger(__name__)


@router.post("/person/clean/{person_id}", status_code=status.HTTP_200_OK)
async def clean_person(
    person_id: UUID,
    auth_source: str = Depends(auth_service.get_auth_source),
    people_container: CosmosContainer = Depends(get_people_container),
    posts_container: CosmosContainer = Depends(get_posts_container),
    comments_container: CosmosContainer = Depends(get_comments_container),
    connections_container: CosmosContainer = Depends(get_connections_container),
    feeds_container: CosmosContainer = Depends(get_feeds_container),
    reactions_container: CosmosContainerAsync = Depends(get_reactions_container),
    person_reactions_container: CosmosContainerAsync = Depends(
        get_person_reactions_container
    ),
):
    """
    Remove a person and all related data (posts, comments, connections) from the system.
    Only allowed if authentication type is Azure AD.
    """
    if auth_source != "azure_ad":
        logger.warning(
            f"⚠️ Unauthorized attempt to clean person {person_id} with auth_source={auth_source}",
            extra={"person_id": str(person_id), "auth_source": auth_source},
        )
        return {"ok": False, "error": "Only Azure AD authentication allowed"}

    # Remove person (ignore if not found)
    try:
        people_container.delete_item(item=person_id, partition_key=person_id)
        logger.info(
            f"🧹 Deleted person {person_id}", extra={"person_id": str(person_id)}
        )
    except CosmosResourceNotFoundError:
        logger.info(
            f"🧹 Person {person_id} already deleted or not found",
            extra={"person_id": str(person_id)},
        )

    # Remove all posts by this person
    post_ids = []
    reaction_target_ids = []
    posts = list(
        posts_container.query_items(
            query="SELECT c.id FROM c WHERE c.author_person_id = @person_id",
            parameters=[{"name": "@person_id", "value": str(person_id)}],
            partition_key=person_id,
        )
    )
    for post in posts:
        post_id = post.get("id") if isinstance(post, dict) else post[0]
        if post_id is None:
            continue
        post_ids.append(post_id)
        reaction_target_ids.append(post_id)
        try:
            posts_container.delete_item(item=post_id, partition_key=person_id)
            logger.info(
                f"🧹 Deleted post {post_id} for person {person_id}",
                extra={"person_id": str(person_id), "post_id": str(post_id)},
            )
        except CosmosResourceNotFoundError:
            logger.info(
                f"🧹 Post {post_id} already deleted or not found",
                extra={"person_id": str(person_id), "post_id": str(post_id)},
            )

    # Remove all comments for those posts
    for post_id in post_ids:
        comments = list(
            comments_container.query_items(
                query="SELECT c.id FROM c WHERE c.target_id = @post_id",
                parameters=[{"name": "@post_id", "value": post_id}],
                partition_key=post_id,
            )
        )
        for comment in comments:
            comment_id = comment.get("id") if isinstance(comment, dict) else comment[0]
            if comment_id is None:
                continue
            reaction_target_ids.append(comment_id)
            try:
                comments_container.delete_item(item=comment_id, partition_key=post_id)
                logger.info(
                    f"🧹 Deleted comment {comment_id} for post {post_id}",
                    extra={
                        "person_id": str(person_id),
                        "post_id": str(post_id),
                        "comment_id": str(comment_id),
                    },
                )
            except CosmosResourceNotFoundError:
                logger.info(
                    f"🧹 Comment {comment_id} already deleted or not found",
                    extra={
                        "person_id": str(person_id),
                        "post_id": str(post_id),
                        "comment_id": str(comment_id),
                    },
                )

    # Remove all connections where person is requester or requestee (both sides)
    connections = list(
        connections_container.query_items(
            query="SELECT c.id, c.person_preview.id AS counterparty_id FROM c",
            parameters=[],
            partition_key=person_id,
        )
    )
    for conn in connections:
        conn_id = conn.get("id") if isinstance(conn, dict) else conn[0]
        counterparty_id = (
            conn.get("counterparty_id") if isinstance(conn, dict) else conn[1]
        )
        if conn_id is None or counterparty_id is None:
            continue
        # Delete connection in this partition
        try:
            connections_container.delete_item(item=conn_id, partition_key=person_id)
            logger.info(
                f"🧹 Deleted connection {conn_id} for person {person_id}",
                extra={"person_id": str(person_id), "conn_id": str(conn_id)},
            )
        except CosmosResourceNotFoundError:
            logger.info(
                f"🧹 Connection {conn_id} already deleted or not found (partition {person_id})",
                extra={"person_id": str(person_id), "conn_id": str(conn_id)},
            )
        # Delete connection in counterparty's partition
        try:
            connections_container.delete_item(
                item=conn_id, partition_key=counterparty_id
            )
            logger.info(
                f"🧹 Deleted connection {conn_id} for person {person_id} in counterparty partition {counterparty_id}",
                extra={
                    "person_id": str(person_id),
                    "conn_id": str(conn_id),
                    "counterparty_id": str(counterparty_id),
                },
            )
        except CosmosResourceNotFoundError:
            logger.info(
                f"🧹 Connection {conn_id} already deleted or not found (partition {counterparty_id})",
                extra={
                    "person_id": str(person_id),
                    "conn_id": str(conn_id),
                    "counterparty_id": str(counterparty_id),
                },
            )

    # Remove all feed records for this person
    items = feeds_container.query_items(
        query="SELECT c.id FROM c",
        parameters=[],
        partition_key=person_id,
    )
    async for feed in items:  # type: ignore
        feed_id = feed.get("id")
        if feed_id is None:
            continue
        try:
            await feeds_container.delete_item(item=feed_id, partition_key=person_id)  # type: ignore
            logger.info(
                f"🧹 Deleted feed record {feed_id} for person {person_id}",
                extra={"person_id": str(person_id), "feed_id": str(feed_id)},
            )
        except CosmosResourceNotFoundError:
            logger.info(
                f"🧹 Feed {feed_id} already deleted or not found",
                extra={"person_id": str(person_id), "feed_id": str(feed_id)},
            )

    # Remove all reactions for each target (post or comment) in reactions_container
    for target_id in reaction_target_ids:
        reactions = [
            item
            async for item in reactions_container.query_items(
                query="SELECT c.id FROM c",
                parameters=[],
                partition_key=target_id,
            )  # type: ignore
        ]  # type: ignore
        for reaction in reactions:
            reaction_id = (
                reaction.get("id") if isinstance(reaction, dict) else reaction[0]
            )
            if not reaction_id:
                continue
            try:
                await reactions_container.delete_item(
                    item=reaction_id, partition_key=target_id
                )
                logger.info(
                    f"🧹 Deleted reaction {reaction_id} for target {target_id} in reactions_container",
                    extra={
                        "target_id": str(target_id),
                        "reaction_id": str(reaction_id),
                    },
                )
            except Exception:
                pass

    # Remove all reactions for this person in person_reactions_container
    person_reactions = [
        item
        async for item in person_reactions_container.query_items(
            query="SELECT c.id FROM c",
            parameters=[],
            partition_key=person_id,
        )  # type: ignore
    ]  # type: ignore
    for reaction in person_reactions:
        reaction_id = reaction.get("id") if isinstance(reaction, dict) else reaction[0]
        if not reaction_id:
            continue
        try:
            await person_reactions_container.delete_item(
                item=reaction_id, partition_key=person_id
            )
            logger.info(
                f"🧹 Deleted reaction {reaction_id} for person {person_id} in person_reactions_container",
                extra={"person_id": str(person_id), "reaction_id": str(reaction_id)},
            )
        except Exception:
            pass

    return {"ok": True, "person_id": str(person_id)}
