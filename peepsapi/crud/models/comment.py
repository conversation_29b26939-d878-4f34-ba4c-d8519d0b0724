"""Comment model for the PeepsAPI application.

This module contains the Comment model, which represents comments on posts, messages, or events.
"""

from typing import List, Literal, Optional
from uuid import UUID

from peepsapi.crud.models.base import PaginationMixin, ReactionMixin
from peepsapi.models import UTCDateTime
from peepsapi.models.base import BaseModelWithExtra

from .base import DeleteMixin, Media, PersonPreview


class CommentInput(BaseModelWithExtra):
    """Input model for creating a post."""

    content: str
    media: List[Media] = []


# ModerationFlag already inherits from BaseModel
class Comment(PaginationMixin, DeleteMixin, ReactionMixin, CommentInput):
    """A reusable comment model for posts, messages, or events.

    Notes:
        - `target_type` indicates the resource type (e.g., "post", "message", "event").
        - `target_id` is the ID of the resource being commented on.
        - `parent_comment_id` enables threaded replies.
        - Partitioning is based on `target_id`.
        - #TODO reference update when any attribute of comment preview is updated
    """

    author_person_id: UUID
    target_id: UUID  # partition key, post_id or event_id
    target_type: Literal["post", "event"]  # Added for resource type
    # type: Literal["ask", "update"]
    updated_at: Optional[UTCDateTime] = None
    parent_comment_id: Optional[UUID] = None
    comments_count: int = 0


class CommentsResponse(BaseModelWithExtra):
    """
    Represents the response for a comments query.

    Attributes:
        items (List[Connection]): A list of comments.
        next_page (Optional[str]): The token for the next page of results, if available.
    """

    people: List[PersonPreview]
    items: List[Comment]
    next_page: Optional[str]
