"""Conversation models for the PeepsAPI application.

This module contains models related to conversations and messages, including the
Conversation model and Message model.
"""

from typing import List, Optional

from peepsapi.models import DateTimeModelMixin, UTCDateTime

from .base import BaseModelWithExtra, Media, ModerationFlag, PersonPreview
from .reaction import Reaction


class Message(DateTimeModelMixin, ModerationFlag):
    """Represents a direct or group message exchanged between users.

    Notes:
        - `conversation_id` groups messages into threads (1-on-1 or group).
        - `sender_id` is the person who sent the message.
        - Messages can include text, media, and reactions.
        - Timestamps are stored as ISO strings.
        - Messages can be soft-deleted using `deleted_for_ids` list.
    """

    id: str
    conversation_id: str
    sender_id: str
    content: str
    parent_message_id: Optional[str] = None
    media: Optional[List[Media]] = []
    reactions: Optional[List[Reaction]] = []
    created_at: UTCDateTime
    updated_at: Optional[UTCDateTime] = None
    deleted_for_ids: Optional[List[str]] = []


class Conversation(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a 1-on-1 or group conversation thread.

    Notes:
        - `participants` holds a list of person IDs involved.
        - For 1-on-1 chats, there are exactly two participants.
        - For group chats, `is_group` is True and may include name and avatar.
        - `created_by` indicates who started the conversation.

    Future Enhancements:
        - Read Receipts
        - Pinned Messages
        - Manage Admin and member for group chats
        - Add new people to existing chat, needs admin for message visibility management
    """

    id: str
    participants: List[PersonPreview]
    is_group: bool
    name: Optional[str] = None
    avatar_url: Optional[str] = None
    created_by: str
    created_at: UTCDateTime
    updated_at: Optional[UTCDateTime] = None
