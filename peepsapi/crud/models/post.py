"""Post model for the PeepsAPI application.

This module contains the Post model, which represents user-created posts in communities.
"""

from typing import List, Literal, Optional
from uuid import UUID

from pydantic import BaseModel

from peepsapi.crud.models.base import (
    CommentPreview,
    DeleteMixin,
    Media,
    PaginationMixin,
    PersonPreview,
    ReactionMixin,
)
from peepsapi.models.base import BaseModelWithExtra
from peepsapi.models.datetime import UTCDateTime


class PostInput(BaseModelWithExtra):
    """Input model for creating a post."""

    content: str
    media: List[Media] = []
    tags: List[str] = []


class Post(PaginationMixin, DeleteMixin, ReactionMixin, PostInput):
    """User-created post."""

    id: UUID
    content: str = ""
    author_person_id: UUID
    updated_at: Optional[UTCDateTime] = None
    parent_post_id: Optional[UUID] = None
    community_id: Optional[UUID] = None
    visibility: Literal["public", "community", "core", "tagged"] = "public"
    comments_count: int = 0
    is_pinned: bool = False
    comment_previews: List[CommentPreview] = []
    # canvas_id: UUID  # TODO: use as partition_key
    # canvas_type: Literal["community", "self"]
    # type: Literal["ask", "update"]


class PostsResponse(BaseModelWithExtra):
    """
    Represents the response for a connections query.

    Attributes:
        items (List[Connection]): A list of connections.
        next_page (Optional[str]): The token for the next page of results, if available.
    """

    people: List[PersonPreview]
    items: List[Post]
    next_page: Optional[str]
