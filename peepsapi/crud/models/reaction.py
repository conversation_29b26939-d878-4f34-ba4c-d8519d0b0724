from typing import List, Literal, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from peepsapi.crud.models.base import (
    DeleteMixin,
    PaginationMixin,
    PersonPreview,
    ReactionType,
)
from peepsapi.models.datetime import DateTimeModelMixin, UTCDateTime

ReactionTargetType = Literal["post", "comment"]


class ReactionInput(BaseModel):
    type: ReactionType = "like"


class Reaction(PaginationMixin, DeleteMixin, ReactionInput, DateTimeModelMixin):
    """Emoji or sentiment applied to a post or comment.

    When type is null it should default to like. We used person_id since most UX
    shows aggregation first and once expanded person previews can be queried using ids.
    """

    id: UUID  # unique ID of reaction
    target_id: UUID  # partition_key for reactions_container, unique for person_reactions_container
    author_person_id: UUID  # partition_key for person_reactions_container, unique for reactions_container

    created_at: UTCDateTime
    updated_at: Optional[UTCDateTime] = None


class ReactionsResponse(BaseModel):
    people: List[PersonPreview]
    items: List["Reaction"]
    next_page: Optional[str]
