"""Person models for the PeepsAPI application.

This module contains models related to people, including the Person model,
<PERSON>ail model, <PERSON>mind<PERSON> model, Achievement model, and Connection model.
"""

from enum import Enum
from typing import List, Optional
from uuid import UUID

from peepsapi.crud.models.base import PersonPreview
from peepsapi.models.base import BaseModelWithExtra


class ConnectionStatus(str, Enum):
    """Represents the status of a connection."""

    REQUESTED = "requested"
    RESCINDED = "rescinded"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    REMOVED = "removed"


class Connection(BaseModelWithExtra):
    """Represents a connection to another person."""

    id: UUID
    requester_person_id: UUID
    requestee_person_id: UUID
    status: ConnectionStatus
    person_preview: PersonPreview
    owner_person_id: UUID


class ConnectionsResponse(BaseModelWithExtra):
    """
    Represents the response for a connections query.

    Attributes:
        items (List[Connection]): A list of connections.
        next_page (Optional[str]): The token for the next page of results, if available.
    """

    items: List[Connection]
    next_page: Optional[str]


class ConnectionRequest(BaseModelWithExtra):
    """
    Represents a request to create a connection between two people.

    Attributes:
        person_id (UUID): The ID of the person to connect with.
    """

    person_id: UUID


class ConnectionUpdate(BaseModelWithExtra):
    """
    Represents an update to the status of a connection.

    Attributes:
        status (ConnectionStatus): The new status to update the connection to.
    """

    status: ConnectionStatus
