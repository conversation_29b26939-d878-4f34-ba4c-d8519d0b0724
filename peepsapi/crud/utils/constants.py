"""Constants for CRUD.
"""

from uuid import UUID

from peepsapi.crud.models import Comment, Person, Post

CONNECTION_NAMESPACE = UUID("c0eec710-0000-0000-0000-000000000000")
REACTION_NAMESPACE = UUID("c0eec711-0000-0000-0000-000000000000")
NOTE_GROUP_NAMESPACE = UUID("a07e5000-0000-0000-0000-000000000000")

# Picture handling
ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif"]
MAX_UPLOAD_SIZE_MB = 10
PICTURE_CONTAINER_NAME = "pictures-originals"
DEFAULT_VERSION = 1
PICTURE_PARENT_MAP = {
    "person": Person,
    "post": Post,
    "comment": Comment,
}
DEFAULT_PICTURE_PATH = {
    "person": {
        "full": "defaults/person.png",
    }
}
PICTURE_PATH_TEMPLATE = "/pictures/{entity}/{entity_id}"
