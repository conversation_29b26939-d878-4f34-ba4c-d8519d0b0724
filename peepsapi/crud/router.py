"""Router for CRUD operations.

Combines all API routers into a single router.
"""

from fastapi import APIRouter

from peepsapi.crud.routes import (
    comments,
    communities,
    connections,
    conversations,
    events,
    feed,
    notes,
    people,
    pictures,
    posts,
    reactions,
    redirects,
)

# Create a router for all CRUD operations
router = APIRouter()

# Include all the API routers

# Active routes
router.include_router(redirects.router)
router.include_router(comments.router)
router.include_router(connections.router)
router.include_router(feed.router)
router.include_router(notes.router)
router.include_router(people.router)
router.include_router(pictures.router)
router.include_router(posts.router)
router.include_router(reactions.router)

# Inactive routes
router.include_router(communities.router)
router.include_router(conversations.router)
router.include_router(events.router)
