import asyncio
from typing import Optional, Union
from uuid import UUID

from peepsapi.crud.services.connection_service import get_active_connection_person_ids
from peepsapi.crud.services.feed_service import (
    add_post_to_feed,
    get_feed_page,
    remove_post_from_feed,
)
from peepsapi.crud.services.post_service import get_posts_page
from peepsapi.jobs.loop import reserve_job
from peepsapi.models.job import (
    AddEachOthersPostsToFeedsParams,
    AddPostToFeedParams,
    Job,
    RemoveEachOthersPostsFromFeedsParams,
    RemovePostFromFeedParams,
)
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


async def execute_create_feed(
    job: Job,
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, CosmosContainer],
):
    raise NotImplementedError


async def execute_add_post_to_feed(
    job: Job,
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, Union[CosmosContainer, CosmosContainerAsync]],
) -> bool:
    if not isinstance(job.params, AddPostToFeedParams):
        logger.error(
            f"❌ | {job.id} | Invalid parameters for add_post_to_feed action: {job.params}"
        )
        return False

    author_person_id = job.params.author_person_id
    post_id = job.params.post_id
    created_at = job.params.created_at

    logger.info(f"🎯 | {job.id} | Adding post {post_id} of {author_person_id} to feeds")

    person_ids: list[UUID] = [author_person_id]
    person_ids.extend(
        get_active_connection_person_ids(
            author_person_id,
            connections_container=container_dependencies["connections"],
        )
    )

    logger.info(
        f"📝 | {job.id} | Retrieved {author_person_id} active connections: {len(person_ids)}"
    )

    # TODO: batch with reserved_until update
    coroutines = []
    for person_id in person_ids:
        coroutines.append(
            add_post_to_feed(
                feed_owner_person_id=person_id,
                post_id=post_id,
                author_person_id=author_person_id,
                created_at=created_at,
                feeds_container=container_dependencies["feeds"],  # type: ignore
                people_container=container_dependencies["people"],
            )
        )

    results = await asyncio.gather(*coroutines, return_exceptions=True)

    is_done = True
    for person_id, result in zip([author_person_id] + person_ids, results):
        if isinstance(result, Exception):
            logger.error(
                f"❌ | {job.id} | Feed update of {person_id} with post {post_id} has failed",
                exc_info=result,
            )
            is_done = False

    if is_done:
        logger.info(f"✅ | {job.id} | Feeds updated with {post_id} successfully")
    else:
        logger.warning(
            f"⚠️ | {job.id} | Feeds update with {post_id} had partially or fully failed, it might be re-attempted"
        )

    return is_done


async def execute_remove_post_from_feed(
    job: Job,
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, Union[CosmosContainer, CosmosContainerAsync]],
) -> bool:
    if not isinstance(job.params, RemovePostFromFeedParams):
        logger.error(
            f"❌ | {job.id} | Invalid parameters for remove_post_from_feed action: {job.params}"
        )
        return False

    author_person_id = job.params.author_person_id
    post_id = job.params.post_id

    logger.info(
        f"🎯 | {job.id} | Removing post {post_id} of {author_person_id} from feeds"
    )

    person_ids = [author_person_id]
    person_ids.extend(
        get_active_connection_person_ids(
            author_person_id,
            connections_container=container_dependencies["connections"],
        )
    )

    logger.info(
        f"📝 | {job.id} | Retrieved {author_person_id} active connections: {len(person_ids)}"
    )

    # TODO: batch with reserved_until update
    coroutines = []
    for person_id in person_ids:
        coroutines.append(
            remove_post_from_feed(
                feed_owner_person_id=person_id,
                post_id=post_id,
                feeds_container=container_dependencies["feeds"],  # type: ignore
                people_container=container_dependencies["people"],
            )
        )
    results = await asyncio.gather(*coroutines, return_exceptions=True)
    is_done = True
    for person_id, result in zip([author_person_id] + person_ids, results):
        if isinstance(result, Exception):
            logger.error(
                f"❌ | {job.id} | Feed removal of {person_id} with post {post_id} has failed",
                exc_info=result,
            )
            is_done = False

    if is_done:
        logger.info(f"✅ | {job.id} | Feeds updated (removed {post_id}) successfully")
    else:
        logger.warning(
            f"⚠️ | {job.id} | Feeds update (removal of {post_id}) had partially or fully failed, it might be re-attempted"
        )

    return is_done


async def execute_remove_each_others_posts_from_feeds(
    job: Job,
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, Union[CosmosContainer, CosmosContainerAsync]],
) -> bool:
    feeds_container = container_dependencies["feeds"]
    people_container = container_dependencies["people"]

    async def remove_posts_of_source_person_from_target_person_feed(
        source_person_id: UUID,
        target_person_id: UUID,
        next_page_token: Optional[str],
    ) -> Optional[str]:
        # Fetch the page of posts metadata from the source user
        page = await get_feed_page(
            owner_person_id=target_person_id,
            next_page_token=next_page_token,
            feeds_container=feeds_container,  # type: ignore
            get_posts=False,
            where_clause="c.author_person_id = @source_person_id",
            where_clause_parameters=[
                {"name": "@source_person_id", "value": str(source_person_id)}
            ],
        )

        # Add posts metadata to the target person's feed
        coros = []
        for post in page.items:
            coros.append(
                remove_post_from_feed(
                    feed_owner_person_id=target_person_id,
                    post_id=post.id,
                    feeds_container=feeds_container,  # type: ignore
                    people_container=people_container,
                )
            )

        if coros:
            await asyncio.gather(*coros, return_exceptions=True)

        return page.next_page

    try:
        if not isinstance(job.params, RemoveEachOthersPostsFromFeedsParams):
            logger.error(
                f"❌ | {job.id} | Invalid parameters for remove_each_others_posts_from_feeds action: {job.params}"
            )
            return False

        person_id_1 = job.params.person_id_1
        person_id_2 = job.params.person_id_2
        logger.info(
            f"🎯 | {job.id} | Cleaning feeds of {person_id_1} and {person_id_2} from each other's posts"
        )

        next_page1: Optional[str] = None
        next_page2: Optional[str] = None
        continue_person1 = True
        continue_person2 = True
        while continue_person1 or continue_person2:
            if continue_person1:
                next_page1 = (
                    await remove_posts_of_source_person_from_target_person_feed(
                        source_person_id=person_id_1,
                        target_person_id=person_id_2,
                        next_page_token=next_page1,
                    )
                )
                continue_person1 = bool(next_page1)

            if continue_person2:
                next_page2 = (
                    await remove_posts_of_source_person_from_target_person_feed(
                        source_person_id=person_id_2,
                        target_person_id=person_id_1,
                        next_page_token=next_page2,
                    )
                )
                continue_person2 = bool(next_page2)

            logger.info(
                f"Added a page of {person_id_1} and {person_id_2} posts to each others feeds"
            )

            if continue_person1 or continue_person2:
                job = await reserve_job(
                    job=job, active_jobs_container=active_jobs_container
                )

        logger.info(
            f"✅ | {job.id} | Feeds of {person_id_1} and {person_id_2} has been cleaned up from each other's posts"
        )
        return True
    except Exception as e:
        logger.error(
            f"❌ | {job.id} | Failed to cleaned up feeds of {person_id_1} and {person_id_2} from each other's posts",
            exc_info=e,
        )
        return False


async def execute_add_each_others_posts_to_feeds(
    job: Job,
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, Union[CosmosContainer, CosmosContainerAsync]],
) -> bool:
    posts_container = container_dependencies["posts"]
    people_container = container_dependencies["people"]
    feeds_container = container_dependencies["feeds"]

    async def add_posts_from_source_person_to_target_person_feed(
        source_person_id: UUID,
        target_person_id: UUID,
        next_page_token: Optional[str],
    ) -> Optional[str]:
        # Fetch the page of posts metadata from the source user
        page = await get_posts_page(
            owner_person_id=source_person_id,
            next_page_token=next_page_token,
            posts_container=posts_container,
            people_container=people_container,
            get_person_previews=False,
            fields=["created_at", "author_person_id", "id"],
        )

        # Add posts metadata to the target person's feed
        coros = []
        for post in page.items:
            coros.append(
                add_post_to_feed(
                    feed_owner_person_id=target_person_id,
                    post_id=post.id,
                    author_person_id=post.author_person_id,
                    created_at=post.created_at,
                    feeds_container=feeds_container,  # type: ignore
                    people_container=people_container,
                )
            )

        if coros:
            await asyncio.gather(*coros, return_exceptions=True)

        return page.next_page

    try:
        if not isinstance(job.params, AddEachOthersPostsToFeedsParams):
            logger.error(
                f"❌ | {job.id} | Invalid parameters for add_each_others_posts_to_feeds action: {job.params}"
            )
            return False

        person_id_1 = job.params.person_id_1
        person_id_2 = job.params.person_id_2
        logger.info(
            f"🎯 Adding posts of {person_id_1} and {person_id_2} to each other's feeds"
        )

        next_page1: Optional[str] = None
        next_page2: Optional[str] = None
        continue_person1 = True
        continue_person2 = True
        while continue_person1 or continue_person2:
            if continue_person1:
                next_page1 = await add_posts_from_source_person_to_target_person_feed(
                    source_person_id=person_id_1,
                    target_person_id=person_id_2,
                    next_page_token=next_page1,
                )
                continue_person1 = bool(next_page1)

            if continue_person2:
                next_page2 = await add_posts_from_source_person_to_target_person_feed(
                    source_person_id=person_id_2,
                    target_person_id=person_id_1,
                    next_page_token=next_page2,
                )
                continue_person2 = bool(next_page2)

            logger.info(
                f"Added a page of {person_id_1} and {person_id_2} posts to each others feeds"
            )

            if continue_person1 or continue_person2:
                job = await reserve_job(
                    job=job, active_jobs_container=active_jobs_container
                )

        logger.info(
            f"✅ | {job.id} | Feeds of {person_id_1} and {person_id_2} have been updated with each other's posts"
        )
        return True
    except Exception as e:
        logger.error(
            f"❌ | {job.id} | Failed to add posts of {person_id_1} and {person_id_2} to each other's feeds",
            exc_info=e,
        )
        return False
