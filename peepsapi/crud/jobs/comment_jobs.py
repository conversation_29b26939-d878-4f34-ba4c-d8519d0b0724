import os
from typing import Union

from azure.cosmos.exceptions import CosmosResourceNotFoundError

from peepsapi.crud.models.comment import Comment
from peepsapi.jobs.loop import reserve_job
from peepsapi.models.job import IncrementCommentCountersParams, Job
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

JOBS_RESERVE_PERIOD_SECONDS = int(os.getenv("JOBS_RESERVE_PERIOD_SECONDS", 30))


async def execute_increment_comment_counters(
    job: Job,
    active_jobs_container: CosmosContainerAsync,
    container_dependencies: dict[str, Union[CosmosContainer, CosmosContainerAsync]],
):
    job_params: IncrementCommentCountersParams = job.params  # type: ignore
    post_id = job_params.post_id
    post_author_person_id = job_params.post_author_person_id
    parent_comment_id = job_params.parent_comment_id
    posts_container = container_dependencies["posts"]
    comments_container = container_dependencies["comments"]
    ops = [{"op": "incr", "path": f"/comments_count", "value": 1}]
    try:
        posts_container.patch_model(
            item=post_id,
            partition_key=post_author_person_id,
            patch_operations=ops,
        )
        logger.info(
            f"✅ | {job.id} | Successfully incremented comment counters for post: {post_id}"
        )
    except CosmosResourceNotFoundError as e:
        logger.warning(
            f"⚠️ | {job.id} | Failed to increment comment counters for post: {post_id} (not found) - {e}"
        )
        return True
    except Exception as e:
        logger.error(
            f"❌ | {job.id} | Unexpected error incrementing comment counters for post: {post_id} - {e}"
        )
        return False

    comments_count = 0
    extend_job_reservation_on = max(1, int(JOBS_RESERVE_PERIOD_SECONDS / 2))
    try:
        while parent_comment_id:
            if comments_count > 0 and comments_count % extend_job_reservation_on == 0:
                job = await reserve_job(
                    job=job, active_jobs_container=active_jobs_container
                )

            comment: Comment = comments_container.patch_model(
                item=parent_comment_id,
                partition_key=post_id,
                patch_operations=ops,
                model_class=Comment,
            )  # type: ignore
            logger.info(
                f"✅ | {job.id} | {comments_count}: Successfully incremented comment counters for comment: {parent_comment_id}"
            )
            comments_count += 1
            parent_comment_id = comment.parent_comment_id
    except CosmosResourceNotFoundError as e:
        logger.warning(
            f"⚠️ | {job.id} | Failed to increment comment counters for comment: {parent_comment_id} (not found) - {e}"
        )
        return True
    except Exception as e:
        logger.error(
            f"❌ | {job.id} | Unexpected error incrementing comment counters for comment: {parent_comment_id} - {e}"
        )
        return False

    return True
