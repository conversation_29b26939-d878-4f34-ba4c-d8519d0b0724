"""Error handling utilities for the PeepsAPI application.

This module provides custom exception classes and error handling utilities
to ensure consistent error responses across the application.
"""

import os
import traceback
from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class PeepsAPIException(HTTPException):
    """Base exception class for PeepsAPI.

    All custom exceptions should inherit from this class to ensure
    consistent error handling and response formatting.
    """

    def __init__(
        self,
        status_code: int,
        error_code: str,
        error_type: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            status_code (int): HTTP status code
            error_code (str): Application-specific error code
            error_type (str): Type of error
            message (str): Human-readable error message
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        self.error_code = error_code
        self.error_type = error_type
        self.details = details
        super().__init__(status_code=status_code, detail=message)


class AuthenticationError(PeepsAPIException):
    """Exception raised for authentication errors."""

    def __init__(
        self,
        message: str = "Authentication failed",
        error_code: str = "AUTH_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message (str, optional): Human-readable error message
            error_code (str, optional): Application-specific error code
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code=error_code,
            error_type="authentication_error",
            message=message,
            details=details,
        )


class AuthorizationError(PeepsAPIException):
    """Exception raised for authorization errors."""

    def __init__(
        self,
        message: str = "Not authorized to perform this action",
        error_code: str = "FORBIDDEN",
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message (str, optional): Human-readable error message
            error_code (str, optional): Application-specific error code
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            error_code=error_code,
            error_type="authorization_error",
            message=message,
            details=details,
        )


class ValidationError(PeepsAPIException):
    """Exception raised for validation errors."""

    def __init__(
        self,
        message: str = "Validation failed",
        error_code: str = "VALIDATION_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message (str, optional): Human-readable error message
            error_code (str, optional): Application-specific error code
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code=error_code,
            error_type="validation_error",
            message=message,
            details=details,
        )


class ResourceNotFoundError(PeepsAPIException):
    """Exception raised when a requested resource is not found."""

    def __init__(
        self,
        message: str = "Resource not found",
        error_code: str = "NOT_FOUND",
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message (str, optional): Human-readable error message
            error_code (str, optional): Application-specific error code
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            error_code=error_code,
            error_type="not_found",
            message=message,
            details=details,
        )


class ServerError(PeepsAPIException):
    """Exception raised for server-side errors."""

    def __init__(
        self,
        message: str = "Internal server error",
        error_code: str = "SERVER_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message (str, optional): Human-readable error message
            error_code (str, optional): Application-specific error code
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=error_code,
            error_type="server_error",
            message=message,
            details=details,
        )


class RateLimitError(PeepsAPIException):
    """Exception raised when rate limits are exceeded."""

    def __init__(
        self,
        message: str = "Rate limit exceeded",
        error_code: str = "RATE_LIMIT",
        details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message (str, optional): Human-readable error message
            error_code (str, optional): Application-specific error code
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            error_code=error_code,
            error_type="rate_limit",
            message=message,
            details=details,
        )


def get_error_details(
    exc: Exception, include_traceback: bool = False
) -> Dict[str, Any]:
    """Get error details from an exception.

    Args:
        exc (Exception): The exception
        include_traceback (bool, optional): Whether to include traceback

    Returns:
        Dict[str, Any]: Error details
    """
    details = {
        "error_class": exc.__class__.__name__,
        "error_message": str(exc),
    }

    if include_traceback:
        details["traceback"] = traceback.format_exc()

    return details
