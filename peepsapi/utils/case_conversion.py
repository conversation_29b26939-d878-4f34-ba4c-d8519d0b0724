"""Case conversion utilities.

This module provides utility functions for converting between snake_case and camelCase.
These functions are used throughout the application to ensure consistent case conventions.
"""

import re
from functools import lru_cache
from typing import Dict, List, Type, Union

JsonType = Union[Dict, List, str, int, float, bool, None]


def validate_case_format(value: str, case_type: str) -> bool:
    """Validate if string follows case convention.

    Args:
        value: String to validate
        case_type: Either 'snake' or 'camel'

    Returns:
        bool: True if string follows convention
    """
    if not value:
        return True

    if case_type == "snake":
        pattern = r"^[a-z][a-z0-9]*(_[a-z0-9]+)*$"
    else:  # camel
        pattern = r"^[a-z][a-zA-Z0-9]*$"
    return bool(re.match(pattern, value))


def normalize_case(value: str, case_type: str) -> str:
    """Normalize string to follow case convention.

    Args:
        value: String to normalize
        case_type: Either 'snake' or 'camel'

    Returns:
        str: Normalized string
    """
    # Remove special characters
    value = re.sub(r"[^a-zA-Z0-9_]", "", value)

    if case_type == "snake":
        return to_snake_case(value)
    return to_camel_case(value)


@lru_cache(maxsize=1024)
def cached_to_camel_case(snake_str: str) -> str:
    """Cached version of to_camel_case for frequently used strings.

    Args:
        snake_str: Snake case string

    Returns:
        str: Camel case string
    """
    return to_camel_case(snake_str)


@lru_cache(maxsize=1024)
def cached_to_snake_case(camel_str: str) -> str:
    """Cached version of to_snake_case for frequently used strings.

    Args:
        camel_str: Camel case string

    Returns:
        str: Snake case string
    """
    return to_snake_case(camel_str)


def convert_type_annotations(cls: Type, to_case: str) -> Type:
    """Convert field names in type annotations.

    Args:
        cls: Class to convert
        to_case: Target case ('snake' or 'camel')

    Returns:
        type: Converted class
    """
    converter = to_snake_case if to_case == "snake" else to_camel_case

    # Convert field names
    new_annotations = {}
    for field_name, field_type in cls.__annotations__.items():
        new_name = converter(field_name)
        new_annotations[new_name] = field_type

    cls.__annotations__ = new_annotations
    return cls


def to_camel_case(snake_str: str) -> str:
    """Convert a snake_case string to camelCase.

    Args:
        snake_str (str): The snake_case string to convert

    Returns:
        str: The camelCase string
    """
    # Handle empty strings
    if not snake_str:
        return snake_str

    # If already camel_case, return as is
    if validate_case_format(snake_str, "camel"):
        return snake_str

    # Validate input format
    if not validate_case_format(snake_str, "snake"):
        raise ValueError(f"Invalid snake_case format: {snake_str}")

    # Split the string by underscores
    components = snake_str.split("_")

    # Capitalize all components except the first one
    camel = components[0] + "".join(x.title() for x in components[1:])

    return camel


def to_snake_case(camel_str: str) -> str:
    """Convert a camelCase string to snake_case.

    Args:
        camel_str (str): The camelCase string to convert

    Returns:
        str: The snake_case string
    """
    # Handle empty strings
    if not camel_str:
        return camel_str

    # If already snake_case, return as is
    if validate_case_format(camel_str, "snake"):
        return camel_str

    # Validate input format
    if not validate_case_format(camel_str, "camel"):
        raise ValueError(f"Invalid camelCase format: {camel_str}")

    # Insert underscore before uppercase letters
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", camel_str)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


def convert_dict_keys_to_camel_case(obj: JsonType) -> JsonType:
    """Recursively convert all keys in a dict from snake_case to camelCase.

    Args:
        obj (JsonType): The object to convert

    Returns:
        JsonType: The converted object
    """
    if isinstance(obj, dict):
        return {
            cached_to_camel_case(k): convert_dict_keys_to_camel_case(v)
            for k, v in obj.items()
        }
    elif isinstance(obj, list):
        return [convert_dict_keys_to_camel_case(i) for i in obj]
    return obj


def convert_dict_keys_to_snake_case(obj: JsonType) -> JsonType:
    """Recursively convert all keys in a dict from camelCase to snake_case.

    Args:
        obj (JsonType): The object to convert

    Returns:
        JsonType: The converted object
    """
    if isinstance(obj, dict):
        return {
            cached_to_snake_case(k): convert_dict_keys_to_snake_case(v)
            for k, v in obj.items()
        }
    elif isinstance(obj, list):
        return [convert_dict_keys_to_snake_case(i) for i in obj]
    return obj
