"""Decorator utilities for the PeepsAPI application.

This module provides decorators for common patterns like error handling,
rate limiting, and authentication.
"""

import functools
import inspect
import traceback
from typing import Any, Callable, TypeVar, cast
from uuid import UUID

from fastapi import Request

from peepsapi.utils.error_handling import (
    PeepsAPIException,
    ResourceNotFoundError,
    ServerError,
    ValidationError,
)
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

F = TypeVar("F", bound=Callable[..., Any])


def handle_exceptions(
    error_code_prefix: str = "API_ERROR",
    log_request: bool = True,
    handle_validation_errors: bool = True,
    handle_not_found_errors: bool = True,
) -> Callable[[F], F]:
    """Decorator for handling exceptions in API endpoints.

    This decorator wraps an endpoint function with standardized error handling.
    It catches exceptions, logs them, and converts them to appropriate HTTP responses.

    Args:
        error_code_prefix (str): Prefix for error codes
        log_request (bool): Whether to log the request
        handle_validation_errors (bool): Whether to handle validation errors
        handle_not_found_errors (bool): Whether to handle not found errors

    Returns:
        Callable: Decorated function
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Get the request object from args or kwargs
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break

            if request is None:
                # Look for request_obj first, then request, but only if it's a Request instance
                if "request_obj" in kwargs and isinstance(
                    kwargs["request_obj"], Request
                ):
                    request = kwargs["request_obj"]
                elif "request" in kwargs and isinstance(kwargs["request"], Request):
                    request = kwargs["request"]

            # Get function name for error codes
            func_name = func.__name__.upper()

            try:
                # Check if the function is a coroutine function (async def)
                if inspect.iscoroutinefunction(func):
                    # Call the original function with await
                    result = await func(*args, **kwargs)
                else:
                    # Call the original function without await
                    result = func(*args, **kwargs)

                # Return the result
                return result

            except ValidationError:
                # Re-raise validation errors if we're not handling them
                if not handle_validation_errors:
                    raise
                # Otherwise, log and re-raise
                if log_request and request:
                    logger.warning(
                        "Validation error in {}".format(func.__name__),
                        request=request,
                    )
                raise

            except ResourceNotFoundError:
                # Re-raise not found errors if we're not handling them
                if not handle_not_found_errors:
                    raise
                # Otherwise, log and re-raise
                if log_request and request:
                    logger.warning(
                        "Resource not found in {}".format(func.__name__),
                        request=request,
                    )
                raise

            except PeepsAPIException:
                # Re-raise other custom exceptions
                raise

            except Exception as e:
                # Log the exception with relevant information
                error_type = type(e).__name__
                error_msg = str(e)
                tb_str = traceback.format_exc()

                # Basic logging without request
                logger.error(
                    "Error {}in {}: {}".format(str(e), func.__name__, error_msg),
                    extra={
                        "error": error_msg,
                        "error_type": error_type,
                        "traceback": tb_str,
                        "function": func.__name__,
                        "func_module": func.__module__,  # Changed from 'module' to 'func_module' to avoid conflict
                    },
                )

                # Convert to ServerError
                raise ServerError(
                    message="Error processing request",
                    error_code="{}_{}".format(error_code_prefix, func_name),
                    details={
                        "error": error_msg,
                        "error_type": error_type,
                        "function": func.__name__,
                        "func_module": func.__module__,  # Changed from 'module' to 'func_module' to avoid conflict
                    },
                )

        return cast(F, wrapper)

    return decorator
