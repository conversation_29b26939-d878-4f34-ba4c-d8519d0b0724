"""Enhanced logging utilities for the PeepsAPI application.

This module provides enhanced logging utilities with contextual information
to improve debugging and monitoring capabilities.
"""

import inspect
import logging
import os
from typing import Any, Dict, Optional

from fastapi import Request


class CustomFormatter(logging.Formatter):
    """Formatter that adds color, context handling, and short level names."""

    COLORS = {
        "DEBUG": "\033[36m",  # Cyan
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[41m",  # Red background
        "GREY": "\033[90m",  # Grey
    }
    RESET = "\033[0m"
    SHORT_NAMES = {
        "DEBUG": "DBG",
        "INFO": "INF",
        "WARNING": "WRN",
        "ERROR": "ERR",
        "CRITICAL": "CRT",
    }

    def format(self, record):
        log_context_enabled = os.getenv("LOG_CONTEXT", "").lower() == "true"
        log_format_string = os.getenv("LOG_FORMAT_STRING", "")
        log_format_timestamp = os.getenv("LOG_FORMAT_TIMESTAMP", "")

        if log_format_string:
            fmt = log_format_string
        else:
            fmt = self._fmt

        if log_context_enabled and "%(context)s" not in fmt:
            fmt = fmt.rstrip() + " | %(context)s"

        # Override datefmt if LOG_FORMAT_TIMESTAMP is set
        datefmt = log_format_timestamp if log_format_timestamp else self.datefmt

        record_copy = logging.makeLogRecord(record.__dict__)

        is_color_enabled = os.getenv("LOG_COLORED", "").lower() == "true"
        is_short_level_enabled = os.getenv("LOG_SHORT_LEVELNAME", "").lower() == "true"

        level_name = record_copy.levelname
        if is_short_level_enabled:
            level_name = self.SHORT_NAMES.get(level_name, level_name)

        if is_color_enabled:
            color = self.COLORS.get(record_copy.levelname, "")
            if color:
                level_name = f"{color}{level_name}{self.RESET}"

        record_copy.levelname = level_name

        context_str = str(getattr(record_copy, "context", "{}"))

        if "%(context)s" in fmt:
            prefix = "CONTEXT: "
            if is_color_enabled:
                prefix = f"{self.COLORS['GREY']}CONTEXT: {self.RESET}"

            if context_str.startswith("CONTEXT: "):
                context_str = context_str.replace("CONTEXT: ", prefix, 1)
            else:
                context_str = f"{prefix}{context_str}"
            record_copy.context = context_str
        else:
            record_copy.context = context_str

        return logging.Formatter(fmt, datefmt).format(record_copy)


class ContextualLogger:
    """Logger that includes contextual information in log entries.

    This logger automatically includes the following context when available:
    - Request path, method, client IP, and user agent
    - Person ID from request state or explicitly provided
    - Authentication source (jwt or azure_ad)
    - For Azure AD auth: user email, name, and any override person ID

    It also uses CustomFormatter to format log entries with colors and short names.
    """

    def __init__(self, name: str):
        """Initialize the logger.

        Args:
            name (str): Logger name
        """
        self.logger = logging.getLogger(name)

    def _log(
        self,
        level: int,
        message: str,
        request: Optional[Request] = None,
        person_id: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> None:
        """Log a message with contextual information if LOG_CONTEXT is true.

        Args:
            level (int): Log level
            message (str): Log message
            request (Optional[Request], optional): FastAPI request object
            person_id (Optional[str], optional): Person ID
            extra (Optional[Dict[str, Any]], optional): Additional context
        """
        log_context_enabled = os.getenv("LOG_CONTEXT", "").lower() == "true"

        if log_context_enabled:
            context = {}

            if extra:
                context.update(extra)

            if request:
                context.update(
                    {
                        "path": request.url.path,
                        "method": request.method,
                        "client_ip": request.client.host if request.client else None,
                        "user_agent": request.headers.get("user-agent"),
                    }
                )

                # Add person_id and auth_source from request state if available
                if hasattr(request.state, "person_id") and not person_id:
                    context["person_id"] = request.state.person_id

                # Add authentication source information
                if hasattr(request.state, "auth_source"):
                    context["auth_source"] = request.state.auth_source

                    # For Azure AD, include additional user information
                    if request.state.auth_source == "azure_ad":
                        if hasattr(request.state, "user_email"):
                            context["user_email"] = request.state.user_email
                        if hasattr(request.state, "user_name"):
                            context["user_name"] = request.state.user_name
                        if (
                            hasattr(request.state, "override_person_id")
                            and request.state.override_person_id
                        ):
                            context[
                                "override_person_id"
                            ] = request.state.override_person_id

            if person_id:
                context["person_id"] = person_id

            self.logger.log(
                level, message, extra={"context": context}, stacklevel=3, **kwargs
            )
        else:
            self.logger.log(level, message, stacklevel=3, **kwargs)

    def debug(
        self,
        message: str,
        request: Optional[Request] = None,
        person_id: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> None:
        """Log a debug message.

        Args:
            message (str): Log message
            request (Optional[Request], optional): FastAPI request object
            person_id (Optional[str], optional): Person ID
            extra (Optional[Dict[str, Any]], optional): Additional context
        """
        self._log(logging.DEBUG, message, request, person_id, extra, **kwargs)

    def info(
        self,
        message: str,
        request: Optional[Request] = None,
        person_id: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> None:
        """Log an info message.

        Args:
            message (str): Log message
            request (Optional[Request], optional): FastAPI request object
            person_id (Optional[str], optional): Person ID
            extra (Optional[Dict[str, Any]], optional): Additional context
        """
        self._log(logging.INFO, message, request, person_id, extra, **kwargs)

    def warning(
        self,
        message: str,
        request: Optional[Request] = None,
        person_id: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> None:
        """Log a warning message.

        Args:
            message (str): Log message
            request (Optional[Request], optional): FastAPI request object
            person_id (Optional[str], optional): Person ID
            extra (Optional[Dict[str, Any]], optional): Additional context
        """
        self._log(logging.WARNING, message, request, person_id, extra, **kwargs)

    def error(
        self,
        message: str,
        request: Optional[Request] = None,
        person_id: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> None:
        """Log an error message.

        Args:
            message (str): Log message
            request (Optional[Request], optional): FastAPI request object
            person_id (Optional[str], optional): Person ID
            extra (Optional[Dict[str, Any]], optional): Additional context
        """
        self._log(logging.ERROR, message, request, person_id, extra, **kwargs)

    def critical(
        self,
        message: str,
        request: Optional[Request] = None,
        person_id: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> None:
        """Log a critical message.

        Args:
            message (str): Log message
            request (Optional[Request], optional): FastAPI request object
            person_id (Optional[str], optional): Person ID
            extra (Optional[Dict[str, Any]], optional): Additional context
        """
        self._log(logging.CRITICAL, message, request, person_id, extra, **kwargs)


def get_logger(name: str) -> ContextualLogger:
    """Get a contextual logger instance.

    Args:
        name (str): Logger name

    Returns:
        ContextualLogger: Contextual logger instance
    """
    logger_instance = ContextualLogger(name)

    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        fmt = getattr(handler, "formatter", None)
        if fmt and hasattr(fmt, "_fmt"):
            handler.setFormatter(CustomFormatter(fmt._fmt, fmt.datefmt))

    # Also patch for all loggers in the hierarchy
    for logger_name in logging.root.manager.loggerDict:
        logger_obj = logging.getLogger(logger_name)
        for handler in getattr(logger_obj, "handlers", []):
            fmt = getattr(handler, "formatter", None)
            if fmt and hasattr(fmt, "_fmt"):
                handler.setFormatter(CustomFormatter(fmt._fmt, fmt.datefmt))

    return logger_instance


def get_caller_context():
    frame = inspect.stack()[2].frame
    func_name = frame.f_code.co_name
    # Try to get the class name if 'self' is in the local variables
    cls_name = (
        frame.f_locals.get("self", None).__class__.__name__
        if "self" in frame.f_locals
        else None
    )
    return f"{cls_name}.{func_name}" if cls_name else func_name
