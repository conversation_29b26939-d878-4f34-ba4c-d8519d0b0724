"""Template utilities for rendering HTML files.

This module provides functions for reading and rendering HTML templates
with dynamic content using Jinja2 templating engine.
"""

import os
from typing import Dict, Optional

from jinja2 import Environment, FileSystemLoader, select_autoescape

from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

# Template directory
TEMPLATE_DIR = os.path.join("static", "templates")

# Create Jinja2 environment
env = Environment(
    loader=FileSystemLoader(TEMPLATE_DIR),
    autoescape=select_autoescape(["html", "xml"]),
    trim_blocks=True,
    lstrip_blocks=True,
)


def read_template(template_name: str) -> Optional[str]:
    """Read a template file from the templates directory.

    Args:
        template_name (str): The name of the template file

    Returns:
        Optional[str]: The template content, or None if the file doesn't exist
    """
    template_path = os.path.join(TEMPLATE_DIR, template_name)
    try:
        with open(template_path, "r") as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"❌ Template file not found", extra={"path": template_path})
        return None
    except Exception as e:
        logger.error(
            f"❌ Error reading template file",
            extra={"path": template_path, "error": str(e)},
        )
        return None


def render_template(template_name: str, context: Dict = None) -> Optional[str]:
    """Render a template with the given context using Jinja2.

    Args:
        template_name (str): The name of the template file
        context (Dict, optional): The context variables to use in the template

    Returns:
        Optional[str]: The rendered template, or None if rendering fails
    """
    if not context:
        context = {}

    try:
        # Get the template from Jinja2 environment
        template = env.get_template(template_name)

        # Render the template with the provided context
        return template.render(**context)
    except FileNotFoundError:
        logger.error(f"❌ Template file not found", extra={"name": template_name})
        return None
    except Exception as e:
        logger.error(
            f"❌ Error rendering template",
            extra={"name": template_name, "error": str(e)},
        )
        return None
