"""Markdown utilities

This module contains utilities for working with markdown.
"""
import re

import html2text
from bs4 import BeautifulSoup

from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

# Optional: customize allowed tags or patterns
SCRIPT_TAG_PATTERN = re.compile(
    r"<\s*script.*?>.*?<\s*/\s*script\s*>", re.IGNORECASE | re.DOTALL
)


def sanitize_markdown(content: str) -> str:
    """
    Convert HTML or mixed content to safe Markdown and protect against XSS.

    Returns:
        markdown (str): Clean, converted markdown content.
    """
    if not isinstance(content, str):
        return ""

    # Step 1: Remove <script> tags
    content_no_scripts = SCRIPT_TAG_PATTERN.sub("", content)

    # Step 2: Parse with BeautifulSoup to extract <img> sources
    soup = BeautifulSoup(content_no_scripts, "html.parser")
    images = [img["src"] for img in soup.find_all("img") if img.get("src")]

    # TODO handle image uploads once we have support for them as well as other media types
    for img in soup.find_all("img"):
        img.decompose()  # Remove from soup to avoid including in markdown

    # Step 3: Convert remaining HTML to markdown
    converter = html2text.HTML2Text()
    converter.ignore_images = True  # We handle images separately
    converter.body_width = 0  # Don't wrap text
    converter.ignore_emphasis = False
    converter.ignore_links = False
    converter.ignore_tables = False
    converter.ignore_anchors = False

    raw_html = str(soup)
    markdown = converter.handle(raw_html)

    # Step 4: Normalize and clean markdown
    markdown = normalize_code_blocks(markdown)

    logger.debug(
        "Sanitized markdown content",
        extra={
            "original_length": len(content),
            "markdown_length": len(markdown),
            "image_count": len(images),
        },
    )

    return markdown.strip()


def normalize_code_blocks(md: str) -> str:
    """Upgrade inline or indented code to fenced blocks when appropriate."""
    lines = md.splitlines()
    normalized = []
    in_code_block = False

    for line in lines:
        if re.match(r"^\s{4,}", line) and not in_code_block:
            normalized.append("```")  # Start of code block
            in_code_block = True

        if not line.strip() and in_code_block:
            normalized.append("```")  # End code block
            in_code_block = False

        normalized.append(line)

    if in_code_block:
        normalized.append("```")

    return "\n".join(normalized)
