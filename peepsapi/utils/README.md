# DateTime Handling in PeepsAPI

This document explains how datetime handling works in the PeepsAPI application.

## Overview

PeepsAPI uses a standardized approach to datetime handling to ensure consistency across the application:

1. All datetimes are stored in UTC timezone
2. All datetimes are serialized to ISO 8601 format strings with 'Z' suffix (e.g., "2024-05-13T19:42:00Z")
3. The `UTCDateTime` class provides type safety and validation
4. The `BaseModelWithExtra` class handles datetime serialization automatically

## Key Components

### BaseModelWithExtra

All models should inherit from `BaseModelWithExtra` (defined in `models/base.py`), which provides:

- Case conversion between snake_case (internal Python code) and camelCase (API)
- Datetime serialization to ISO 8601 strings with 'Z' suffix for UTC timezone

```python
class BaseModelWithExtra(BaseModel):
    model_config = {
        "extra": "allow",
        "alias_generator": to_camel_case,
        "populate_by_name": True,
        "json_encoders": {
            datetime.datetime: lambda dt: dt.isoformat().replace("+00:00", "Z")
        }
    }
```

### UTCDateTime

The `UTCDateTime` class (defined in `utils/datetime_utils.py`) extends the standard `datetime.datetime` class to ensure all datetime objects are in UTC timezone. It provides methods for conversion to and from different formats and timezones.

### DateTimeModelMixin

The `DateTimeModelMixin` (defined in `utils/datetime_utils.py`) provides validation methods for datetime fields and ensures that `UTCDateTime` is properly handled in serialization.

### Utility Functions

The `utils/datetime_utils.py` module provides utility functions for working with datetime objects:

- `now()`: Get the current UTC time
- `from_iso_string()`: Parse ISO format strings
- `from_datetime()`: Convert standard datetime objects
- `is_iso_datetime()`: Check if a string is an ISO format datetime
- `to_iso_string()`: Convert datetimes to ISO format strings
- `format_for_display()`: Format datetimes for display
- `serialize_datetime_dict()`: Serialize dictionaries with datetime objects

## Best Practices

1. Always use `UTCDateTime` for datetime fields in models
2. Always inherit from `BaseModelWithExtra` for models
3. Use `now()` to get the current UTC time
4. Use `from_iso_string()` to parse ISO format strings
5. Use `to_iso_string()` to convert datetimes to ISO format strings

## Example

```python
from peepsapi.models.base import BaseModelWithExtra
from peepsapi.models import UTCDateTime, now

class MyModel(BaseModelWithExtra):
    id: str
    created_at: UTCDateTime = now()
    updated_at: Optional[UTCDateTime] = None
```

When serialized to JSON, the datetime fields will be automatically converted to ISO 8601 strings with 'Z' suffix:

```json
{
    "id": "123",
    "createdAt": "2024-05-13T19:42:00Z",
    "updatedAt": null
}
```
