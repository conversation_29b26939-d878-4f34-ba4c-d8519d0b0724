"""Error monitoring system for the PeepsAPI application.

This module provides functionality for monitoring and tracking errors
in the application, including aggregating error statistics and
providing insights into error patterns.
"""

from collections import defaultdict, deque
from datetime import timed<PERSON><PERSON>
from threading import Lock
from typing import Any, Dict, Optional

from fastapi import Request

from peepsapi.models import UTCDateTime, now
from peepsapi.models.datetime import to_iso_string
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


class ErrorMonitor:
    """Singleton class for monitoring errors in the application.

    This class tracks errors, aggregates statistics, and provides
    insights into error patterns. It uses a thread-safe implementation
    to ensure accurate tracking in a multi-threaded environment.
    """

    _instance = None
    _lock = Lock()

    def __new__(cls):
        """Create a new instance of ErrorMonitor if one doesn't exist.

        Returns:
            ErrorMonitor: The singleton instance
        """
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ErrorMonitor, cls).__new__(cls)
                cls._instance._initialize()
            return cls._instance

    def _initialize(self):
        """Initialize the error monitor."""
        # Recent errors (limited to 1000 entries)
        self._recent_errors = deque(maxlen=1000)

        # Error counts by type
        self._error_counts = defaultdict(int)

        # Error counts by endpoint
        self._endpoint_error_counts = defaultdict(int)

        # Error counts by status code
        self._status_code_counts = defaultdict(int)

        # Error counts by time period (hourly)
        self._hourly_counts = defaultdict(int)

        # Set of IPs with suspicious activity
        self._suspicious_ips = set()

        # Lock for thread safety
        self._data_lock = Lock()

    def track_error(
        self,
        error_type: str,
        error_code: str,
        status_code: int,
        message: str,
        path: str,
        method: str,
        request: Optional[Request] = None,
        person_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        """Track an error occurrence.

        Args:
            error_type (str): Type of error (e.g., "authentication_error")
            error_code (str): Application-specific error code
            status_code (int): HTTP status code
            message (str): Error message
            path (str): Request path
            method (str): HTTP method
            request (Optional[Request], optional): FastAPI request object
            person_id (Optional[str], optional): Person ID
            details (Optional[Dict[str, Any]], optional): Additional error details
        """
        timestamp = now()
        hour_key = timestamp.strftime("%Y-%m-%d %H:00")
        endpoint = f"{method} {path}"

        # Get client IP if request is available
        client_ip = None
        if request and request.client:
            client_ip = request.client.host

        # Create error entry
        error_entry = {
            "timestamp": timestamp,
            "error_type": error_type,
            "error_code": error_code,
            "status_code": status_code,
            "message": message,
            "path": path,
            "method": method,
            "person_id": person_id,
            "client_ip": client_ip,
            "details": details,
        }

        with self._data_lock:
            # Add to recent errors
            self._recent_errors.append(error_entry)

            # Update counts
            self._error_counts[error_type] += 1
            self._endpoint_error_counts[endpoint] += 1
            self._status_code_counts[status_code] += 1
            self._hourly_counts[hour_key] += 1

            # Check for suspicious activity
            if client_ip and self._is_suspicious(client_ip, error_type, timestamp):
                self._suspicious_ips.add(client_ip)
                logger.warning(
                    f"Suspicious activity detected from IP: {client_ip}",
                    extra={"client_ip": client_ip, "error_type": error_type},
                )

    def _is_suspicious(
        self, client_ip: str, error_type: str, timestamp: UTCDateTime
    ) -> bool:
        """Check if activity from an IP is suspicious.

        This method checks for patterns that might indicate suspicious activity,
        such as multiple authentication errors in a short time period.

        Args:
            client_ip (str): Client IP address
            error_type (str): Type of error
            timestamp (datetime): Timestamp of the error

        Returns:
            bool: True if activity is suspicious, False otherwise
        """
        # Check for multiple authentication errors from the same IP
        if error_type == "authentication_error":
            # Count authentication errors from this IP in the last hour
            count = 0
            for error in self._recent_errors:
                if (
                    error["client_ip"] == client_ip
                    and error["error_type"] == "authentication_error"
                    and (timestamp - error["timestamp"]) < timedelta(hours=1)
                ):
                    count += 1

            # More than 5 authentication errors in an hour is suspicious
            return count >= 5

        return False

    def get_statistics(
        self, hours: int = 24, include_details: bool = False
    ) -> Dict[str, Any]:
        """Get error statistics for the specified time period.

        Args:
            hours (int, optional): Number of hours to include in statistics
            include_details (bool, optional): Whether to include error details

        Returns:
            Dict[str, Any]: Error statistics
        """
        cutoff_time = now() - timedelta(hours=hours)

        with self._data_lock:
            # Filter recent errors by time
            recent_errors = [
                error
                for error in self._recent_errors
                if error["timestamp"] >= cutoff_time
            ]

            # Count errors by type
            error_counts = defaultdict(int)
            endpoint_counts = defaultdict(int)
            status_code_counts = defaultdict(int)
            hourly_counts = defaultdict(int)

            for error in recent_errors:
                error_type = error["error_type"]
                endpoint = f"{error['method']} {error['path']}"
                status_code = error["status_code"]
                hour_key = error["timestamp"].strftime("%Y-%m-%d %H:00")

                error_counts[error_type] += 1
                endpoint_counts[endpoint] += 1
                status_code_counts[status_code] += 1
                hourly_counts[hour_key] += 1

            # Prepare recent errors for response
            recent_errors_response = []
            for error in list(recent_errors)[-50:]:  # Last 50 errors
                error_copy = error.copy()
                error_copy["timestamp"] = error_copy["timestamp"].isoformat()

                if not include_details:
                    error_copy.pop("details", None)

                recent_errors_response.append(error_copy)

            return {
                "total_errors": len(recent_errors),
                "error_counts": dict(error_counts),
                "endpoint_counts": dict(endpoint_counts),
                "status_code_counts": dict(status_code_counts),
                "hourly_counts": dict(hourly_counts),
                "suspicious_ips": list(self._suspicious_ips),
                "recent_errors": recent_errors_response,
                "generated_at": to_iso_string(now()),
            }

    def clear_statistics(self):
        """Clear all error statistics."""
        with self._data_lock:
            self._recent_errors.clear()
            self._error_counts.clear()
            self._endpoint_error_counts.clear()
            self._status_code_counts.clear()
            self._hourly_counts.clear()
            self._suspicious_ips.clear()


# Create a singleton instance
error_monitor = ErrorMonitor()


def track_error(
    error_type: str,
    error_code: str,
    status_code: int,
    message: str,
    path: str,
    method: str,
    request: Optional[Request] = None,
    person_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
):
    """Track an error occurrence.

    This function is a convenience wrapper around the ErrorMonitor.track_error method.

    Args:
        error_type (str): Type of error (e.g., "authentication_error")
        error_code (str): Application-specific error code
        status_code (int): HTTP status code
        message (str): Error message
        path (str): Request path
        method (str): HTTP method
        request (Optional[Request], optional): FastAPI request object
        person_id (Optional[str], optional): Person ID
        details (Optional[Dict[str, Any]], optional): Additional error details
    """
    error_monitor.track_error(
        error_type=error_type,
        error_code=error_code,
        status_code=status_code,
        message=message,
        path=path,
        method=method,
        request=request,
        person_id=person_id,
        details=details,
    )


def get_error_statistics(
    hours: int = 24, include_details: bool = False
) -> Dict[str, Any]:
    """Get error statistics for the specified time period.

    This function is a convenience wrapper around the ErrorMonitor.get_statistics method.

    Args:
        hours (int, optional): Number of hours to include in statistics
        include_details (bool, optional): Whether to include error details

    Returns:
        Dict[str, Any]: Error statistics
    """
    return error_monitor.get_statistics(hours=hours, include_details=include_details)


def clear_error_statistics():
    """Clear all error statistics.

    This function is a convenience wrapper around the ErrorMonitor.clear_statistics method.
    """
    error_monitor.clear_statistics()
