import base64
from typing import List, Optional
from uuid import UUID

from peepsapi.models.datetime import UTCDateTime
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)


def chronological_pagination_encode_next_page_token(
    created_at: UTCDateTime,
    id: UUID,
) -> str:
    token = f"{created_at.to_iso_string()}|{id}"
    logger.info(f"Token encoded: {token}")
    return base64.urlsafe_b64encode(token.encode()).decode()


def chronological_pagination_decode_next_page_token(token: str) -> tuple[str, str]:
    decoded = base64.urlsafe_b64decode(token.encode()).decode()
    created_at_str, id_str = decoded.split("|")
    logger.info(f"Token decoded: {created_at_str} {id_str}")
    return created_at_str, id_str


def chronological_pagination_build_query(
    where_clause: Optional[str] = None,
    where_clause_parameters: List[dict] = [],
    skip_soft_deleted=False,
    fields: Optional[List[str]] = None,
    page_size: int = 1,
    next_page_token: Optional[str] = None,
    order_field: str = "created_at",
) -> tuple[str, List[dict]]:
    # Determine the fields for SELECT clause in the query
    select_clause = (
        "SELECT *"
        if fields is None
        else f"SELECT {', '.join([f'c.{field}' for field in fields])}"
    )

    # Convert WHERE clause to list
    if where_clause:
        where_clause_list: List[str] = [f"({where_clause})"]  # type: ignore
    else:
        where_clause_list: List[str] = []  # type: ignore

    if skip_soft_deleted:
        where_clause_list.append("c.deleted = false")

    # Add pagination subquery to the WHERE clause list
    pagination_params = []
    if next_page_token:
        created_at_str, id_str = chronological_pagination_decode_next_page_token(
            next_page_token
        )
        where_clause_list.append(
            f"(c.{order_field} < @createdAt OR (c.{order_field} = @createdAt AND c.id < @id))"
        )
        pagination_params = [
            {"name": "@createdAt", "value": created_at_str},
            {"name": "@id", "value": id_str},
        ]

    # Compile str version of WHERE clause
    where_clause = (
        f"WHERE {' AND '.join(where_clause_list)}" if len(where_clause_list) > 0 else ""
    )

    # Set LIMIT clause parameters
    limit_parameters = [{"name": "@limit", "value": page_size}]

    # Generate SQL query
    query = f"{select_clause} FROM c {where_clause} ORDER BY c.{order_field} DESC, c.id DESC OFFSET 0 LIMIT @limit"
    logger.info(f"query: {query}")

    # Generate parameters
    parameters = where_clause_parameters + pagination_params + limit_parameters
    logger.info(f"parameters: {parameters}")

    return query, parameters
