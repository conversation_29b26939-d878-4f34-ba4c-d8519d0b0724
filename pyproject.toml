[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "peepsapi"
version = "0.1.0"
description = "Peeps API"
requires-python = ">=3.12"
dependencies = [
    # Add your dependencies here
]

[tool.pytest.ini_options]
pythonpath = [
    "."
]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --tb=short"

[tool.setuptools]
packages = ["peepsapi"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 88
extend-ignore = "E203"
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "venv",
]

[tool.poetry.dependencies]
python = "^3.12"
