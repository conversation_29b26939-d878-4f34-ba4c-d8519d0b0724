# peepsAPI

This is a simple API for managing peeps. It includes a secure passkey authentication system based on the WebAuthn standard.

### Prerequisites
- Azure CLI installed and logged in (`az login`)
- Python 3.8 or higher
- GNU Make

> **Note:** On Windows, install dependencies and run commands using WSL (Windows Subsystem for Linux).

## Getting Started

1. Clone the repository:
```bash
git clone https://github.com/yourusername/peepsAPI.git
cd peepsAPI
```

2. Build the API server:
```bash
make build
```
This will:
- Check if `local.peepsapp.ai` is in your hosts file and add it if needed (requires sudo)
- Create a virtual environment in the `venv` directory
- Install dependencies
- Set up pre-commit hooks

> **Note for Windows users:** On Windows, you'll need to manually add the hosts entry:
> ```powershell
> # Run PowerShell as Administrator, then:
> Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "127.0.0.1 local.peepsapp.ai" -Force
> ```

3. Generate trusted certificates (required for WebAuthn):
```bash
# Install mkcert (for trusted certificates)
# macOS: brew install mkcert nss
# Ubuntu/Debian: sudo apt install libnss3-tools mkcert
# Windows: choco install mkcert

# Generate trusted certificates
make generate-certs
```

4. Start the API server:
```bash
# Start HTTPS
make start
```
This will:
- Check that Azure CLI is installed and logged in (`az login`)
- Start the FastAPI server with HTTPS at `https://local.peepsapp.ai:8443`
- Log in via Azure AD at `https://local.peepsapp.ai:8443/dashboard`

5. (Optional) Start with configurable logging:
```bash
# Start with specific LOG_LEVEL, LOG_FORMAT, and AZURE_LOG_LEVEL
make debug ERROR detailed INFO
```
See the [Logging Documentation](LOGGING.md) for more details.

> **Important:** WebAuthn requires properly trusted certificates. mkcert is required for local development with WebAuthn.

### Common Commands

#### Development

- `make build` - Set up hosts file entry, install dependencies, and set up pre-commit hooks
- `make generate-certs` - Generate trusted certificates using mkcert (required for WebAuthn)
- `make start` - Start the API server with HTTPS and hot reload
- `make debug [LOG_LEVEL] [LOG_FORMAT] [AZURE_LOG_LEVEL]` - Start with configurable logging (see [Logging Documentation](LOGGING.md))
- `make test` - Run all tests
- `make clean` - Remove build artifacts

#### Code Quality
- `make format` - Format code using Black and isort
- `make format-check` - Check formatting without making changes
- `make lint` - Run linting with flake8
- `make commit` - Run all checks (format, lint, test) to prepare for committing

#### Deployment
- `make deploy-infra ENV={environment}` - Deploy infrastructure resources to Azure
- `make deploy-service ENV={environment}` - Deploy the application to Azure Container Apps
  - Set `LOG_LEVEL=DEBUG` (or INFO) to control runtime logging level

#### Docker
- `make docker-build` - Build Docker image
- `make docker-run` - Run a new Docker container from the Docker image
- `make docker-logs` - Stream Docker container logs
- `make docker-stop` - Stop and remove Docker container
- `make docker-clean` - Remove Docker image
- `make docker-prune` - Remove dangling Docker images, volumes, and build cache

## Features

### Authentication

The API includes two authentication methods:

#### Passkey Authentication

The API includes a secure passkey authentication system based on the WebAuthn/FIDO2 standard. This provides a passwordless authentication mechanism using biometrics or security keys.

Key features:
- Registration via invite tokens
- Multi-device support
- Account recovery mechanisms
- Comprehensive security features
- HTTPS support for local development
- Custom domain support (local.peepsapp.ai) for testing

> **Note:** WebAuthn requires HTTPS except on localhost. For the best testing experience, use the `local.peepsapp.ai` domain with HTTPS as described above.

For more information, see the [Passkey Authentication Documentation](docs/passkey_authentication.md) and [HTTPS Support Documentation](docs/https_support.md).

#### Azure AD Authentication

The API also supports Azure AD Single Sign-On (SSO) for internal employees, allowing them to authenticate using their Microsoft accounts.

Key features:
- Single Sign-On with Microsoft accounts
- Cookie-based session management
- Integration with existing authentication system
- Audit logging for security monitoring
- Support for local development testing

For more information, see the [SSO Login Documentation](docs/sso-login.md).

### Infrastructure

The API uses Azure Cosmos DB for data storage. The infrastructure is defined using Bicep templates and can be deployed using the `make deploy-infra` command.

Key components:
- Cosmos DB collections for various data types
- Azure Key Vault for secrets management

Infrastructure deployment is handled separately from the application build and startup process. To deploy the infrastructure to different environments, run:

```bash
# Deploy to an environment
make deploy-infra ENV=dev
```

#### Environment Configuration

The application uses a combination of local environment variables and Azure Key Vault for configuration:

- Local development uses the `.env` file in the project root
- Azure environments use Key Vault for secure storage of secrets

When you deploy infrastructure with `make deploy-infra ENV={environment}`, the following happens:

1. Azure resources are created based on the Bicep templates
2. Secrets are stored in Azure Key Vault
3. The application is configured to read from Key Vault in production

To deploy to a new environment:

1. Create parameter files in the `Infra` directory (e.g., `main-parameters-{environment}.json`)
2. Deploy the infrastructure using `make deploy-infra ENV={environment}`
3. Deploy the application using `make deploy-service ENV={environment}`

For more information, see the [Infrastructure Documentation](Infra/README.md).

#### Mock Data Loader

The mock data loader (`scripts/mockdata_loader.py`) is used to generate and manage mock data for testing and development. It creates realistic test data that matches the API's data model.

Key features:
- Generates mock data for all major entities (people, communities, events, etc.)
- Maintains data consistency and relationships
- Supports incremental updates
- Includes data validation and error handling
- Preserves existing data when updating

To use the mock data loader:

```bash
# Generate mock data
python scripts/mockdata_loader.py --env {test,dev,stage}
```

For more information about the data loaders, see the [Data Loader Documentation](docs/scripts/data_loaders.md).

## Running with Docker

### Prerequisites
- Azure Developer CLI installed and logged in (`azd auth login`)
- Docker (tested on v27.5.0)
- GNU Make

To run the API in Docker:

```bash
make docker-run
```

### Docker Commands
- `make docker-build` - Build Docker image
- `make docker-run` - Run a new Docker container from the Docker image
- `make docker-logs` - Stream Docker container logs
- `make docker-stop` - Stop and remove Docker container
- `make docker-clean` - Remove Docker image
- `make docker-prune` - Remove dangling Docker images, volumes, and build cache

## License

Know Your People - 2025. All rights reserved. This software is proprietary and confidential. Unauthorized use, reproduction, or distribution is strictly prohibited.
