"""Scale test utility for the Notes service."""

import argparse
import time
from uuid import uuid4

from peepsapi.crud.models.note import NoteObjectType
from peepsapi.crud.services.notes_service import NotesService


class MemoryContainer:
    """In-memory fallback container for local testing."""

    def __init__(self):
        self.storage = {}

    def create_model(self, model):
        self.storage[model.id] = model

    def query_models(self, query, model_class, parameters=None, partition_key=None):
        aid = parameters[0]["value"]
        oid = parameters[1]["value"]
        return [
            m
            for m in self.storage.values()
            if m.author_id == aid
            and m.object_id == oid
            and not getattr(m, "deleted", False)
        ]

    def read_model(self, id, partition_key, model_class):
        return self.storage[id]

    def patch_model(self, item, partition_key, update_fields, model_class=None):
        obj = self.storage[item]
        for k, v in update_fields.items():
            setattr(obj, k, v)


def run_scale_test(num: int) -> None:
    service = NotesService()
    # Default to memory container to avoid Cosmos dependency
    service.container = MemoryContainer()

    author_id = uuid4()
    object_id = uuid4()

    start = time.perf_counter()
    for i in range(num):
        service.create_note(author_id, object_id, NoteObjectType.PERSON, f"note {i}")
    creation_time = time.perf_counter() - start

    start = time.perf_counter()
    notes = service.get_notes_for_object(author_id, object_id)
    fetch_time = time.perf_counter() - start

    print(f"Created {num} notes in {creation_time:.2f}s")
    print(f"Fetched {len(notes)} notes in {fetch_time:.2f}s")


def main() -> None:
    parser = argparse.ArgumentParser(description="Scale test for notes")
    parser.add_argument(
        "--num", type=int, default=1000, help="Number of notes to create"
    )
    args = parser.parse_args()

    run_scale_test(args.num)


if __name__ == "__main__":
    main()
