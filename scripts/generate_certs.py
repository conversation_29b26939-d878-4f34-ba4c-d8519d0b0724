#!/usr/bin/env python3
"""Script to generate certificates for HTTPS.

This script generates certificates for HTTPS without starting the server.
It tries to use mkcert if available, otherwise falls back to self-signed certificates.

Typical usage:
    python scripts/generate_certs.py
    # or
    make generate-certs
"""

import subprocess
import sys
from pathlib import Path


def generate_certificates():
    """Generate trusted certificates for local development using mkcert.

    Returns:
        bool: True if certificates were generated successfully, False otherwise
    """
    cert_dir = Path("certs")
    cert_file = cert_dir / "cert.pem"
    key_file = cert_dir / "key.pem"

    # Create directory if it doesn't exist
    cert_dir.mkdir(exist_ok=True)

    try:
        # Check if mkcert is installed
        mkcert_check = subprocess.run(
            ["which", "mkcert"], capture_output=True, text=True
        )

        if mkcert_check.returncode != 0:
            print("\n❌ mkcert is not installed. Please install it first:")
            print("  macOS: brew install mkcert nss")
            print("  Ubuntu/Debian: sudo apt install libnss3-tools mkcert")
            print("  More info: https://github.com/FiloSottile/mkcert")
            return False

        print("\n🔐 Generating trusted certificates using mkcert...")

        # Install local CA if needed
        subprocess.run(["mkcert", "-install"], check=True, capture_output=True)

        # Generate certificates
        subprocess.run(
            [
                "mkcert",
                "-cert-file",
                str(cert_file),
                "-key-file",
                str(key_file),
                "local.peepsapp.ai",
            ],
            check=True,
            capture_output=True,
        )

        print(f"\n✅ Trusted certificates generated successfully in {cert_dir}")
        print(
            "  These certificates will be trusted by your browser and will work with WebAuthn."
        )
        return True
    except FileNotFoundError:
        print("\n❌ mkcert command not found. Please install mkcert:")
        print("  macOS: brew install mkcert nss")
        print("  Ubuntu/Debian: sudo apt install libnss3-tools mkcert")
        print("  More info: https://github.com/FiloSottile/mkcert")
        return False
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error generating certificates: {e}")
        print(f"  Error output: {e.stderr.decode() if e.stderr else 'None'}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False


def main():
    """Generate certificates for HTTPS."""
    print("🔑 Generating certificates for HTTPS...")
    if generate_certificates():
        print("\n✅ Certificates generated successfully!")
        print("  You can now start the server with HTTPS using:")
        print("  make start-https")
    else:
        print("\n❌ Failed to generate certificates")
        sys.exit(1)


if __name__ == "__main__":
    main()
