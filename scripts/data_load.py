"""Data loading and transformation script for PeepsAPI.

This script loads sample data from JSON files, transforms it to match the application's
data models, validates it against the models, and loads it into the database.

It handles the following data types:
- People
- Communities
- Events
- Posts
- Comments
"""

import json
import os
import uuid
from typing import Any, Dict, List, Tuple, Type, Union
from uuid import UUID

from pydantic import BaseModel, ValidationError

# Import path_setup to add project root to Python path
if __name__ == "__main__":
    from dotenv import load_dotenv
    import path_setup  # noqa: F401 - imported for side effects

from peepsapi import config
from peepsapi.crud.models.comment import Comment
from peepsapi.crud.models.community import Community
from peepsapi.crud.models.event import Event
from peepsapi.crud.models.person import Person
from peepsapi.crud.models.post import Post
from peepsapi.crud.utils.constants import CONNECTION_NAMESPACE
from peepsapi.models import UTCDateTime, from_iso_string, now, to_iso_string
from peepsapi.services.cosmos_db import DatabaseConnection
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)

# Constants for file names and container names
DATA_FILES = {
    "person": {
        "input": "people.json",
        "output": "transformed_people.json",
        "container": "people",
    },
    "community": {
        "input": "communities.json",
        "output": "transformed_communities.json",
        "container": "communities",
    },
    "event": {
        "input": "events.json",
        "output": "transformed_events.json",
        "container": "events",
    },
    "post": {
        "input": "posts.json",
        "output": "transformed_posts.json",
        "container": "posts",
    },
    "comment": {
        "input": "comments.json",
        "output": "transformed_comments.json",
        "container": "comments",
    },
    "connection": {
        "input": "connections.json",
        "output": "transformed_connections.json",
        "container": "connections",
    },
}

validation_errors = {}


def parse_datetime(
    datetime_str: Union[str, UTCDateTime], for_comparison: bool = False
) -> UTCDateTime:
    """
    Parse a datetime string into a UTCDateTime object.

    Args:
        datetime_str: ISO format datetime string or datetime object
        for_comparison: If True, removes timezone info for consistent comparison
                       If False, preserves timezone information

    Returns:
        UTCDateTime object
    """
    # If already a UTCDateTime object, return it
    if isinstance(datetime_str, UTCDateTime):
        return datetime_str

    # If it's a string, ensure it has timezone info
    if isinstance(datetime_str, str):
        # Add 'Z' if no timezone indicator is present
        if not any(tz in datetime_str for tz in ["Z", "+", "-"]):
            # Handle microseconds if present
            if "." in datetime_str:
                datetime_str = f"{datetime_str}Z"
            else:
                datetime_str = f"{datetime_str}.000Z"
        # Convert to UTCDateTime
        return from_iso_string(datetime_str)

    # Handle unexpected types
    raise ValueError(f"Cannot convert {type(datetime_str)} to UTCDateTime")


def save_transformed_data(data: List[Dict[str, Any]], filename: str, data_dir: str):
    """Save transformed data to a JSON file."""
    output_path = os.path.join(data_dir, filename)
    with open(output_path, "w") as f:
        json.dump(data, f, indent=2, default=str)
        f.write("\n")  # Add newline at the end of the file
    logger.info(f"Saved transformed data to {output_path}")


def load_and_validate_person_data(
    data_dir: str, transformed_events: List[Dict[str, Any]] = None
) -> bool:
    """Load and validate person data. Returns True if validation successful."""
    try:
        person_file = os.path.join(data_dir, DATA_FILES["person"]["input"])
        if not os.path.exists(person_file):
            logger.warning(f"Person data file not found at {person_file}")
            return False

        logger.info(f"Loading person data from {person_file}")
        person_data = load_json_file(person_file)
        logger.info(f"Found {len(person_data)} person records")

        transformed_data = []
        validation_success = True

        for item in person_data:
            try:
                transformed = transform_person_data(item, transformed_events)
                # Validate against schema
                Person(**transformed)
                transformed_data.append(transformed)
            except Exception as e:
                logger.error(f"Error transforming person {item.get('id')}: {str(e)}")
                validation_success = False

        if validation_success:
            logger.info(
                f"Successfully transformed {len(transformed_data)} person records"
            )
            save_transformed_data(
                transformed_data, DATA_FILES["person"]["output"], data_dir
            )
            return True
        return False

    except Exception as e:
        logger.error(f"Error in person data validation: {str(e)}")
        return False


def load_and_validate_community_data(
    data_dir: str, people_data: List[Dict[str, Any]]
) -> bool:
    """Load and validate community data. Returns True if validation successful."""
    try:
        community_file = os.path.join(data_dir, DATA_FILES["community"]["input"])
        if not os.path.exists(community_file):
            logger.warning(f"Community data file not found at {community_file}")
            return False

        logger.info(f"Loading community data from {community_file}")
        community_data = load_json_file(community_file)
        logger.info(f"Found {len(community_data)} community records")

        transformed_data = []
        validation_success = True

        for item in community_data:
            try:
                transformed = transform_community_data(item, people_data, [])
                # Validate against schema
                Community(**transformed)
                transformed_data.append(transformed)
            except Exception as e:
                logger.error(f"Error transforming community {item.get('id')}: {str(e)}")
                validation_success = False

        if validation_success:
            logger.info(
                f"Successfully transformed {len(transformed_data)} community records"
            )
            save_transformed_data(
                transformed_data, DATA_FILES["community"]["output"], data_dir
            )
            return True
        return False

    except Exception as e:
        logger.error(f"Error in community data validation: {str(e)}")
        return False


def load_and_validate_event_data(
    data_dir: str, people_data: List[Dict[str, Any]]
) -> bool:
    """Load and validate event data. Returns True if validation successful."""
    try:
        event_file = os.path.join(data_dir, DATA_FILES["event"]["input"])
        if not os.path.exists(event_file):
            logger.warning(f"Event data file not found at {event_file}")
            return False

        logger.info(f"Loading event data from {event_file}")
        event_data = load_json_file(event_file)
        logger.info(f"Found {len(event_data)} event records")

        transformed_data = []
        validation_success = True

        for item in event_data:
            try:
                transformed = transform_event_data(item, people_data, [])
                # Validate against schema
                Event(**transformed)
                transformed_data.append(transformed)
            except Exception as e:
                logger.error(f"Error transforming event {item.get('id')}: {str(e)}")
                validation_success = False

        if validation_success:
            logger.info(
                f"Successfully transformed {len(transformed_data)} event records"
            )
            save_transformed_data(
                transformed_data, DATA_FILES["event"]["output"], data_dir
            )
            return True
        return False

    except Exception as e:
        logger.error(f"Error in event data validation: {str(e)}")
        return False


def load_and_validate_post_data(
    data_dir: str, people_data: List[Dict[str, Any]]
) -> bool:
    """Load and validate post data. Returns True if validation successful."""
    try:
        post_file = os.path.join(data_dir, DATA_FILES["post"]["input"])
        if not os.path.exists(post_file):
            logger.warning(f"Post data file not found at {post_file}")
            return False

        logger.info(f"Loading post data from {post_file}")
        post_data = load_json_file(post_file)
        logger.info(f"Found {len(post_data)} post records")

        # Load comments to populate post comments field
        comment_file = os.path.join(data_dir, DATA_FILES["comment"]["input"])
        comments_data = []
        if os.path.exists(comment_file):
            logger.info(f"Loading comment data from {comment_file}")
            comments_data = load_json_file(comment_file)
            logger.info(f"Found {len(comments_data)} comment records")

        transformed_data = []
        validation_success = True

        for item in post_data:
            try:
                transformed = transform_post_data(item, people_data, comments_data)

                # Populate comments field with top 10 most recent comments
                if comments_data:
                    post_comments = get_top_comments_for_post(item["id"], comments_data)
                    transformed["comments"] = post_comments

                # Validate against schema
                Post(**transformed)
                transformed_data.append(transformed)
            except Exception as e:
                logger.error(
                    f"Error transforming post {item.get('id')}:{print(item)} {str(e)}"
                )
                validation_success = False

        if validation_success:
            logger.info(
                f"Successfully transformed {len(transformed_data)} post records"
            )
            save_transformed_data(
                transformed_data, DATA_FILES["post"]["output"], data_dir
            )
            return True
        return False

    except Exception as e:
        logger.error(f"Error in post data validation: {str(e)}")
        return False


def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """Load and parse a JSON file."""
    with open(file_path, "r") as f:
        return json.load(f)


def get_person_preview(
    person_id: Union[str, UUID], people_data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Get PersonPreview data for a given person ID."""
    # Convert string ID to UUID if needed
    if isinstance(person_id, str):
        try:
            person_id = UUID(person_id)
        except ValueError:
            raise ValueError(f"Invalid UUID string: {person_id}")

    person = next((p for p in people_data if UUID(p["id"]) == person_id), None)
    if not person:
        raise ValueError(f"Person with ID {person_id} not found")
    return {
        "id": str(person["id"]),  # Convert UUID to string for JSON serialization
        "name": person["name"],
        "last_name": person.get("last_name", ""),
        "current_role": person.get("current_role", ""),
        "current_company": person.get("current_company", ""),
        "profile_picture_url": person.get("profile_picture_url", ""),
    }


def update_person_with_event_details(
    person_data: List[Dict[str, Any]], transformed_events: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Update person data with event details after both people and events are transformed.

    Args:
        person_data: List of transformed person data
        transformed_events: List of transformed event data

    Returns:
        Updated list of person data with event details
    """
    for person in person_data:
        if person.get("upcoming_rsvp_events"):
            upcoming_events = []
            for event in person["upcoming_rsvp_events"]:
                if transformed_events:
                    event_data = next(
                        (e for e in transformed_events if e["id"] == event["event_id"]),
                        None,
                    )
                    if event_data:
                        upcoming_events.append(
                            {
                                "id": event_data["id"],
                                "title": event_data["title"],
                                "start_time": event_data["start_time"],
                                "location": event_data["location"],
                            }
                        )
            person["upcoming_events"] = upcoming_events if upcoming_events else None
            # Remove the temporary field
            del person["upcoming_rsvp_events"]
    return person_data


def transform_person_data(
    data: Dict[str, Any], transformed_events: List[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Transform person JSON data to match Person model schema.

    Args:
        data: The person data to transform
        transformed_events: Optional list of transformed event data to use for upcoming events
    """
    # Convert string ID to UUID if needed
    if isinstance(data["id"], str):
        try:
            data["id"] = str(UUID(data["id"]))
        except ValueError:
            # Generate a new UUID if the provided one is invalid
            logger.warning(f"Invalid UUID string: {data['id']}. Generating new UUID.")
            data["id"] = str(UUID(int=hash(data["id"]) & ((1 << 128) - 1)))

    # Transform social links with defaults
    social_links = []
    default_social_links = {
        "linkedin": f"https://linkedin.com/in/{data.get('name', '').lower().replace(' ', '-')}",
        "twitter": f"https://twitter.com/{data.get('name', '').lower().replace(' ', '')}",
        "github": f"https://github.com/{data.get('name', '').lower().replace(' ', '')}",
        "facebook": f"https://facebook.com/{data.get('name', '').lower().replace(' ', '')}",
        "instagram": f"https://instagram.com/{data.get('name', '').lower().replace(' ', '')}",
    }

    # Add default social links if none provided
    if not data.get("social_links"):
        for platform, url in default_social_links.items():
            social_links.append({"platform": platform, "url": url})
    else:
        # Add provided social links
        for platform, value in data["social_links"].items():
            if value:  # Only add non-empty social links
                social_links.append({"platform": platform, "url": value})

    # Transform phone numbers
    phone_numbers = []
    if data.get("mobile_number"):
        phone_numbers.append(
            {
                "type": "mobile",
                "number": data["mobile_number"],
                "active_since": parse_datetime(now()),
                "verified": False,
                "deactivated_at": None,
            }
        )
    # Also check for WhatsApp numbers in social_ids
    if data.get("social_ids"):
        for social_id in data["social_ids"]:
            if social_id.get("platform") == "whatsapp" and social_id.get("id"):
                # Only add if it's not already in phone_numbers
                if not any(p["number"] == social_id["id"] for p in phone_numbers):
                    phone_numbers.append(
                        {
                            "type": "whatsapp",
                            "number": social_id["id"],
                            "active_since": parse_datetime(now()),
                            "verified": False,
                            "deactivated_at": None,
                        }
                    )

    # Transform emails
    emails = []
    if data.get("emails"):
        # Transform each email in the list
        for email_data in data["emails"]:
            email = {
                "type": email_data.get("type", "personal"),
                "address": email_data["address"],
                "active_since": parse_datetime(email_data.get("active_since", now())),
                "verified": email_data.get("verified", False),
            }
            if email_data.get("deactivated_at"):
                email["deactivated_at"] = parse_datetime(email_data["deactivated_at"])
            emails.append(email)
    elif data.get("email"):
        # If only a single email is provided, create an email object
        emails.append(
            {
                "type": "personal",
                "address": data["email"],
                "active_since": parse_datetime(now()),
                "verified": False,
            }
        )

    if not emails:
        raise ValueError(f"No email found for person {data['id']}")

    # Transform achievements
    achievements = []
    if data.get("achievements"):
        for achievement in data["achievements"]:
            achievements.append(
                {
                    "name": achievement["name"],
                    "color": achievement["color"],
                    "comment": achievement["comment"],
                }
            )

    # Transform upcoming events
    upcoming_events = []
    if data.get("upcoming_rsvp_events"):
        for event in data["upcoming_rsvp_events"]:
            if transformed_events:
                event_data = next(
                    (e for e in transformed_events if e["id"] == event["event_id"]),
                    None,
                )
                if event_data:
                    upcoming_events.append(
                        {
                            "id": event_data["id"],
                            "title": event_data["title"],
                            "start_time": event_data["start_time"],
                            "location": event_data["location"],
                        }
                    )
    print(phone_numbers)
    return {
        "id": data["id"],
        "name": data["name"],
        "last_name": data.get("last_name", ""),
        "bio": data.get("bio", ""),
        "profile_picture_url": data.get("profile_picture_url", ""),
        "current_role": data.get("role", ""),
        "current_company": data.get("current_company", ""),
        "location": data.get("location", ""),
        "type": data.get("type", "member"),
        "member_since": parse_datetime(data.get("member_since", now())),
        "invited_by_id": data.get("invited_by_id"),
        "social_links": social_links,
        "phone_numbers": phone_numbers,
        "emails": emails,
        "achievements": achievements if achievements else None,
        "upcoming_events": upcoming_events if upcoming_events else None,
    }


def transform_community_data(
    data: Dict[str, Any],
    people_data: List[Dict[str, Any]],
    all_events: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """Transform community JSON data to match Community model schema."""
    # Validate required fields
    if not data.get("name"):
        raise ValueError("Community name is required")
    if not data.get("description"):
        raise ValueError("Community description is required")

    # Convert string IDs to UUIDs if needed
    if isinstance(data["id"], str):
        try:
            data["id"] = str(UUID(data["id"]))
        except ValueError:
            # Generate a new UUID if the provided one is invalid
            logger.warning(f"Invalid UUID string: {data['id']}. Generating new UUID.")
            data["id"] = str(UUID(int=hash(data["id"]) & ((1 << 128) - 1)))

    # Handle created_by field - try creator_id first, then look for admin member
    created_by = data.get("creator_id")
    if not created_by:
        # Look for an admin member
        admin_member = next(
            (m for m in data.get("members", []) if m.get("role") == "admin"), None
        )
        if admin_member:
            created_by = admin_member["person_id"]
        elif not people_data:
            raise ValueError("No people data available to set community creator")
        else:
            # If no admin found, use the first person in people_data
            created_by = people_data[0]["id"]
            logger.warning(
                f"No creator_id or admin member found for community {data['id']}. Using first person as creator."
            )

    if isinstance(created_by, str):
        try:
            created_by = str(UUID(created_by))
        except ValueError:
            # Generate a new UUID if the provided one is invalid
            logger.warning(
                f"Invalid UUID string for created_by: {created_by}. Generating new UUID."
            )
            created_by = str(UUID(int=hash(created_by) & ((1 << 128) - 1)))

    # Get creator's preview data
    creator = next((p for p in people_data if p["id"] == created_by), None)
    if not creator:
        raise ValueError(f"Creator with ID {created_by} not found in people data")

    created_by_preview = {
        "id": creator["id"],
        "name": creator["name"],
        "last_name": creator.get("last_name", ""),
        "current_role": creator.get("current_role", ""),
        "current_company": creator.get("current_company", ""),
        "profile_picture_url": creator.get("profile_picture_url", ""),
    }

    # Transform settings with defaults
    settings = {
        "allow_member_invites": True,
        "allow_member_posts": True,
        "allow_member_events": True,
        "allow_member_comments": True,
        "allow_member_likes": True,
        "allow_member_media": True,
        "allow_member_tags": True,
        "allow_member_visibility": True,
    }
    # Update with any provided settings
    for key, value in data.get("settings", {}).items():
        if value is not None:  # Only add non-null settings
            settings[key] = value

    # Transform members
    members = []
    for member in data.get("members", []):
        if not member.get("person_id"):
            logger.warning(
                f"Skipping member without person_id in community {data['id']}"
            )
            continue

        if isinstance(member["person_id"], str):
            try:
                member["person_id"] = str(UUID(member["person_id"]))
            except ValueError:
                # Generate a new UUID if the provided one is invalid
                logger.warning(
                    f"Invalid UUID string for member: {member['person_id']}. Generating new UUID."
                )
                member["person_id"] = str(
                    UUID(int=hash(member["person_id"]) & ((1 << 128) - 1))
                )

        # Convert joined_at to string
        joined_at = member.get("joined_at", now())
        if isinstance(joined_at, (UTCDateTime,)):
            joined_at = to_iso_string(joined_at)

        members.append(
            {
                "person_id": member["person_id"],
                "role": member.get("role", "member"),
                "joined_at": joined_at,
            }
        )

    # Transform events
    events = []
    for event_id in data.get("events", []):
        if not event_id:
            logger.warning(f"Skipping empty event_id in community {data['id']}")
            continue

        if isinstance(event_id, str):
            try:
                event_id = str(UUID(event_id))
            except ValueError:
                # Generate a new UUID if the provided one is invalid
                logger.warning(
                    f"Invalid UUID string for event: {event_id}. Generating new UUID."
                )
                event_id = str(UUID(int=hash(event_id) & ((1 << 128) - 1)))

        event = next((e for e in all_events if e["id"] == event_id), None)
        if event:
            events.append(
                {
                    "id": event["id"],
                    "title": event["title"],
                    "start_time": parse_datetime(event["start_time"]),
                    "end_time": parse_datetime(event["end_time"]),
                    "location": event.get("location", ""),
                    "cover_image_url": event.get("cover_image_url", ""),
                }
            )

    # Get upcoming events (events that haven't started yet)
    now_time = now()
    upcoming_events = [
        event for event in events if parse_datetime(event["start_time"]) > now_time
    ]

    # Generate social links as a list
    social_links = []
    default_social_links = {
        "linkedin": f"https://linkedin.com/company/{data['name'].lower().replace(' ', '-')}",
        "twitter": f"https://twitter.com/{data['name'].lower().replace(' ', '')}",
        "github": f"https://github.com/{data['name'].lower().replace(' ', '')}",
        "facebook": f"https://facebook.com/{data['name'].lower().replace(' ', '')}",
        "instagram": f"https://instagram.com/{data['name'].lower().replace(' ', '')}",
    }

    # Add default social links if none provided
    if not data.get("social_links"):
        for platform, url in default_social_links.items():
            social_links.append({"platform": platform, "url": url})
    else:
        # Add provided social links
        for platform, value in data["social_links"].items():
            if value:  # Only add non-empty social links
                social_links.append({"platform": platform, "url": value})

    return {
        # Required fields
        "id": data["id"],
        "name": data["name"],
        "description": data["description"],
        "created_by": created_by_preview,
        "created_at": parse_datetime(data.get("created_at", now())),
        "updated_at": parse_datetime(data.get("updated_at", now())),
        # Optional fields with defaults
        "cover_image_url": data.get("cover_image_url", ""),
        "settings": settings,
        "members": members,
        "events": events,
        "tags": data.get("tags", []),
        "visibility": data.get("visibility", "public"),
        "memberCount": len(members),
        "profile_picture_url": data.get("profile_picture_url", ""),
        "coverPictureUrl": data.get("cover_image_url", ""),
        "location": data.get("location", "San Francisco, CA"),
        "website": data.get(
            "website", f"https://{data['name'].lower().replace(' ', '')}.com"
        ),
        "socialLinks": social_links,
        "upcomingEvents": upcoming_events,
    }


def get_timezone_from_location(location: str) -> str:
    """Get timezone based on location."""
    location = location.lower()
    if "seattle" in location:
        return "PST"
    elif "berlin" in location:
        return "CEST"
    elif "london" in location:
        return "BST"
    elif "hong kong" in location:
        return "HKT"
    elif any(x in location.lower() for x in ["remote", "zoom", "online"]):
        return "UTC"
    return "UTC"  # Default to UTC


def get_event_visibility(visibility: str) -> str:
    """Map visibility values for events."""
    visibility = visibility.lower()
    if visibility == "private":
        return "private"
    elif visibility == "public":
        return "public"
    elif visibility == "community":
        return "invite-only"  # Map community to invite-only for events
    return "public"  # Default to public


def transform_event_data(
    data: Dict[str, Any],
    people_data: List[Dict[str, Any]],
    all_comments: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """Transform event JSON data to match Event model schema."""
    # Convert string IDs to UUIDs if needed
    if isinstance(data["id"], str):
        try:
            data["id"] = str(UUID(data["id"]))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {data['id']}")

    # Handle host_id
    host_id = data.get("host_id")
    if not host_id:
        raise ValueError(f"No host_id found for event {data['id']}")

    if isinstance(host_id, str):
        try:
            host_id = str(UUID(host_id))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {host_id}")

    # Get host person data
    host_person = next((p for p in people_data if p["id"] == host_id), None)
    if not host_person:
        raise ValueError(f"Host person with ID {host_id} not found in people data")

    # Create host preview
    host_preview = {
        "id": host_person["id"],
        "name": host_person["name"],
        "last_name": host_person.get("last_name", ""),
        "current_role": host_person.get("current_role", ""),
        "current_company": host_person.get("current_company", ""),
        "profile_picture_url": host_person.get("profile_picture_url", ""),
    }

    # Transform attendees
    attendees = []
    for attendee in data.get("attendees", []):
        if isinstance(attendee["person_id"], str):
            try:
                attendee["person_id"] = str(UUID(attendee["person_id"]))
            except ValueError:
                raise ValueError(f"Invalid UUID string: {attendee['person_id']}")

        # Get attendee person data
        attendee_person = next(
            (p for p in people_data if p["id"] == attendee["person_id"]), None
        )
        if not attendee_person:
            logger.warning(
                f"Attendee person with ID {attendee['person_id']} not found in people data"
            )
            continue

        # Map status to valid values
        status = attendee.get("status", "invited")
        if status == "invited":
            status = "interested"  # Map invited to interested

        attendees.append(
            {
                "person": {
                    "id": attendee["person_id"],  # Use person_id as id
                    "name": attendee_person["name"],
                    "profile_picture_url": attendee_person.get(
                        "profile_picture_url", ""
                    ),
                },
                "status": status,
                "respondedAt": parse_datetime(attendee.get("responded_at", now())),
            }
        )

    # Transform comments
    comments = []
    for comment in all_comments:
        if (
            comment.get("target_id") == data["id"]
            and comment.get("target_type") == "event"
        ):
            if isinstance(comment["id"], str):
                try:
                    comment["id"] = str(UUID(comment["id"]))
                except ValueError:
                    raise ValueError(f"Invalid UUID string: {comment['id']}")

            comments.append(comment["id"])

    # Get location and determine timezone
    location = data.get("location", "")
    timezone = get_timezone_from_location(location)

    # Get visibility
    visibility = get_event_visibility(data.get("visibility", "public"))

    return {
        # Required fields
        "id": data["id"],
        "title": data["title"],
        "description": data["description"],
        "start_time": parse_datetime(data["start_time"]),
        "end_time": parse_datetime(data["end_time"]),
        "host_id": host_id,
        "created_at": parse_datetime(data["created_at"]),
        "updated_at": None,  # Set to None since it's not in the input data
        # New required fields
        "createdBy": host_id,  # Use host_id as createdBy
        "timezone": timezone,
        "visibility": visibility,
        "hosts": [host_preview],  # List of PersonPreview objects
        "tags": data.get("tags", []),
        # Optional fields with defaults
        "location": location,
        "cover_image_url": data.get("cover_image_url", ""),
        "attendees": attendees,
        "comments": comments,
    }


def get_post_visibility(visibility: str) -> str:
    """Map visibility values for posts."""
    visibility = visibility.lower()
    if visibility == "private":
        return "community"  # Map private to community
    elif visibility == "public":
        return "public"
    elif visibility == "community":
        return "community"
    elif visibility == "core":
        return "core"
    elif visibility == "tagged":
        return "tagged"
    elif visibility == "invite-only":
        return "community"  # Map invite-only to community
    return "public"  # Default to public


def transform_post_data(
    data: Dict[str, Any],
    people_data: List[Dict[str, Any]],
    all_comments: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """Transform post JSON data to match Post model schema."""
    # Convert string IDs to UUIDs if needed
    if isinstance(data["id"], str):
        try:
            data["id"] = str(UUID(data["id"]))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {data['id']}")

    if isinstance(data["author_id"], str):
        try:
            data["author_id"] = str(UUID(data["author_id"]))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {data['author_id']}")

    # Get author person data
    author_person = next((p for p in people_data if p["id"] == data["author_id"]), None)
    if not author_person:
        raise ValueError(
            f"Author person with ID {data['author_id']} not found in people data"
        )

    # Create author preview
    author_preview = {
        "id": author_person["id"],
        "name": author_person["name"],
        "last_name": author_person.get("last_name", ""),
        "current_role": author_person.get("current_role", ""),
        "current_company": author_person.get("current_company", ""),
        "profile_picture_url": author_person.get("profile_picture_url", ""),
    }

    # Handle community_id - ensure it's a string
    community_id = data.get("community_id", "")
    if community_id and isinstance(community_id, str):
        try:
            community_id = str(UUID(community_id))
        except ValueError:
            raise ValueError(f"Invalid UUID string for community_id: {community_id}")

    # Transform media
    media = []
    for item in data.get("media", []) or []:  # Handle None case
        if isinstance(item["id"], str):
            try:
                item["id"] = str(UUID(item["id"]))
            except ValueError:
                raise ValueError(f"Invalid UUID string: {item['id']}")

        media.append(
            {
                "id": item["id"],
                "type": item["type"],
                "url": item["url"],
                "thumbnail_url": item.get("thumbnail_url", ""),
            }
        )

    # Transform reactions
    reactions = []
    for reaction in data.get("reactions", []) or []:  # Handle None case
        if isinstance(reaction["person_id"], str):
            try:
                reaction["person_id"] = str(UUID(reaction["person_id"]))
            except ValueError:
                raise ValueError(f"Invalid UUID string: {reaction['person_id']}")

        reactions.append(
            {
                "person_id": reaction["person_id"],
                "type": reaction.get("type", "like"),
                "reacted_at": parse_datetime(reaction.get("reacted_at", now())),
            }
        )

    # Transform comments
    comments = []
    for comment in all_comments:
        if (
            comment.get("target_id") == data["id"]
            and comment.get("target_type") == "post"
        ):
            if isinstance(comment["id"], str):
                try:
                    comment["id"] = str(UUID(comment["id"]))
                except ValueError:
                    raise ValueError(f"Invalid UUID string: {comment['id']}")

            comments.append(comment["id"])

    # Handle datetime fields with defaults
    current_time = now()
    created_at = data.get("created_at", current_time)
    updated_at = data.get("updated_at", current_time)

    # Handle tags - ensure it's a list
    tags = data.get("tags")
    if tags is None:
        tags = []
    elif not isinstance(tags, list):
        tags = [tags]

    return {
        # Required fields
        "id": data["id"],
        "author": author_preview,  # Use author preview instead of just ID
        "content": data["content"],
        "created_at": parse_datetime(created_at),
        "updated_at": parse_datetime(updated_at),
        "communityId": community_id,  # Use the mapped community_id
        "tags": tags,  # Handle None case
        "visibility": get_post_visibility(
            data.get("visibility", "public")
        ),  # Map visibility values
        # Optional fields with defaults
        "media": media,
        "reactions": reactions,
        "comments": comments,
        "is_pinned": data.get("is_pinned", False),
    }


def transform_comment_data(
    data: Dict[str, Any],
    people_data: List[Dict[str, Any]],
    all_comments: List[Dict[str, Any]],
) -> Dict[str, Any]:
    """Transform comment JSON data to match Comment model schema."""
    # Convert string IDs to UUIDs if needed
    if isinstance(data["id"], str):
        try:
            data["id"] = str(UUID(data["id"]))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {data['id']}")

    if isinstance(data["author_id"], str):
        try:
            data["author_id"] = str(UUID(data["author_id"]))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {data['author_id']}")

    # Get author person data
    author_person = next((p for p in people_data if p["id"] == data["author_id"]), None)
    if not author_person:
        raise ValueError(
            f"Author person with ID {data['author_id']} not found in people data"
        )

    # Create author preview
    author_preview = {
        "id": author_person["id"],
        "name": author_person["name"],
        "last_name": author_person.get("last_name", ""),
        "current_role": author_person.get("current_role", ""),
        "current_company": author_person.get("current_company", ""),
        "profile_picture_url": author_person.get("profile_picture_url", ""),
    }

    # Handle parent_post_id
    if isinstance(data.get("parent_post_id"), str):
        try:
            data["parent_id"] = str(UUID(data["parent_post_id"]))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {data['parent_post_id']}")

    # Get canvas_id (target_id)
    canvas_id = data.get("canvas_id")
    if not canvas_id:
        raise ValueError(f"No canvas_id found for comment {data['id']}")

    if isinstance(canvas_id, str):
        try:
            canvas_id = str(UUID(canvas_id))
        except ValueError:
            raise ValueError(f"Invalid UUID string: {canvas_id}")

    # Check if this comment has any child comments (replies)
    has_comments = any(
        comment.get("parent_post_id") == data["id"] for comment in all_comments
    )

    # Transform reactions with proper None handling
    reactions = []
    for reaction in data.get("reactions", []) or []:  # Handle None case
        if not reaction or not reaction.get("person_id"):
            continue  # Skip invalid reactions

        if isinstance(reaction["person_id"], str):
            try:
                reaction["person_id"] = str(UUID(reaction["person_id"]))
            except ValueError:
                raise ValueError(f"Invalid UUID string: {reaction['person_id']}")

        reactions.append(
            {
                "person_id": reaction["person_id"],
                "type": reaction.get("type", "like"),
                "reacted_at": parse_datetime(reaction.get("reacted_at", now())),
            }
        )

    # Handle datetime fields with defaults
    current_time = now()
    created_at = data.get("created_at", current_time)
    updated_at = data.get("updated_at", current_time)

    return {
        # Required fields
        "id": data["id"],
        "target_id": canvas_id,  # Use canvas_id as target_id
        "target_type": "post",  # Default to post since all comments are on posts
        "author": author_preview,  # Add author preview
        "content": data["content"],
        "created_at": parse_datetime(created_at),
        "updated_at": parse_datetime(updated_at),
        # Optional fields with defaults
        "parent_id": data.get("parent_id"),
        "reactions": reactions,
        "has_comments": has_comments,
    }


def create_comment_preview(comment: Dict[str, Any]) -> Dict[str, Any]:
    """Create a CommentPreview object from a full comment."""
    from peepsapi.utils import to_iso_string

    created_at = comment["created_at"]
    if isinstance(created_at, (UTCDateTime,)):
        created_at = to_iso_string(created_at)

    return {
        "id": comment["id"],
        "author_id": comment["author_id"],
        "content": comment["content"],
        "created_at": created_at,
    }


def get_top_comments_for_post(
    post_id: str, comments_data: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """Get the top 10 most recent comments for a post.

    Comments are ordered by the newer of updated_at or created_at.
    Only direct comments to the post are included (parent_id == post_id).
    """
    # Filter comments that reference this post_id with parent_post_id
    post_comments = [c for c in comments_data if c.get("parent_post_id") == post_id]

    # Sort comments by the newer of updated_at or created_at
    def get_latest_timestamp(comment):
        # Parse created_at to datetime, ensuring it's naive (no timezone)
        created = parse_datetime(comment["created_at"], for_comparison=True)

        # Parse updated_at to datetime if it exists, ensuring it's naive
        updated = None
        if comment.get("updated_at"):
            updated = parse_datetime(comment["updated_at"], for_comparison=True)

        # Return the newer of the two timestamps
        return updated if updated and updated > created else created

    sorted_comments = sorted(post_comments, key=get_latest_timestamp, reverse=True)

    # Take the top 10
    top_comments = sorted_comments[:10]

    # Convert to CommentPreview objects
    comment_previews = []
    for comment in top_comments:
        comment_previews.append(create_comment_preview(comment))

    return comment_previews


def load_and_validate_comment_data(
    data_dir: str, people_data: List[Dict[str, Any]]
) -> bool:
    """Load and validate comment data. Returns True if validation successful."""
    try:
        comment_file = os.path.join(data_dir, DATA_FILES["comment"]["input"])
        if not os.path.exists(comment_file):
            logger.warning(f"Comment data file not found at {comment_file}")
            return False

        logger.info(f"Loading comment data from {comment_file}")
        comment_data = load_json_file(comment_file)
        logger.info(f"Found {len(comment_data)} comment records")

        transformed_data = []
        validation_success = True

        for item in comment_data:
            try:
                # print(comment_data)
                transformed = transform_comment_data(item, people_data, comment_data)
                # Validate against schema
                Comment(**transformed)
                transformed_data.append(transformed)
            except Exception as e:
                logger.error(f"Error transforming comment {item.get('id')}: {str(e)}")
                validation_success = False

        if validation_success:
            logger.info(
                f"Successfully transformed {len(transformed_data)} comment records"
            )
            save_transformed_data(
                transformed_data, DATA_FILES["comment"]["output"], data_dir
            )
            return True
        return False

    except Exception as e:
        logger.error(f"Error in comment data validation: {str(e)}")
        return False


def validate_data(
    data: List[Dict[str, Any]],
    model: Type[BaseModel],
    transform_func: callable,
    extra_args: Dict[str, Any] = None,
) -> Tuple[List[BaseModel], List[Dict[str, Any]]]:
    """Validate data against a Pydantic model.

    Returns (valid_items, validation_errors)
    """
    valid_items = []
    validation_errors = []

    for item in data:
        try:
            if extra_args:
                transformed = transform_func(item, **extra_args)
            else:
                transformed = transform_func(item)
            valid_item = model(**transformed)
            valid_items.append(valid_item)
        except ValidationError as e:
            errors = []
            for error in e.errors():
                field_path = " -> ".join(str(loc) for loc in error["loc"])
                errors.append(
                    {
                        "field": field_path,
                        "error": error["msg"],
                        "value": error.get("ctx", {}).get("error", "N/A"),
                    }
                )

            validation_errors.append(
                {"item_id": item.get("id", "unknown"), "errors": errors}
            )
        except Exception as e:
            validation_errors.append(
                {
                    "item_id": item.get("id", "unknown"),
                    "errors": [{"field": "unknown", "error": str(e), "value": "N/A"}],
                }
            )

    return valid_items, validation_errors


def compare_records(
    cosmos_record: Dict[str, Any], json_record: Dict[str, Any]
) -> List[str]:
    """Compare a Cosmos DB record with a JSON record and return list of differences."""
    differences = []
    for key in json_record:
        if key in cosmos_record:
            # Handle types mismatch
            if type(json_record[key]) is not type(cosmos_record[key]):
                differences.append(
                    f"{key}: Type mismatch - Cosmos={type(cosmos_record[key])}, JSON={type(json_record[key])}"
                )
                continue

            # Handle datetime comparison
            if isinstance(json_record[key], (UTCDateTime,)):
                cosmos_time = parse_datetime(cosmos_record[key])
                if abs((json_record[key] - cosmos_time).total_seconds()) > 1:
                    differences.append(
                        f"{key}: Cosmos={cosmos_record[key]}, JSON={json_record[key]}"
                    )
            # Handle nested objects
            elif isinstance(json_record[key], dict):
                nested_diffs = compare_records(cosmos_record[key], json_record[key])
                differences.extend([f"{key}.{diff}" for diff in nested_diffs])
            # Handle lists
            elif isinstance(json_record[key], list):
                if len(json_record[key]) != len(cosmos_record[key]):
                    differences.append(
                        f"{key}: Different lengths - Cosmos={len(cosmos_record[key])}, JSON={len(json_record[key])}"
                    )
            # Simple value comparison
            elif json_record[key] != cosmos_record[key]:
                differences.append(
                    f"{key}: Cosmos={cosmos_record[key]}, JSON={json_record[key]}"
                )
        else:
            differences.append(f"{key}: Cosmos=<no record>, JSON={json_record[key]}")
    return differences


def get_id(record, container_name):
    """
    Get a unique identifier for a record based on the container name.

    Args:
        record: The record for which to generate the ID.
        container_name: The name of the container the record belongs to.

    Returns:
        A unique identifier for the record.
    """
    if container_name == "connections":
        # Try both camelCase and snake_case versions of requestee_person_id
        owner_id = record.get("requester_person_id")
        if not owner_id:
            raise ValueError(
                f"Connection record {record['id']} has no requestee_person_id or requester_person_id"
            )
        return record["id"] + "--" + owner_id

    return record["id"]


def validate_against_cosmos(
    container_name: str, transformed_data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Compare transformed data against existing Cosmos DB records.

    Returns a dict with new records and discrepancies.
    """
    # DatabaseConnection is already imported at the top

    # Initialize DB connection if needed
    if not DatabaseConnection._instance:
        DatabaseConnection.initialize()

    db = DatabaseConnection.get_instance()
    container = db.get_container(container_name)

    # Query all existing records
    existing_records = list(container.read_all_items())
    existing_by_id = {
        get_id(record, container_name): record for record in existing_records
    }

    new_records = []
    discrepancies = {}

    for record in transformed_data:
        record_id = get_id(record, container_name)
        if record_id not in existing_by_id:
            new_records.append(record)
        else:
            # Compare fields
            differences = compare_records(existing_by_id[record_id], record)
            if differences:
                discrepancies[record_id] = differences

    return {
        "new_records": new_records,
        "discrepancies": discrepancies,
        "total_existing": len(existing_records),
        "total_new": len(new_records),
        "total_discrepancies": len(discrepancies),
    }


def print_validation_summary(
    model_name: str, validation_result: Dict[str, Any]
) -> None:
    """Print a human-readable summary of validation results."""
    print(f"\n=== {model_name} Data Validation Summary ===")
    print(f"Total existing records: {validation_result['total_existing']}")
    print(f"New records to be added: {validation_result['total_new']}")
    print(f"Records with discrepancies: {validation_result['total_discrepancies']}")

    if validation_result["discrepancies"]:
        print("\nDetailed Discrepancies:")
        print("----------------------")
        for record_id, differences in validation_result["discrepancies"].items():
            print(f"\nRecord ID: {record_id}")
            for diff in differences:
                print(f"- {diff}")
    else:
        print("\nNo discrepancies found!")


async def load_container_data(filename: str, container_name: str) -> None:
    """Load transformed data into a specific container with validation."""
    try:
        data_path = os.path.join(os.path.dirname(__file__), "data", f"{filename}")
        if not os.path.exists(data_path):
            logger.warning(f"No transformed data file found at {data_path}")
            return

        with open(data_path, "r") as f:
            data = json.load(f)

        # Compare with existing data
        validation_result = validate_against_cosmos(container_name, data)

        db = DatabaseConnection.get_instance()
        container = db.get_container(container_name)

        # Handle records with discrepancies (update existing records)
        if validation_result["discrepancies"]:
            logger.warning(f"\nDiscrepancies found in {container_name}:")
            records_to_update = []

            # Find the corresponding records in the data to update
            for record_id, differences in validation_result["discrepancies"].items():
                logger.warning(f"\nRecord {record_id} differences:")
                for diff in differences:
                    logger.warning(f"  - {diff}")

                # Find the record in the data
                record_to_update = next(
                    (
                        item
                        for item in data
                        if get_id(item, container_name) == record_id
                    ),
                    None,
                )
                if record_to_update:
                    records_to_update.append(record_to_update)

            # Update the records in the database
            if records_to_update:
                logger.info(
                    f"{container_name}: Updating {len(records_to_update)} existing records"
                )
                for item in records_to_update:
                    try:
                        container.upsert_item(body=item)
                        logger.info(
                            f"{container_name}: Updated record {get_id(item, container_name)}"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error updating record {get_id(item, container_name)}: {str(e)}"
                        )

        # Handle new records
        if validation_result["new_records"]:
            logger.info(
                f"{container_name}: Loading {len(validation_result['new_records'])} new records"
            )
            for item in validation_result["new_records"]:
                try:
                    pass
                    container.create_item(body=item)
                except Exception as e:
                    if "Conflict" in str(e):
                        logger.info(
                            f"{container_name}: Record {get_id(item, container_name)} already exists, skipping"
                        )
                    else:
                        raise

        existing_count = validation_result["total_existing"]
        new_count = validation_result["total_new"]
        logger.info(
            f"{container_name}: Processed {existing_count} existing, {new_count} new records"
        )

    except Exception as e:
        if "Conflict" not in str(e):
            logger.error(f"Error loading data into {container_name}: {str(e)}")
            raise


async def load_model_data_to_cosmos(model_name: str) -> None:
    """Load transformed data for a specific model into Cosmos DB.

    Args:
        model_name: Name of the model to load (e.g. 'person', 'community')
    """
    try:
        # Initialize database connection if needed
        DatabaseConnection.initialize()

        if model_name not in DATA_FILES:
            raise ValueError(f"Unknown model name: {model_name}")

        container_name = DATA_FILES[model_name]["container"]
        filename = DATA_FILES[model_name]["output"]
        await load_container_data(filename, container_name)

    except Exception as e:
        logger.error(f"Error loading {model_name} data: {str(e)}")
        raise


def generate_connections():
    """
    Generate connections between people based on predefined rules.

    This function reads transformed person data, generates connections
    between people with different statuses (accepted, requested), and
    saves the connections to a JSON file. Each connection is represented
    as a bidirectional relationship with a unique ID.
    """
    # Read the transformed_person_20250408_204601.json file
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    transformed_file = os.path.join(data_dir, DATA_FILES["person"]["output"])

    if not os.path.exists(transformed_file):
        logger.error(f"File not found: {transformed_file}")
        return

    with open(transformed_file, "r") as f:
        all_records = json.load(f)

    generated_connection_ids = {}
    connections = []

    def get_person_preview(person: Dict[str, Any]) -> Dict[str, Any]:
        """Get a ConnectionPersonPreview for a person."""
        # Convert string ID to UUID if needed
        if isinstance(person["id"], str):
            try:
                person["id"] = str(UUID(person["id"]))
            except ValueError:
                raise ValueError(f"Invalid UUID string: {person['id']}")

        return {
            "id": person["id"],
            "name": person["name"],
            "last_name": person.get("last_name", ""),
            "current_role": person.get("current_role", ""),
            "current_company": person.get("current_company", ""),
            "location": person.get("location", ""),
            "profile_picture_url": person.get("profile_picture_url", ""),
        }

    def generate_connection(
        id: str, requester: Dict[str, Any], requestee: Dict[str, Any], status: str
    ) -> List[Dict[str, Any]]:
        """Generate a bidirectional connection between two people."""
        # Convert string IDs to UUIDs if needed
        if isinstance(requester["id"], str):
            try:
                requester["id"] = str(UUID(requester["id"]))
            except ValueError:
                raise ValueError(f"Invalid UUID string: {requester['id']}")

        if isinstance(requestee["id"], str):
            try:
                requestee["id"] = str(UUID(requestee["id"]))
            except ValueError:
                raise ValueError(f"Invalid UUID string: {requestee['id']}")

        new_records = []

        # First direction (requester -> requestee)
        connection = {
            "id": str(
                uuid.uuid5(
                    CONNECTION_NAMESPACE,
                    "-".join(sorted([str(requester["id"]), str(requestee["id"])])),
                )
            ),
            "requester_person_id": requester["id"],
            "requestee_person_id": requestee["id"],
            "status": status,
            "person_preview": get_person_preview(requestee),
            "owner_person_id": requester["id"],
            "created_at": parse_datetime(now()),
            "updated_at": parse_datetime(now()),
        }
        new_records.append(connection)

        # Second direction (requestee -> requester)
        connection = {
            "id": str(
                uuid.uuid5(
                    CONNECTION_NAMESPACE,
                    "-".join(sorted([str(requester["id"]), str(requestee["id"])])),
                )
            ),
            "requester_person_id": requester["id"],
            "requestee_person_id": requestee["id"],
            "status": status,
            "person_preview": get_person_preview(requester),
            "owner_person_id": requestee["id"],
            "created_at": parse_datetime(now()),
            "updated_at": parse_datetime(now()),
        }
        new_records.append(connection)

        return new_records

    for record in all_records:
        # Add "accepted" connections
        counter = 0
        for counter_record in all_records:
            if counter == 5:
                break

            if record["id"] == counter_record["id"]:
                continue

            id = "-".join(sorted([record["id"], counter_record["id"]]))

            if id in generated_connection_ids:
                if generated_connection_ids[id] == "accepted":
                    counter += 1
                continue

            counter += 1
            generated_connection_ids[id] = "accepted"
            connections.extend(
                generate_connection(id, record, counter_record, "accepted")
            )

        # Add "requested" connections
        counter = 0
        for counter_record in all_records:
            if counter == 5:
                break

            if record["id"] == counter_record["id"]:
                continue

            id = "-".join(sorted([record["id"], counter_record["id"]]))

            if id in generated_connection_ids:
                if generated_connection_ids[id] == "requested":
                    counter += 1
                continue

            counter += 1
            generated_connection_ids[id] = "requested"
            connections.extend(
                generate_connection(id, record, counter_record, "requested")
            )

    # Save connections to transformed_connections.json
    transformed_connections_file = os.path.join(
        data_dir, "transformed_connections.json"
    )
    with open(transformed_connections_file, "w") as f:
        json.dump(connections, f, indent=2, default=str)
        f.write("\n")  # Add newline at the end of the file

    logger.info(f"Saved connections to {transformed_connections_file}")


async def validate_and_load_data():
    """Orchestrate the validation and loading of all data models in dependency order.

    Returns True if all operations were successful.
    """
    # Get data directory
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    logger.info("Starting data validation...")

    # First validate person data
    if not load_and_validate_person_data(data_dir):
        logger.error("Person data validation failed")
        return False
    logger.info("Person data validation successful")

    person_data = load_json_file(os.path.join(data_dir, DATA_FILES["person"]["input"]))

    # Generate transformed_connections.json
    generate_connections()

    # Validate community data
    if not load_and_validate_community_data(data_dir, person_data):
        logger.error("Community data validation failed")
        return False
    logger.info("Community data validation successful")

    # Validate event data
    if not load_and_validate_event_data(data_dir, person_data):
        logger.error("Event data validation failed")
        return False
    logger.info("Event data validation successful")

    # Load transformed event data
    event_file = os.path.join(data_dir, DATA_FILES["event"]["output"])
    transformed_events = []
    if os.path.exists(event_file):
        with open(event_file, "r") as f:
            transformed_events = json.load(f)

    # Update person data with event details
    person_file = os.path.join(data_dir, DATA_FILES["person"]["output"])
    if os.path.exists(person_file):
        with open(person_file, "r") as f:
            transformed_people = json.load(f)
        updated_people = update_person_with_event_details(
            transformed_people, transformed_events
        )
        save_transformed_data(updated_people, DATA_FILES["person"]["output"], data_dir)

    # Continue with other validations
    if not load_and_validate_post_data(data_dir, person_data):
        logger.error("Post data validation failed")
        return False
    logger.info("Post data validation successful")

    if not load_and_validate_comment_data(data_dir, person_data):
        logger.error("Comment data validation failed")
        return False
    logger.info("Comment data validation successful")

    # If all validations passed, load into Cosmos DB
    logger.info("All validations successful. Loading data into Cosmos DB...")

    # Initialize database connection
    DatabaseConnection.initialize()

    # Load data in dependency order
    await load_model_data_to_cosmos("person")
    await load_model_data_to_cosmos("community")
    await load_model_data_to_cosmos("event")
    await load_model_data_to_cosmos("post")
    await load_model_data_to_cosmos("comment")
    await load_model_data_to_cosmos("connection")

    return True


def main():
    """Execute the data loading process.

    This function orchestrates the entire data loading process, including:
    1. Loading and validating data from JSON files
    2. Transforming data to match the application's data models
    3. Loading the transformed data into the database

    Returns:
        None
    """
    # Get the directory containing the data files
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    logger.info(f"\nValidating data files in: {data_dir}\n")

    # Initialize configuraton for DB connection
    load_dotenv()
    config.init()

    # Initialize DB connection
    DatabaseConnection.initialize()

    # Load person data first (needed for other validations)
    # This is used in the validation functions, not directly here
    _ = load_json_file(os.path.join(data_dir, DATA_FILES["person"]["input"]))

    # Validate each model's data against Cosmos DB
    for model_name, data_info in DATA_FILES.items():
        try:
            # Load transformed data
            transformed_file = os.path.join(data_dir, data_info["output"])
            if os.path.exists(transformed_file):
                with open(transformed_file, "r") as f:
                    transformed_data = json.load(f)

                # Get validation results
                validation_result = validate_against_cosmos(
                    data_info["container"], transformed_data
                )
                print_validation_summary(model_name.capitalize(), validation_result)
            else:
                print(f"\n=== {model_name.capitalize()} Data ===")
                print(f"No transformed data file found at {transformed_file}")
        except Exception as e:
            print(f"\nError validating {model_name} data: {str(e)}")


if __name__ == "__main__":
    main()
