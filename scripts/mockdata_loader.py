"""Data loader script for transforming JSON data into model format.

This script reads JSON files from the data directory and transforms them into
the corresponding model format, saving the results to new files.
"""

import argparse
import json
import logging
import os
import re
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from dotenv import load_dotenv

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

from peepsapi.config import get as config_get
from peepsapi.crud.models.base import EventPreview, PersonPreview
from peepsapi.crud.models.community import Community, CommunitySettings, Member
from peepsapi.crud.models.connection import Connection
from peepsapi.crud.models.event import Attendee, Event
from peepsapi.crud.models.person import (
    <PERSON>,
    Email,
    Person,
    Phone<PERSON><PERSON>ber,
    SocialLink,
)
from peepsapi.crud.services.connection_service import gen_connection_id
from peepsapi.crud.services.picture_service import (
    PictureService,
    build_picture_url_by_id,
)
from peepsapi.models import UTCDateTime, now
from peepsapi.services.cosmos_containers import (
    get_communities_container,
    get_connections_container,
    get_events_container,
    get_people_container,
)
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.error_handling import ValidationError
from peepsapi.utils.logging import get_logger

logger = get_logger(__name__)
# Set up logging to stdout
console_handler = logging.StreamHandler()
formatter = logging.Formatter("%(asctime)s - [%(name)s] - %(levelname)s - %(message)s")
console_handler.setFormatter(formatter)

# Set the root logger to DEBUG level and add the handler
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.addHandler(console_handler)

# Set Azure-related loggers to WARNING to reduce noise
logging.getLogger("azure").setLevel(logging.ERROR)
logging.getLogger("azure.identity").setLevel(logging.ERROR)
logging.getLogger("azure.keyvault").setLevel(logging.ERROR)
logging.getLogger("azure.cosmos").setLevel(logging.ERROR)
logging.getLogger("azure.storage").setLevel(logging.ERROR)
logging.getLogger("urllib3.connectionpool").setLevel(logging.ERROR)
logging.getLogger("azure.cosmos.cosmos_client").setLevel(logging.ERROR)
logging.getLogger("azure.cosmos.http_constants").setLevel(logging.ERROR)

picture_service = PictureService()

# Environment configurations
ENVIRONMENTS = ["test", "dev", "stage"]
ENVIRONMENT_CONFIGS = {
    "test": {
        "AZURE_KEY_VAULT_URL": "https://peepsapp-vault-test.vault.azure.net/",
    },
    "dev": {
        "AZURE_KEY_VAULT_URL": "https://peepsapp-vault-dev.vault.azure.net/",
    },
    "stage": {
        "AZURE_KEY_VAULT_URL": "https://peepsapp-vault-stage.vault.azure.net/",
    },
}

# Key Vault secret names
SECRET_NAMES = {
    "COSMOS_ENDPOINT": "cosmos-endpoint",
    "COSMOS_KEY": "cosmos-key",
    "COSMOS_DB": "cosmos-db",
    "AZURE_BLOB_CONNECTION_STRING": "azure-blob-connection-string",
}


def get_key_vault_client(key_vault_url: str) -> SecretClient:
    """Create and return a Key Vault client.

    Args:
        key_vault_url: URL of the Key Vault

    Returns:
        SecretClient instance
    """
    credential = DefaultAzureCredential()
    return SecretClient(vault_url=key_vault_url, credential=credential)


def read_secrets_from_key_vault(key_vault_url: str) -> Dict[str, str]:
    """Read secrets from Key Vault.

    Args:
        key_vault_url: URL of the Key Vault

    Returns:
        Dictionary containing secret values
    """
    client = get_key_vault_client(key_vault_url)
    secrets = {}

    for env_var, secret_name in SECRET_NAMES.items():
        try:
            secret = client.get_secret(secret_name)
            secrets[env_var] = secret.value
        except Exception as e:
            logger.error(f"Warning: Could not read secret {secret_name}: {str(e)}")
            secrets[env_var] = None

    return secrets


def get_environment() -> str:
    """Prompt user to select environment.

    Returns:
        Selected environment name
    """
    while True:
        try:
            choice = int(input("\nSelect environment:\n[1] test\n[2] dev\n[3] stage\n"))
            if 1 <= choice <= len(ENVIRONMENTS):
                return ENVIRONMENTS[choice - 1]
            print("Invalid choice. Please select a number between 1 and 3")
        except ValueError:
            print("Please enter a valid number")


def load_environment_config(environment: str) -> Dict[str, str]:
    """Load configuration for the selected environment.

    Args:
        environment: Selected environment name

    Returns:
        Dictionary containing environment configuration
    """
    if environment not in ENVIRONMENT_CONFIGS:
        raise ValueError(f"Invalid environment: {environment}")

    config = ENVIRONMENT_CONFIGS[environment]

    # Load environment variables from .env file if it exists
    load_dotenv()

    # Read secrets from Key Vault
    key_vault_url = config["AZURE_KEY_VAULT_URL"]
    logger.info(f"Reading secrets from Key Vault: {key_vault_url}")
    secrets = read_secrets_from_key_vault(key_vault_url)

    # Set environment variables from secrets
    for key, value in secrets.items():
        if value:  # Only set if we got a value from Key Vault
            os.environ[key] = value
            logger.info(f"Set environment variable: {key}")

    # Get all configuration values using config.get()
    for key in config:
        config[key] = config_get(key)

    return config


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Load data into PeepsAPI database")
    parser.add_argument(
        "--env",
        type=str,
        choices=ENVIRONMENTS,
        help="Environment to load data into (if not provided, will prompt for selection)",
    )
    return parser.parse_args()


def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """Load and parse a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        List of dictionaries from the JSON file
    """
    with open(file_path, "r") as f:
        return json.load(f)


def save_json_file(data: List[Dict[str, Any]], file_path: str) -> None:
    """Save data to a JSON file.

    Args:
        data: Data to save
        file_path: Path where to save the file
    """
    with open(file_path, "w") as f:
        json.dump(data, f, indent=2, default=str)
        f.write("\n")  # Add empty line at the end


def parse_datetime(dt_str: str) -> UTCDateTime:
    """Parse a datetime string into UTCDateTime.

    Args:
        dt_str: ISO format datetime string

    Returns:
        UTCDateTime object
    """
    return UTCDateTime.fromisoformat(dt_str)


def transform_social_links(
    social_ids: List[Dict[str, str]], member_since: UTCDateTime
) -> List[SocialLink]:
    """Transform social IDs into SocialLink objects.

    Args:
        social_ids: List of social platform IDs

    Returns:
        List of SocialLink objects
    """
    social_links = []
    for social in social_ids:
        social_links.append(
            SocialLink(
                platform=social["platform"],
                id=social["id"],
                url=f"https://{social['platform']}.com/{social['id']}",  # Construct URL
                username=social["id"],
                verified=False,
                connected_at=member_since,
            )
        )
    return social_links


def transform_emails(
    emails: List[Dict[str, Any]], member_since: UTCDateTime
) -> List[Email]:
    """Transform email data into Email objects.

    Args:
        emails: List of email data

    Returns:
        List of Email objects
    """
    return [
        Email(
            type=email["type"],
            address=email["address"],
            active_since=parse_datetime(email["active_since"])
            if email.get("active_since")
            else member_since,
            verified=False,
        )
        for email in emails
    ]


def transform_phone_number(
    mobile_number: str, member_since: UTCDateTime
) -> List[PhoneNumber]:
    """Transform mobile number into PhoneNumber object.

    Args:
        mobile_number: Mobile number string
        member_since: Member since timestamp

    Returns:
        List containing a single PhoneNumber object
    """
    return [
        PhoneNumber(
            type="mobile",
            number=mobile_number,
            active_since=member_since,
            verified=False,
        )
    ]


def transform_achievements(achievements: List[Dict[str, str]]) -> List[Achievement]:
    """Transform achievements data into Achievement objects.

    Args:
        achievements: List of achievement data

    Returns:
        List of Achievement objects
    """
    return [
        Achievement(
            name=achievement["name"],
            color=achievement["color"],
            comment=achievement["comment"],
        )
        for achievement in achievements
    ]


def transform_upcoming_events(events: List[Dict[str, Any]]) -> List[EventPreview]:
    """Transform upcoming RSVP events into EventPreview objects.

    Args:
        events: List of event RSVP data

    Returns:
        List of EventPreview objects
    """
    return [
        EventPreview(
            id=UUID(event["event_id"]),
            title="",  # These would need to be fetched from events data
            start_time=parse_datetime(event["rsvp_time"]),
            end_time=None,  # Not available in RSVP data
            location="",  # Not available in RSVP data
            community_id=None,  # Not available in RSVP data
        )
        for event in events
    ]


def transform_person(person_data: Dict[str, Any]) -> Person:
    """Transform a person's data from JSON format to Person model.

    Args:
        person_data: Person data from JSON

    Returns:
        Person object with transformed data
    """
    # Convert string IDs to UUID
    person_id = UUID(person_data["id"])
    invited_by_id = (
        UUID(person_data["invited_by_id"]) if person_data.get("invited_by_id") else None
    )
    member_since = parse_datetime(person_data["member_since"])

    # Create Person object with transformed data
    return Person(
        id=person_id,
        name=person_data["name"],
        last_name=person_data["last_name"],
        current_role=person_data["role"],
        current_company=person_data["current_company"],
        location=person_data["location"],
        invited_by_id=invited_by_id,
        member_since=member_since,
        bio=person_data.get("bio"),
        # Transform complex objects
        social_links=transform_social_links(
            person_data.get("social_ids", []), member_since
        ),
        emails=transform_emails(person_data.get("emails", []), member_since),
        phone_numbers=transform_phone_number(
            person_data.get("mobile_number", ""), member_since
        ),
        upcoming_events=transform_upcoming_events(
            person_data.get("upcoming_rsvp_events", [])
        ),
        achievements=transform_achievements(person_data.get("achievements", [])),
        # Default values for fields not in JSON
        profile_pic=build_picture_url_by_id(parent_class=Person, parent_id=person_id),
        profile_completed=False,
        has_passkey=False,
        primary_identifier_type="",
        primary_identifier_value="",
        connection_status=None,
        connection_requestee=None,
        notes=None,
        reminders=None,
    )


def transform_member(member_data: Dict[str, Any]) -> Member:
    """Transform member data into Member object.

    Args:
        member_data: Member data from JSON

    Returns:
        Member object with transformed data
    """
    return Member(
        person_id=UUID(member_data["person_id"]),
        role=member_data["role"],
        joined_at=member_data["joined_at"],
        left_at=None,  # Not available in JSON data
    )


def get_default_community_settings() -> CommunitySettings:
    """Get default community settings.

    Returns:
        CommunitySettings object with default values
    """
    return CommunitySettings(
        allow_member_invites=True,
        allow_member_posts=True,
        allow_member_events=True,
        allow_member_comments=True,
        allow_member_likes=True,
        allow_member_media=True,
        allow_member_tags=True,
        allow_member_visibility=True,
    )


def transform_community(
    community_data: Dict[str, Any], people_data: List[Dict[str, Any]]
) -> Community:
    """Transform a community's data from JSON format to Community model.

    Args:
        community_data: Community data from JSON
        people_data: List of people data to find creator info

    Returns:
        Community object with transformed data
    """
    # Find creator info from people data
    creator = next(
        (p for p in people_data if p["id"] == community_data["creator_id"]), None
    )
    if not creator:
        raise ValueError(
            f"Creator with ID {community_data['creator_id']} not found in people data"
        )

    # Create PersonPreview for creator
    created_by = PersonPreview(
        id=UUID(creator["id"]),
        name=creator["name"],
        last_name=creator.get("last_name", ""),
        current_role=creator.get("role", ""),
        current_company=creator.get("current_company", ""),
        profile_pic_thumb=build_picture_url_by_id(
            parent_class=Person, parent_id=creator["id"]
        ),
        location=creator.get("location", ""),
    )

    # Transform members
    members = [transform_member(member) for member in community_data.get("members", [])]

    # Create Community object
    return Community(
        id=community_data["id"],
        name=community_data["name"],
        settings=get_default_community_settings(),
        created_by=created_by,
        created_at=parse_datetime(community_data["created_at"]),
        visibility=community_data["visibility"],
        description=community_data.get("description"),
        tags=community_data.get("tags", []),
        members=members,
        updated_at=None,  # Not available in JSON data
        profile_picture_url=None,  # Not available in JSON data
        cover_picture_url=None,  # Not available in JSON data
        location=None,  # Not available in JSON data
        website=None,  # Not available in JSON data
        social_links=[],  # Not available in JSON data
        upcoming_events=[],  # Not available in JSON data
    )


def transform_attendee(
    attendee_data: Dict[str, Any], people_data: List[Dict[str, Any]]
) -> Attendee:
    """Transform attendee data into Attendee object.

    Args:
        attendee_data: Attendee data from JSON
        people_data: List of people data to find person info

    Returns:
        Attendee object with transformed data
    """
    # Find person info from people data
    person = next(
        (p for p in people_data if p["id"] == attendee_data["person_id"]), None
    )
    if not person:
        raise ValueError(
            f"Person with ID {attendee_data['person_id']} not found in people data"
        )

    # Create PersonPreview for attendee
    person_preview = PersonPreview(
        id=UUID(person["id"]),
        name=person["name"],
        last_name=person.get("last_name", ""),
        current_role=person.get("role", ""),
        current_company=person.get("current_company", ""),
        profile_pic_thumb=build_picture_url_by_id(
            parent_class=Person, parent_id=person["id"]
        ),
        location=person.get("location", ""),
    )

    return Attendee(
        person=person_preview,
        status=attendee_data["status"],
        responded_at=parse_datetime(attendee_data["rsvp_time"]),
    )


def get_timezone_from_location(location: str) -> str:
    """Get timezone based on location.

    Args:
        location: Location string

    Returns:
        Timezone string
    """
    # Simple mapping for common locations
    timezone_map = {
        "Berlin": "Europe/Berlin",
        "London": "Europe/London",
        "Seattle": "America/Los_Angeles",
        "Remote": "UTC",
    }
    return timezone_map.get(location, "UTC")


def transform_event(
    event_data: Dict[str, Any], people_data: List[Dict[str, Any]]
) -> Event:
    """Transform event data from JSON format to Event model.

    Args:
        event_data: Event data from JSON
        people_data: List of people data to find person info

    Returns:
        Event object with transformed data
    """
    # Find host info from people data
    host = next((p for p in people_data if p["id"] == event_data["host_id"]), None)
    if not host:
        raise ValueError(
            f"Host with ID {event_data['host_id']} not found in people data"
        )

    # Create PersonPreview for host
    host_preview = PersonPreview(
        id=UUID(host["id"]),
        name=host["name"],
        last_name=host.get("last_name", ""),
        current_role=host.get("role", ""),
        current_company=host.get("current_company", ""),
        profile_pic_thumb=build_picture_url_by_id(
            parent_class=Person, parent_id=host["id"]
        ),
        location=host.get("location", ""),
    )

    # Transform attendees
    attendees = [
        transform_attendee(attendee, people_data)
        for attendee in event_data.get("attendees", [])
    ]

    # Create Event object
    return Event(
        id=event_data["id"],
        title=event_data["title"],
        location=event_data["location"],
        created_by=UUID(event_data["host_id"]),  # Using host_id as created_by
        start_time=parse_datetime(event_data["start_time"]),
        end_time=parse_datetime(event_data["end_time"]),
        timezone=get_timezone_from_location(event_data["location"]),
        visibility=event_data["visibility"],
        created_at=parse_datetime(event_data["created_at"]),
        description=event_data.get("description"),
        community_id=event_data.get("community_id"),
        tags=event_data.get("tags", []),
        attendees=attendees,
        hosts=[host_preview],
        comments=None,  # Not available in JSON data
        cover_image_url=None,  # Not available in JSON data
    )


def upload_profile_picture_from_file(person_id: UUID, image_filename: str) -> str:
    """Upload a profile picture from a local file.

    Args:
        person_id: UUID of the person
        image_filename: Name of the image file in profilePics directory

    Returns:
        Picture object with metadata
    """
    # 1. Construct file path
    profile_pics_dir = Path("scripts/data/profilePics")
    # Remove any leading slashes or profilePics/ prefix from the filename
    clean_filename = image_filename.lstrip("/").replace("profilePics/", "")
    image_path = profile_pics_dir / clean_filename

    logger.debug(f"🔍 Looking for profile picture at: {image_path}")

    # 2. Read image file
    try:
        with open(image_path, "rb") as f:
            image_bytes = f.read()
    except FileNotFoundError:
        logger.error(
            f"❌ Profile picture file not found: {image_filename}",
            extra={
                "person_id": str(person_id),
                "image_path": str(image_path),
                "clean_filename": clean_filename,
            },
        )
        raise ValidationError(
            message=f"Profile picture file not found: {image_filename}",
            error_code="ImageFileNotFound",
        )

    # 3. Determine content type from file extension
    content_type = {
        ".jpg": "image/jpeg",
        ".jpeg": "image/jpeg",
        ".png": "image/png",
        ".gif": "image/gif",
    }.get(image_path.suffix.lower())

    if not content_type:
        logger.error(
            f"❌ Unsupported image format: {image_path.suffix}",
            extra={"person_id": str(person_id), "image_path": str(image_path)},
        )
        raise ValidationError(
            message=f"Unsupported image format: {image_path.suffix}",
            error_code="InvalidImageFormat",
        )

    # 4. Upload using existing PictureService
    try:
        picture_service.upload_picture(
            image_file=image_bytes,
            parent_class=Person,
            parent_id=person_id,
            content_type=content_type,
            original_filename=clean_filename,
        )
    except Exception as e:
        logger.error(
            f"❌ Failed to upload profile picture for {person_id}: {str(e)}",
            extra={
                "person_id": str(person_id),
                "image_path": str(image_path),
                "content_type": content_type,
                "error_type": type(e).__name__,
                "error_details": str(e),
            },
        )
        raise

    return build_picture_url_by_id(parent_class=Person, parent_id=person_id)


def get_existing_person(
    person_id: UUID, container: CosmosContainer
) -> Optional[Tuple[Person, int]]:
    """Get existing person from the database if they exist.

    Args:
        person_id: UUID of the person
        container: Cosmos DB container

    Returns:
        Person object if found, None otherwise
    """
    try:
        return container.read_model(
            id=person_id, partition_key=person_id, model_class=Person
        )
    except CosmosResourceNotFoundError:
        return None


def normalize_datetime(dt):
    """Normalize datetime to UTC timestamp for comparison."""
    if isinstance(dt, (datetime, UTCDateTime)):
        # Convert to UTC if not already
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        else:
            dt = dt.astimezone(timezone.utc)
        return dt.timestamp()
    elif isinstance(dt, str):
        # Handle string datetime formats
        try:
            # Try parsing with Z timezone
            if dt.endswith("Z"):
                dt = datetime.fromisoformat(dt.replace("Z", "+00:00")).timestamp()
            # Try parsing with +00:00 timezone
            elif dt.endswith("+00:00"):
                dt = datetime.fromisoformat(dt).timestamp()
            # Try parsing without timezone
            else:
                return (
                    datetime.fromisoformat(dt).replace(tzinfo=timezone.utc).timestamp()
                )
        except ValueError:
            return dt
    return dt


def compare_datetime_values(val1, val2, field_name: str = None) -> bool:
    """Compare two datetime values, handling different formats and types.

    Args:
        val1: First datetime value (can be datetime, UTCDateTime, or string)
        val2: Second datetime value (can be datetime, UTCDateTime, or string)
        field_name: Optional field name for debug logging

    Returns:
        bool: True if values represent the same time, False otherwise
    """
    # Handle None values
    if val1 is None and val2 is None:
        return True
    if val1 is None or val2 is None:
        if field_name:
            logger.debug(f"{field_name} difference: {val1} != {val2}")
        return False

    # Normalize both values to timestamps
    ts1 = normalize_datetime(val1)
    ts2 = normalize_datetime(val2)

    # Compare timestamps
    if ts1 != ts2:
        if field_name:
            logger.debug(f"{field_name} difference:")
            logger.debug(f"  Original values: {val1} != {val2}")
            logger.debug(f"  Timestamps: {ts1} != {ts2}")
        return False
    return True


def compare_phone_numbers(phones1, phones2):
    """Compare two lists of phone numbers, normalizing datetime formats."""
    if len(phones1) != len(phones2):
        return False

    # Sort by type and number for consistent comparison
    sorted1 = sorted(phones1, key=lambda x: (x.get("type", ""), x.get("number", "")))
    sorted2 = sorted(phones2, key=lambda x: (x.get("type", ""), x.get("number", "")))

    for phone1, phone2 in zip(sorted1, sorted2):
        # Compare all fields, normalizing active_since
        for key in phone1:
            if key in ["active_since", "deactivated_at"]:
                if not compare_datetime_values(
                    phone1.get(key), phone2.get(key), f"Phone {key}"
                ):
                    return False
            elif phone1.get(key) != phone2.get(key):
                logger.debug(
                    f"Phone difference in {key}: {phone1.get(key)} != {phone2.get(key)}"
                )
                return False
    return True


def compare_emails(emails1, emails2):
    """Compare two lists of emails, normalizing datetime formats."""
    if len(emails1) != len(emails2):
        return False

    # Sort by type and address for consistent comparison
    sorted1 = sorted(emails1, key=lambda x: (x.get("type", ""), x.get("address", "")))
    sorted2 = sorted(emails2, key=lambda x: (x.get("type", ""), x.get("address", "")))

    for email1, email2 in zip(sorted1, sorted2):
        # Compare all fields, normalizing active_since
        for key in email1:
            if key == "active_since":
                if not compare_datetime_values(
                    email1.get(key), email2.get(key), "Email active_since"
                ):
                    return False
            elif email1.get(key) != email2.get(key):
                logger.debug(
                    f"Email difference in {key}: {email1.get(key)} != {email2.get(key)}"
                )
                return False
    return True


def compare_attendees(attendees1, attendees2):
    """Compare two lists of attendees, normalizing datetime formats."""
    if len(attendees1) != len(attendees2):
        return False

    # Sort by person ID for consistent comparison
    sorted1 = sorted(attendees1, key=lambda x: x.get("person", {}).get("id", ""))
    sorted2 = sorted(attendees2, key=lambda x: x.get("person", {}).get("id", ""))

    for attendee1, attendee2 in zip(sorted1, sorted2):
        # Compare person preview
        if not compare_person_preview(attendee1.get("person"), attendee2.get("person")):
            return False
        # Compare status
        if attendee1.get("status") != attendee2.get("status"):
            return False
        # Compare responded_at
        if not compare_datetime_values(
            attendee1.get("responded_at"),
            attendee2.get("responded_at"),
            "Attendee responded_at",
        ):
            return False
    return True


def update_person_attributes(existing: Person, new: Person) -> Tuple[Person, int]:
    """Update only missing or different attributes in existing person."""
    # Create a dict of the new person's attributes
    new_attrs = new.model_dump()

    # Create a dict of the existing person's attributes
    existing_attrs = existing.model_dump()

    updates_count = 0
    updated_fields = []
    # Update only if new value is different and not None
    for key, value in new_attrs.items():
        existing_value = existing_attrs.get(key)

        # Skip if both values are None
        if value is None and existing_value is None:
            continue

        # Special handling for specific fields
        if (
            key == "social_links"
            and isinstance(value, list)
            and isinstance(existing_value, list)
        ):
            if compare_social_links(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: social links differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        elif (
            key == "emails"
            and isinstance(value, list)
            and isinstance(existing_value, list)
        ):
            if compare_emails(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: emails differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        elif (
            key == "phone_numbers"
            and isinstance(value, list)
            and isinstance(existing_value, list)
        ):
            if compare_phone_numbers(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: phone numbers differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        elif (
            key == "upcoming_events"
            and isinstance(value, list)
            and isinstance(existing_value, list)
        ):
            if compare_upcoming_events(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: events differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        # Handle datetime objects
        elif isinstance(value, (datetime, UTCDateTime)) and isinstance(
            existing_value, (datetime, UTCDateTime)
        ):
            if compare_datetime_values(value, existing_value, key):
                continue
        # Handle other values
        elif str(value) == str(existing_value):
            continue
        else:
            logger.debug(f"Person - Updating {key}: values differ")
            logger.debug(f"  Existing: {existing_value}")
            logger.debug(f"  New: {value}")

        # Update if we get here
        setattr(existing, key, value)
        updates_count += 1
        updated_fields.append(key)

    if updated_fields:
        logger.debug(
            f"Updated person {existing.id} fields: {', '.join(updated_fields)}"
        )

    return existing, updates_count


def create_or_update_person(
    person_data: Dict[str, Any],
    container: CosmosContainer,
    upcoming_events: List[EventPreview],
    stats: Dict[str, Dict[str, int]],
) -> Person:
    """Create or update a person in the database.

    Args:
        person_data: Person data from JSON
        container: Cosmos DB container

    Returns:
        Person object that was created or updated
    """
    try:
        # Transform the person data
        transformed_person = transform_person(person_data)

        # Check if person exists
        existing_person = get_existing_person(transformed_person.id, container)

        if existing_person:
            # Update existing person
            existing_profile_pic = existing_person.profile_pic
            updates_count = 0
            transformed_person.upcoming_events = upcoming_events
            updated_person, updates_count = update_person_attributes(
                existing_person, transformed_person
            )
            updated_person.profile_pic = existing_profile_pic

            if not existing_profile_pic and person_data.get("profile_pic"):
                try:
                    updated_person.profile_pic = upload_profile_picture_from_file(
                        person_id=transformed_person.id,
                        image_filename=person_data["profile_pic"],
                    )
                    logger.info(f"Profile Pic updated to: {updated_person.profile_pic}")
                except Exception as e:
                    logger.warning(
                        f"Failed to upload profile picture for {transformed_person.id}: {str(e)}"
                    )
            if updates_count > 0:
                stats["people"]["updated"] += 1
                return container.replace_model(updated_person)
            else:
                return existing_person
        else:
            # Create new person
            if person_data.get("profile_pic"):
                try:
                    transformed_person.profile_pic = upload_profile_picture_from_file(
                        person_id=transformed_person.id,
                        image_filename=person_data["profile_pic"],
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to upload profile picture for {transformed_person.id}: {str(e)}"
                    )
            logger.info(
                f"Created person {transformed_person.id} with {len(upcoming_events)} upcoming events"
            )
            stats["people"]["created"] += 1
            return container.create_model(transformed_person)
    except Exception as e:
        logger.error(
            f"Failed to process person {person_data.get('id')}: {str(e)}", exc_info=True
        )
        raise


def get_existing_connection(
    connection: Connection, container: CosmosContainer
) -> Optional[Connection]:
    """Get existing connection from the database if it exists.

    Args:
        connection_id: UUID of the connection
        container: Cosmos DB container

    Returns:
        Connection object if found, None otherwise
    """
    try:
        return container.read_model(
            id=connection.id,
            partition_key=connection.owner_person_id,
            model_class=Connection,
        )  # type: ignore
    except CosmosResourceNotFoundError:
        logger.debug(
            f"Connection {connection.id} not found in database - will create new"
        )
        return None
    except Exception as e:
        logger.error(f"Error reading connection {connection.id}: {str(e)}")
        raise


def update_connection_attributes(
    existing: Connection, new: Connection
) -> Tuple[Connection, int]:
    """Update only missing or different attributes in existing connection.

    Args:
        existing: Existing connection from database
        new: New connection data to merge

    Returns:
        Tuple of (Updated Connection object, number of updates)
    """
    updates_count = 0
    # Update only if new value is different and not None
    for key, value in new.model_dump().items():
        existing_value = getattr(existing, key)
        if value != existing_value:
            setattr(existing, key, value)
            updates_count += 1

    return existing, updates_count


def create_or_update_connection(
    connection: Connection, container: CosmosContainer
) -> Connection:
    """Create or update a connection in the database.

    Args:
        connection: Connection object to create or update
        container: Cosmos DB container

    Returns:
        Connection object that was created or updated
    """
    try:
        # Check if connection exists
        existing_connection = get_existing_connection(connection, container)

        if existing_connection:
            # Update existing connection
            updated_connection, updates_count = update_connection_attributes(
                existing_connection, connection
            )
            if updates_count > 0:
                logger.info(f"Updated connection {connection.id}")
                return container.replace_model(updated_connection)
            return existing_connection
        else:
            # Create new connection
            logger.info(f"Creating new connection {connection.id}")
            return container.create_model(connection)
    except Exception as e:
        logger.error(
            f"Failed to process connection {connection.id}: {str(e)}", exc_info=True
        )
        raise


def generate_connections(
    people_data: List[Dict[str, Any]], container: CosmosContainer
) -> List[Dict[str, Any]]:
    """Generate connections between people based on predefined rules.

    Args:
        people_data: List of person data
        container: Cosmos DB container for connections

    Returns:
        List of generated connections
    """
    generated_connection_ids = {}
    connections = []

    def get_person_preview(person: Dict[str, Any]) -> PersonPreview:
        """Get a PersonPreview for a person."""
        return PersonPreview(
            id=UUID(person["id"]),
            name=person["name"],
            last_name=person.get("last_name", ""),
            current_role=person.get("current_role", ""),
            current_company=person.get("current_company", ""),
            location=person.get("location", ""),
            profile_pic_thumb=build_picture_url_by_id(
                parent_class=Person, parent_id=person["id"]
            ),
        )

    def generate_connection(
        id: str, requester: Dict[str, Any], requestee: Dict[str, Any], status: str
    ) -> List[Connection]:
        """Generate a bidirectional connection between two people."""
        new_records = []
        connection_id = gen_connection_id(requester["id"], requestee["id"])

        # Check if connection already exists
        try:
            existing = container.read_model(
                id=connection_id, partition_key=requester["id"], model_class=Connection
            )
            # TODO uncomment logger.debug(f"Connection {connection_id} already exists, skipping creation")
            return []
        except CosmosResourceNotFoundError:
            pass  # Connection doesn't exist, proceed with creation

        # First direction (requester -> requestee)
        connection = Connection(
            id=connection_id,
            requester_person_id=UUID(requester["id"]),
            requestee_person_id=UUID(requestee["id"]),
            status=status,
            person_preview=get_person_preview(requestee),
            owner_person_id=UUID(requester["id"]),
        )
        new_records.append(connection)

        # Second direction (requestee -> requester)
        connection = Connection(
            id=connection_id,
            requester_person_id=UUID(requester["id"]),
            requestee_person_id=UUID(requestee["id"]),
            status=status,
            person_preview=get_person_preview(requester),
            owner_person_id=UUID(requestee["id"]),
        )
        new_records.append(connection)

        return new_records

    for record in people_data:
        # Add "accepted" connections
        counter = 0
        for counter_record in people_data:
            if counter == 5:  # Limit to 5 accepted connections per person
                break

            if record["id"] == counter_record["id"]:
                continue

            id = "-".join(sorted([record["id"], counter_record["id"]]))

            if id in generated_connection_ids:
                if generated_connection_ids[id] == "accepted":
                    counter += 1
                continue

            counter += 1
            generated_connection_ids[id] = "accepted"
            connections.extend(
                generate_connection(id, record, counter_record, "accepted")
            )

        # Add "requested" connections
        counter = 0
        for counter_record in people_data:
            if counter == 5:  # Limit to 5 requested connections per person
                break

            if record["id"] == counter_record["id"]:
                continue

            id = "-".join(sorted([record["id"], counter_record["id"]]))

            if id in generated_connection_ids:
                if generated_connection_ids[id] == "requested":
                    counter += 1
                continue

            counter += 1
            generated_connection_ids[id] = "requested"
            connections.extend(
                generate_connection(id, record, counter_record, "requested")
            )

    # Save connections to database
    for connection in connections:
        try:
            container.create_model(connection)
            logger.info(
                f"Created connection {connection.id} between {connection.requester_person_id} and {connection.requestee_person_id}"
            )
        except Exception as e:
            logger.error(f"Failed to create connection {connection.id}: {str(e)}")

    return connections


def get_upcoming_events_for_person(
    person_id: UUID, events: List[Event]
) -> List[EventPreview]:
    """Get upcoming events for a person.

    Args:
        person_id: UUID of the person
        events: List of transformed events

    Returns:
        List of EventPreview objects for upcoming events
    """
    upcoming_events = []

    for event in events:
        # Check if person is an attendee
        attendee = next((a for a in event.attendees if a.person.id == person_id), None)
        if not attendee:
            continue

        # Check if event is in the future
        if event.start_time <= now():
            continue

        # Check if person is going or interested
        if attendee.status not in ["going", "interested"]:
            continue

        # Create EventPreview
        upcoming_events.append(
            EventPreview(
                id=event.id,
                title=event.title,
                start_time=event.start_time,
                location=event.location,
            )
        )

    return sorted(upcoming_events, key=lambda x: x.start_time)


def get_existing_event(event_id: UUID, container: CosmosContainer) -> Optional[Event]:
    """Get existing event from the database if it exists.

    Args:
        event_id: UUID of the event
        container: Cosmos DB container

    Returns:
        Event object if found, None otherwise
    """
    try:
        return container.read_model(
            id=event_id, partition_key=event_id, model_class=Event
        )
    except CosmosResourceNotFoundError:
        logger.debug(f"Event {event_id} not found in database - will create new")
        return None


def compare_person_preview(preview1, preview2):
    """Compare two person previews."""
    if not preview1 or not preview2:
        return preview1 == preview2

    # Compare all fields except profile_pic_thumb (which might have different formats)
    for key in preview1:
        if key == "profile_pic_thumb":
            continue
        if preview1.get(key) != preview2.get(key):
            logger.debug(
                f"Person preview difference in {key}: {preview1.get(key)} != {preview2.get(key)}"
            )
            return False
    return True


def update_event_attributes(existing: Event, new: Event) -> Tuple[Event, int]:
    """Update only missing or different attributes in existing event."""
    # Create a dict of the new event's attributes
    new_attrs = new.model_dump()

    # Create a dict of the existing event's attributes
    existing_attrs = existing.model_dump()

    def compare_attendees(attendees1, attendees2):
        """Compare two lists of attendees, normalizing datetime formats."""
        if len(attendees1) != len(attendees2):
            return False

        # Sort by person ID for consistent comparison
        sorted1 = sorted(attendees1, key=lambda x: x.get("person", {}).get("id", ""))
        sorted2 = sorted(attendees2, key=lambda x: x.get("person", {}).get("id", ""))

        for attendee1, attendee2 in zip(sorted1, sorted2):
            # Compare person preview
            if not compare_person_preview(
                attendee1.get("person"), attendee2.get("person")
            ):
                return False
            # Compare status
            if attendee1.get("status") != attendee2.get("status"):
                return False
            # Compare responded_at
            if compare_datetime_values(
                attendee1.get("responded_at"),
                attendee2.get("responded_at"),
                "Attendee responded_at",
            ):
                continue
        return True

    def compare_hosts(hosts1, hosts2):
        """Compare two lists of hosts."""
        if len(hosts1) != len(hosts2):
            return False

        # Sort by ID for consistent comparison
        sorted1 = sorted(hosts1, key=lambda x: x.get("id", ""))
        sorted2 = sorted(hosts2, key=lambda x: x.get("id", ""))

        for host1, host2 in zip(sorted1, sorted2):
            if not compare_person_preview(host1, host2):
                return False
        return True

    updates_count = 0
    updated_fields = []
    # Update only if new value is different and not None
    for key, value in new_attrs.items():
        existing_value = existing_attrs.get(key)

        # Skip if both values are None
        if value is None and existing_value is None:
            continue

        # Special handling for specific fields
        if (
            key == "attendees"
            and isinstance(value, list)
            and isinstance(existing_value, list)
        ):
            if compare_attendees(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: attendees differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        elif (
            key == "hosts"
            and isinstance(value, list)
            and isinstance(existing_value, list)
        ):
            if compare_hosts(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: hosts differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        # Handle datetime objects
        elif isinstance(value, (datetime, UTCDateTime)) and isinstance(
            existing_value, (datetime, UTCDateTime)
        ):
            if compare_datetime_values(value, existing_value, key):
                continue
        # Handle other values
        elif str(value) == str(existing_value):
            continue
        else:
            logger.debug(f"Event - Updating {key}: values differ")
            logger.debug(f"  Existing: {existing_value}")
            logger.debug(f"  New: {value}")

        # Update if we get here
        setattr(existing, key, value)
        updates_count += 1
        updated_fields.append(key)

    if updated_fields:
        logger.debug(f"Updated event {existing.id} fields: {', '.join(updated_fields)}")

    return existing, updates_count


def create_or_update_event(
    event: Event, container: CosmosContainer, stats: Dict[str, Dict[str, int]]
) -> Event:
    """Create or update an event in the database.

    Args:
        event: Event object to create or update
        container: Cosmos DB container

    Returns:
        Event object that was created or updated
    """
    try:
        # Check if event exists
        existing_event = get_existing_event(event.id, container)

        if existing_event:
            # Update existing event
            updated_event, updates_count = update_event_attributes(
                existing_event, event
            )
            if updates_count > 0:
                stats["events"]["updated"] += 1
                return container.replace_model(updated_event)
            return existing_event
        else:
            # Create new event
            stats["events"]["created"] += 1
            return container.create_model(event)
    except Exception as e:
        logger.error(f"Failed to process event {event.id}: {str(e)}", exc_info=True)
        raise


def get_existing_community(
    community_id: UUID, container: CosmosContainer
) -> Optional[Community]:
    """Get existing community from the database if it exists.

    Args:
        community_id: UUID of the community
        container: Cosmos DB container

    Returns:
        Community object if found, None otherwise
    """
    try:
        return container.read_model(
            id=community_id, partition_key=community_id, model_class=Community
        )
    except CosmosResourceNotFoundError:
        logger.debug(
            f"Community {community_id} not found in database - will create new"
        )
        return None
    except Exception as e:
        logger.error(f"Error reading community {community_id}: {str(e)}")
        raise


def compare_members(members1, members2):
    """Compare two lists of members, normalizing datetime formats."""
    if len(members1) != len(members2):
        return False

    # Sort by person_id for consistent comparison
    sorted1 = sorted(members1, key=lambda x: x.get("person_id", ""))
    sorted2 = sorted(members2, key=lambda x: x.get("person_id", ""))

    for member1, member2 in zip(sorted1, sorted2):
        # Compare all fields, normalizing joined_at
        for key in member1:
            if key == "joined_at":
                if compare_datetime_values(
                    member1.get(key), member2.get(key), f"Member {key}"
                ):
                    continue
            elif member1.get(key) != member2.get(key):
                logger.debug(
                    f"Member difference in {key}: {member1.get(key)} != {member2.get(key)}"
                )
                return False
    return True


def compare_person_preview(preview1, preview2):
    """Compare two person previews."""
    if not preview1 or not preview2:
        return preview1 == preview2

    for key in preview1:
        if preview1.get(key) != preview2.get(key):
            logger.debug(
                f"Person preview difference in {key}: {preview1.get(key)} != {preview2.get(key)}"
            )
            return False
    return True


def compare_community_settings(settings1, settings2):
    """Compare two community settings objects."""
    if not settings1 or not settings2:
        return settings1 == settings2

    for key in settings1:
        if settings1.get(key) != settings2.get(key):
            logger.debug(
                f"Settings difference in {key}: {settings1.get(key)} != {settings2.get(key)}"
            )
            return False
    return True


def update_community_attributes(
    existing: Community, new: Community
) -> Tuple[Community, int]:
    """Update only missing or different attributes in existing community.

    Args:
        existing: Existing community from database
        new: New community data to merge

    Returns:
        Tuple of (Updated Community object, number of updates)
    """
    # Create a dict of the new community's attributes
    new_attrs = new.model_dump()

    # Create a dict of the existing community's attributes
    existing_attrs = existing.model_dump()

    updates_count = 0
    updated_fields = []
    # Update only if new value is different and not None
    for key, value in new_attrs.items():
        existing_value = existing_attrs.get(key)

        # Skip if both values are None
        if value is None and existing_value is None:
            continue

        # Special handling for specific fields
        if (
            key == "members"
            and isinstance(value, list)
            and isinstance(existing_value, list)
        ):
            if compare_members(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: members differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        elif (
            key == "created_by"
            and isinstance(value, dict)
            and isinstance(existing_value, dict)
        ):
            if compare_person_preview(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: created_by differs")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        elif (
            key == "settings"
            and isinstance(value, dict)
            and isinstance(existing_value, dict)
        ):
            if compare_community_settings(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: settings differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        # Handle datetime objects
        elif isinstance(value, (datetime, UTCDateTime)) or isinstance(
            existing_value, (datetime, UTCDateTime)
        ):
            if compare_datetime_values(value, existing_value, key):
                continue
        # Handle lists
        elif isinstance(value, list) and isinstance(existing_value, list):
            if compare_lists(value, existing_value):
                continue
            else:
                logger.debug(f"Updating {key}: lists differ")
                logger.debug(f"  Existing: {json.dumps(existing_value, default=str)}")
                logger.debug(f"  New: {json.dumps(value, default=str)}")
        # Handle other values
        elif str(value) == str(existing_value):
            continue
        else:
            logger.debug(f"Community - Updating {key}: values differ")
            logger.debug(f"  Existing: {type(existing_value)} - {existing_value}")
            logger.debug(f"  New: {type(value)} - {value}")

        # Update if we get here
        setattr(existing, key, value)
        updates_count += 1
        updated_fields.append(key)

    if updated_fields:
        logger.debug(
            f"Updated community {existing.id} fields: {', '.join(updated_fields)}"
        )

    return existing, updates_count


def create_or_update_community(
    community: Community, container: CosmosContainer, stats: Dict[str, Dict[str, int]]
) -> Community:
    """Create or update a community in the database.

    Args:
        community: Community object to create or update
        container: Cosmos DB container

    Returns:
        Community object that was created or updated
    """
    try:
        # Check if community exists
        existing_community = get_existing_community(community.id, container)

        if existing_community:
            # Update existing community
            updated_community, updates_count = update_community_attributes(
                existing_community, community
            )
            if updates_count > 0:
                stats["communities"]["updated"] += 1
                return container.replace_model(updated_community)
            return existing_community
        else:
            # Create new community
            logger.info(f"Creating new community {community.id}")
            stats["communities"]["created"] += 1
            return container.create_model(community)
    except Exception as e:
        logger.error(
            f"Failed to process community {community.id}: {str(e)}", exc_info=True
        )
        raise


def compare_social_links(links1, links2):
    """Compare two lists of social links, normalizing datetime formats."""
    if len(links1) != len(links2):
        return False

    # Sort by platform and id for consistent comparison
    sorted1 = sorted(links1, key=lambda x: (x.get("platform", ""), x.get("id", "")))
    sorted2 = sorted(links2, key=lambda x: (x.get("platform", ""), x.get("id", "")))

    for link1, link2 in zip(sorted1, sorted2):
        # Compare all fields, normalizing connected_at
        for key in link1:
            if key == "connected_at":
                if not compare_datetime_values(
                    link1.get(key), link2.get(key), "Social link connected_at"
                ):
                    return False
            elif link1.get(key) != link2.get(key):
                logger.debug(
                    f"Social link difference in {key}: {link1.get(key)} != {link2.get(key)}"
                )
                return False
    return True


def compare_upcoming_events(events1, events2):
    """Compare two lists of upcoming events, focusing on essential fields."""
    if len(events1) != len(events2):
        return False

    # Sort by event ID for consistent comparison
    sorted1 = sorted(events1, key=lambda x: x.get("id", ""))
    sorted2 = sorted(events2, key=lambda x: x.get("id", ""))

    for event1, event2 in zip(sorted1, sorted2):
        # Compare only essential fields
        if event1.get("id") != event2.get("id"):
            logger.debug(
                f"Event ID difference: {event1.get('id')} != {event2.get('id')}"
            )
            return False
        if not compare_datetime_values(
            event1.get("start_time"), event2.get("start_time"), "Event start_time"
        ):
            return False
        if not compare_datetime_values(
            event1.get("end_time"), event2.get("end_time"), "Event end_time"
        ):
            return False
        # Compare other fields
        for key in ["title", "location", "community_id"]:
            if event1.get(key) != event2.get(key):
                logger.debug(
                    f"Event difference in {key}: {event1.get(key)} != {event2.get(key)}"
                )
                return False
    return True


def compare_lists(list1, list2):
    """Compare two lists of simple values (strings, numbers, etc)."""
    if len(list1) != len(list2):
        return False

    # Sort lists for consistent comparison
    sorted1 = sorted(str(x) for x in list1)
    sorted2 = sorted(str(x) for x in list2)

    for val1, val2 in zip(sorted1, sorted2):
        if val1 != val2:
            logger.debug(f"List difference: {val1} != {val2}")
            return False
    return True


def main():
    """Main function to transform data."""
    # Parse command line arguments
    args = parse_args()

    # Get environment (either from args or prompt)
    environment = args.env if args.env else get_environment()
    logger.info(f"\nSelected environment: {environment}")

    # Load environment configuration
    load_environment_config(environment)
    logger.info(f"Using Cosmos DB: {os.getenv('COSMOS_DB')}")
    logger.info(
        f"Using Azure blob: {re.search(r'AccountName=([^;]+)', os.getenv('AZURE_BLOB_CONNECTION_STRING')).group(1)}"
    )

    # Setup paths
    data_dir = Path("scripts/data")
    people_json = data_dir / "people.json"
    communities_json = data_dir / "communities.json"
    events_json = data_dir / "events.json"
    output_dir = data_dir / "transformed"
    output_dir.mkdir(exist_ok=True)

    # Get Cosmos DB containers
    people_container = get_people_container()
    communities_container = get_communities_container()
    connections_container = get_connections_container()
    events_container = get_events_container()

    # Initialize counters
    stats = {
        "people": {"created": 0, "updated": 0},
        "events": {"created": 0, "updated": 0},
        "communities": {"created": 0, "updated": 0},
        "connections": {"created": 0, "updated": 0},
    }

    # Load and process people data
    people_data = load_json_file(str(people_json))
    transformed_people = []

    # Load and transform event data first
    events_data = load_json_file(str(events_json))
    transformed_events = []
    for event_data in events_data:
        try:
            transformed_event = transform_event(event_data, people_data)
            transformed_event = create_or_update_event(
                transformed_event, events_container, stats
            )
            # Convert the event back to an Event object if it's a dictionary
            if isinstance(transformed_event, dict):
                transformed_event = Event.model_validate(transformed_event)
            transformed_events.append(transformed_event)
        except Exception as e:
            logger.error(
                f"Failed to process event {event_data.get('id')}: {str(e)}",
                exc_info=True,
            )
            continue

    # Generate connections between people
    logger.info("Generating connections between people...")
    connections = generate_connections(people_data, connections_container)
    stats["connections"]["created"] = len(connections)

    # Load and transform community data
    communities_data = load_json_file(str(communities_json))
    transformed_communities = []
    for community_data in communities_data:
        try:
            transformed_community = transform_community(community_data, people_data)
            transformed_community = create_or_update_community(
                transformed_community, communities_container, stats
            )
            # Convert the community back to a Community object if it's a dictionary
            if isinstance(transformed_community, dict):
                transformed_community = Community.model_validate(transformed_community)
            transformed_communities.append(transformed_community)
        except Exception as e:
            logger.error(
                f"Failed to process community {community_data.get('id')}: {str(e)}",
                exc_info=True,
            )
            continue

    # Process people with their upcoming events
    logger.info("Processing people with their upcoming events...")
    for person_data in people_data:
        try:
            # Get upcoming events for this person
            person_id = UUID(person_data["id"])
            upcoming_events = get_upcoming_events_for_person(
                person_id, transformed_events
            )

            # Create or update person with their upcoming events
            person = create_or_update_person(
                person_data, people_container, upcoming_events, stats
            )
            transformed_people.append(person)
        except Exception as e:
            logger.error(
                f"Failed to process person {person_data.get('id')}: {str(e)}",
                exc_info=True,
            )
            continue

    # Convert objects to dictionaries for JSON serialization
    serializable_people = [
        person if isinstance(person, dict) else person.model_dump()
        for person in transformed_people
    ]
    serializable_connections = [
        connection if isinstance(connection, dict) else connection.model_dump()
        for connection in connections
    ]
    serializable_communities = [
        community if isinstance(community, dict) else community.model_dump()
        for community in transformed_communities
    ]
    serializable_events = [
        event if isinstance(event, dict) else event.model_dump()
        for event in transformed_events
    ]

    # Save transformed data
    save_json_file(serializable_people, str(output_dir / "mock_people.json"))
    save_json_file(serializable_connections, str(output_dir / "mock_connections.json"))
    save_json_file(serializable_communities, str(output_dir / "mock_communities.json"))
    save_json_file(serializable_events, str(output_dir / "mock_events.json"))

    # Log final statistics
    logger.info("\nFinal Statistics:")
    logger.info(
        f"People: {stats['people']['created']} created, {stats['people']['updated']} updated"
    )
    logger.info(
        f"Events: {stats['events']['created']} created, {stats['events']['updated']} updated"
    )
    logger.info(
        f"Communities: {stats['communities']['created']} created, {stats['communities']['updated']} updated"
    )
    logger.info(f"Connections: {stats['connections']['created']} created")


if __name__ == "__main__":
    main()
