"""Path setup module for the peepsAPI project.

This module adds the project root to the Python path, allowing imports from the project root.
Import this module at the top of any script that needs to import from the project root.

Example usage:
    import scripts.path_setup  # noqa: F401 - imported for side effects
    from peepsapi.some_module import SomeClass
"""
import sys
from pathlib import Path

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)
