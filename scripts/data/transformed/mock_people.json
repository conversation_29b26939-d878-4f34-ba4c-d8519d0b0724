[{"id": "914ba888-5408-4d5f-bea2-b240c86949fb", "name": "Ankit", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "current_role": "Product Designer", "current_company": "<PERSON><PERSON>", "location": "Hong Kong", "profile_pic": "/pictures/person/914ba888-5408-4d5f-bea2-b240c86949fb", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:22.614176+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+4979343451", "username": "+4979343451", "id": "+4979343451", "verified": false, "connected_at": "2025-04-03T23:26:22.614176"}, {"platform": "linkedin", "url": "https://linkedin.com/ankit520", "username": "ankit520", "id": "ankit520", "verified": false, "connected_at": "2025-04-03T23:26:22.614176"}, {"platform": "twitter", "url": "https://twitter.com/ankit955", "username": "ankit955", "id": "ankit955", "verified": false, "connected_at": "2025-04-03T23:26:22.614176"}, {"platform": "facebook", "url": "https://facebook.com/ankit375", "username": "ankit375", "id": "ankit375", "verified": false, "connected_at": "2025-04-03T23:26:22.614176"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+4979343451", "active_since": "2025-04-03T23:26:22.614176", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON><PERSON> is a Product Designer at Bowtie Life Insurance Company in Hong Kong, where he focuses on crafting user-centric digital experiences in the insurtech sector. With a background in UX design and a passion for simplifying complex systems, he contributes to building intuitive and accessible insurance solutions", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAfAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAfAAAAAAAAAA==/", "_etag": "\"e8001f5a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "7bcee6d9-61ca-4316-9352-e370e38df749", "name": "<PERSON>", "last_name": "Goldsmith", "current_role": "Founder", "current_company": "ProjectMoonHut.com", "location": "New York, NY", "profile_pic": "/pictures/person/7bcee6d9-61ca-4316-9352-e370e38df749", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:20.534633+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+85248483092", "username": "+85248483092", "id": "+85248483092", "verified": false, "connected_at": "2025-04-03T23:26:20.534633"}, {"platform": "facebook", "url": "https://facebook.com/david374", "username": "david374", "id": "david374", "verified": false, "connected_at": "2025-04-03T23:26:20.534633"}, {"platform": "twitter", "url": "https://twitter.com/david888", "username": "david888", "id": "david888", "verified": false, "connected_at": "2025-04-03T23:26:20.534633"}, {"platform": "linkedin", "url": "https://linkedin.com/david175", "username": "david175", "id": "david175", "verified": false, "connected_at": "2025-04-03T23:26:20.534633"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85248483092", "active_since": "2025-04-03T23:26:20.534633", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "I am a serial entrepreneur, inventor, investor, consultant, board member, advisor, speaker, and author of the best-selling book Paid to THINK: A Leader’s Toolkit for Redefining Your Future [on its 14th printing].", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAgAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAgAAAAAAAAAA==/", "_etag": "\"e800205a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "e69fc4e8-e461-49e4-9c0d-edb3cc6dfaab", "name": "<PERSON>", "last_name": "<PERSON><PERSON>", "current_role": "UX/UI Designer", "current_company": "Votee AI", "location": "Hong Kong", "profile_pic": "/pictures/person/e69fc4e8-e461-49e4-9c0d-edb3cc6dfaab", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:22.621361+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+4968351922", "username": "+4968351922", "id": "+4968351922", "verified": false, "connected_at": "2025-04-03T23:26:22.621361"}, {"platform": "twitter", "url": "https://twitter.com/adrian849", "username": "adrian849", "id": "adrian849", "verified": false, "connected_at": "2025-04-03T23:26:22.621361"}, {"platform": "linkedin", "url": "https://linkedin.com/adrian163", "username": "adrian163", "id": "adrian163", "verified": false, "connected_at": "2025-04-03T23:26:22.621361"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+4968351922", "active_since": "2025-04-03T23:26:22.621361", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is a UX/UI Designer at Votee AI, where he specializes in creating intuitive and engaging user experiences for AI-driven platforms. With a strong background in user-centered design, <PERSON> focuses on enhancing digital interfaces to improve user interaction and satisfaction.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAhAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAhAAAAAAAAAA==/", "_etag": "\"e800215a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "current_role": "CoFounder", "current_company": "Know Your People Inc.", "location": "Seattle, WA", "profile_pic": "/pictures/person/6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:20.500151+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+***********", "username": "+***********", "id": "+***********", "verified": false, "connected_at": "2025-04-03T23:26:20.500151"}, {"platform": "twitter", "url": "https://twitter.com/david335", "username": "david335", "id": "david335", "verified": false, "connected_at": "2025-04-03T23:26:20.500151"}, {"platform": "facebook", "url": "https://facebook.com/david910", "username": "david910", "id": "david910", "verified": false, "connected_at": "2025-04-03T23:26:20.500151"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+***********", "active_since": "2025-04-03T23:26:20.500151", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "I'm a growth catalyst, business-savvy technologist, and a coffee-tank based in Seattle. I believe that human ingenuity and technology are better together. This belief fuels my contributions to People App and my knack for building strong teams.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAiAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAiAAAAAAAAAA==/", "_etag": "\"e800225a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "name": "Ilya", "last_name": "Belikin", "current_role": "CoFounder", "current_company": "Know Your People Inc.", "location": "Hong Kong", "profile_pic": "/pictures/person/661dd3e3-9aab-4554-9883-dfdd02fb42da", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-03-22 12:12:20.453284+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+85295378570", "username": "+85295378570", "id": "+85295378570", "verified": false, "connected_at": "2025-03-22T12:12:20.453284"}, {"platform": "facebook", "url": "https://facebook.com/ilyabelikin", "username": "ilyabelikin", "id": "ilyabelikin", "verified": false, "connected_at": "2025-03-22T12:12:20.453284"}, {"platform": "linkedin", "url": "https://linkedin.com/ilyabelikin", "username": "ilyabelikin", "id": "ilyabelikin", "verified": false, "connected_at": "2025-03-22T12:12:20.453284"}, {"platform": "instagram", "url": "https://instagram.com/ilyabelikin", "username": "ilyabelikin", "id": "ilyabelikin", "verified": false, "connected_at": "2025-03-22T12:12:20.453284"}, {"platform": "telegram", "url": "https://telegram.com/ilyabelikin", "username": "ilyabelikin", "id": "ilyabelikin", "verified": false, "connected_at": "2025-03-22T12:12:20.453284"}, {"platform": "bluesky", "url": "https://bluesky.com/ihrd.bsky.social", "username": "ihrd.bsky.social", "id": "ihrd.bsky.social", "verified": false, "connected_at": "2025-03-22T12:12:20.453284"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85295378570", "active_since": "2025-03-22T12:12:20.453284", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "5c6f5b3f-d22e-4300-b665-b7cca2d528d7", "title": "AMA with Founders", "start_time": "2025-08-29T09:16:18.157416Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "Citizen, Designer, Tea-drunk 🍵. I love to design and build products that help people connect and grow.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAjAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAjAAAAAAAAAA==/", "_etag": "\"e800235a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "aa52d301-0ede-4ddc-a988-3a4e09a3abec", "name": "Pol<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "current_role": "Head of Community", "current_company": "The Desk", "location": "Hong Kong", "profile_pic": "/pictures/person/aa52d301-0ede-4ddc-a988-3a4e09a3abec", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:21.582331+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+85242085559", "username": "+85242085559", "id": "+85242085559", "verified": false, "connected_at": "2025-04-03T23:26:21.582331"}, {"platform": "linkedin", "url": "https://linkedin.com/polina230", "username": "polina230", "id": "polina230", "verified": false, "connected_at": "2025-04-03T23:26:21.582331"}, {"platform": "facebook", "url": "https://facebook.com/polina557", "username": "polina557", "id": "polina557", "verified": false, "connected_at": "2025-04-03T23:26:21.582331"}], "emails": [{"type": "work", "address": "polina.sara<PERSON><PERSON>@thedesk.com", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "polina.<PERSON><PERSON><PERSON><PERSON>@gmail.com", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85242085559", "active_since": "2025-04-03T23:26:21.582331", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "000c3582-e686-4bdc-9dbd-8a9d4625a0e1", "title": "Coffee & Connect", "start_time": "2025-07-03T11:16:18.155734Z", "location": "Zoom"}, {"id": "5c6f5b3f-d22e-4300-b665-b7cca2d528d7", "title": "AMA with Founders", "start_time": "2025-08-29T09:16:18.157416Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON> is the Head of Community at theDesk in Hong Kong, where she leads initiatives to foster collaboration among members. With a background in interior architecture and design, she brings a creative approach to building vibrant coworking communities.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAkAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAkAAAAAAAAAA==/", "_etag": "\"e800245a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "335bc62f-c046-4c5b-a588-dd9286bcd794", "name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "current_role": "Senior iOS Engineer", "current_company": "Lloyds", "location": "Bristol, UK", "profile_pic": "/pictures/person/335bc62f-c046-4c5b-a588-dd9286bcd794", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:22.629628+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+17955993616", "username": "+17955993616", "id": "+17955993616", "verified": false, "connected_at": "2025-04-03T23:26:22.629628"}, {"platform": "facebook", "url": "https://facebook.com/yuri695", "username": "yuri695", "id": "yuri695", "verified": false, "connected_at": "2025-04-03T23:26:22.629628"}, {"platform": "twitter", "url": "https://twitter.com/yuri284", "username": "yuri284", "id": "yuri284", "verified": false, "connected_at": "2025-04-03T23:26:22.629628"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+17955993616", "active_since": "2025-04-03T23:26:22.629628", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "5c6f5b3f-d22e-4300-b665-b7cca2d528d7", "title": "AMA with Founders", "start_time": "2025-08-29T09:16:18.157416Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is a Senior iOS Engineer with a decade of experience in mobile development. He has worked with companies like Babylon Health and TAOlight, and authored Junior to Senior: How to Level Up as a Software Engineer, a technology-agnostic guide offering practical advice for career growth in software development.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAlAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAlAAAAAAAAAA==/", "_etag": "\"e800255a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "c47502b2-12a5-4c19-b03f-c4a6be5640bb", "name": "<PERSON>", "last_name": "<PERSON>", "current_role": "Host", "current_company": "Web Summit", "location": "Seattle, WA", "profile_pic": "/pictures/person/c47502b2-12a5-4c19-b03f-c4a6be5640bb", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:21.574939+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+19449341041", "username": "+19449341041", "id": "+19449341041", "verified": false, "connected_at": "2025-04-03T23:26:21.574939"}, {"platform": "twitter", "url": "https://twitter.com/casey907", "username": "casey907", "id": "casey907", "verified": false, "connected_at": "2025-04-03T23:26:21.574939"}, {"platform": "linkedin", "url": "https://linkedin.com/casey807", "username": "casey807", "id": "casey807", "verified": false, "connected_at": "2025-04-03T23:26:21.574939"}, {"platform": "facebook", "url": "https://facebook.com/casey960", "username": "casey960", "id": "casey960", "verified": false, "connected_at": "2025-04-03T23:26:21.574939"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+19449341041", "active_since": "2025-04-03T23:26:21.574939", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "1218d15a-37f3-43f5-a377-eeddf6ad970e", "title": "Community Meetup", "start_time": "2025-06-22T20:16:18.156203Z", "location": "Seattle"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is the Vice President of Asia Pacific at Web Summit, one of the world’s largest technology conferences. Based in Hong Kong, he co-hosts The GM Show by Memeland and is a prominent figure in Asia’s startup ecosystem, having co-founded StartupsHK and previously led ventures like ActionAce.com. <PERSON> is also involved in building a creative studio at the intersection of Web3 and AI.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAmAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAmAAAAAAAAAA==/", "_etag": "\"e800265a-0000-0700-0000-683a812d0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "b0e80b71-a158-4dd0-b679-bc00f797ab07", "name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "current_role": "Product Manager", "current_company": "GOGOX", "location": "Hong Kong", "profile_pic": "/pictures/person/b0e80b71-a158-4dd0-b679-bc00f797ab07", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:20.519443+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+13269696858", "username": "+13269696858", "id": "+13269696858", "verified": false, "connected_at": "2025-04-03T23:26:20.519443"}, {"platform": "facebook", "url": "https://facebook.com/adnan226", "username": "adnan226", "id": "adnan226", "verified": false, "connected_at": "2025-04-03T23:26:20.519443"}, {"platform": "twitter", "url": "https://twitter.com/adnan782", "username": "adnan782", "id": "adnan782", "verified": false, "connected_at": "2025-04-03T23:26:20.519443"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+13269696858", "active_since": "2025-04-03T23:26:20.519443", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [{"name": "Ubuntu", "color": "#964B00", "comment": "Made 1000+ introductions"}], "bio": "<PERSON><PERSON> is a Product Manager at GOGOX in Hong Kong, where he leads product development for logistics and mobility solutions. He actively shares his expertise through guest lectures and talks, guiding aspiring professionals on transitioning into tech product roles.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAnAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAnAAAAAAAAAA==/", "_etag": "\"e800275a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "18ad0b14-8269-45e4-ad01-827bc477c57f", "name": "Iris", "last_name": "Mir", "current_role": "Freelancer", "current_company": "Independent", "location": "Hong Kong", "profile_pic": "/pictures/person/18ad0b14-8269-45e4-ad01-827bc477c57f", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:22.606951+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+4457366566", "username": "+4457366566", "id": "+4457366566", "verified": false, "connected_at": "2025-04-03T23:26:22.606951"}, {"platform": "facebook", "url": "https://facebook.com/iris685", "username": "iris685", "id": "iris685", "verified": false, "connected_at": "2025-04-03T23:26:22.606951"}, {"platform": "twitter", "url": "https://twitter.com/iris202", "username": "iris202", "id": "iris202", "verified": false, "connected_at": "2025-04-03T23:26:22.606951"}, {"platform": "linkedin", "url": "https://linkedin.com/iris878", "username": "iris878", "id": "iris878", "verified": false, "connected_at": "2025-04-03T23:26:22.606951"}], "emails": [{"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+4457366566", "active_since": "2025-04-03T23:26:22.606951", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "000c3582-e686-4bdc-9dbd-8a9d4625a0e1", "title": "Coffee & Connect", "start_time": "2025-07-03T11:16:18.155734Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is a Barcelona-born writer, photographer, and creative educator based in Tenerife, Canary Islands. With a background in Communication Sciences and Political Science, she spent over a decade in Hong Kong and Beijing as an international correspondent and cross-cultural project manager. Her work explores themes of cultural identity, self-expression, and storytelling through writing, photography, and movement-based practices. She is the author of The Mansion South of Maple Street and Passing Guest, both inspired by her experiences in Asia.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAoAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAoAAAAAAAAAA==/", "_etag": "\"e800285a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "83c07ea7-bc38-4b64-8104-de3111278ef3", "name": "<PERSON>", "last_name": "San", "current_role": "Co-Founder", "current_company": "GenieFriends", "location": "Seattle, WA", "profile_pic": "/pictures/person/83c07ea7-bc38-4b64-8104-de3111278ef3", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:20.542181+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+13721013678", "username": "+13721013678", "id": "+13721013678", "verified": false, "connected_at": "2025-04-03T23:26:20.542181"}, {"platform": "linkedin", "url": "https://linkedin.com/eric437", "username": "eric437", "id": "eric437", "verified": false, "connected_at": "2025-04-03T23:26:20.542181"}, {"platform": "facebook", "url": "https://facebook.com/eric413", "username": "eric413", "id": "eric413", "verified": false, "connected_at": "2025-04-03T23:26:20.542181"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+13721013678", "active_since": "2025-04-03T23:26:20.542181", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "344ac7d1-9feb-4226-8f55-5c0c98d77c16", "title": "Demo Day", "start_time": "2025-09-06T07:16:18.156034Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is the co-founder of GenieFriends, a Hong Kong-based community platform that fosters meaningful connections among creatives, technologists, and entrepreneurs through curated events and AI-powered matching. With a background in product and data roles at Uber and On Deck, <PERSON> is passionate about building communities that spark collaboration and innovation", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkApAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkApAAAAAAAAAA==/", "_etag": "\"e800295a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "88c9e8ab-fe80-4b7b-8a7d-ae31cf92e903", "name": "<PERSON><PERSON>", "last_name": "<PERSON>", "current_role": "Research Fellow", "current_company": "Al Safety", "location": "Hong Kong", "profile_pic": "/pictures/person/88c9e8ab-fe80-4b7b-8a7d-ae31cf92e903", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:22.599882+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+85256242313", "username": "+85256242313", "id": "+85256242313", "verified": false, "connected_at": "2025-04-03T23:26:22.599882"}, {"platform": "twitter", "url": "https://twitter.com/haihao814", "username": "haihao814", "id": "haihao814", "verified": false, "connected_at": "2025-04-03T23:26:22.599882"}, {"platform": "linkedin", "url": "https://linkedin.com/haihao406", "username": "haihao406", "id": "haihao406", "verified": false, "connected_at": "2025-04-03T23:26:22.599882"}, {"platform": "facebook", "url": "https://facebook.com/haihao439", "username": "haihao439", "id": "haihao439", "verified": false, "connected_at": "2025-04-03T23:26:22.599882"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85256242313", "active_since": "2025-04-03T23:26:22.599882", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON> is an AI safety researcher and community builder with a background in materials science and mathematics from Rice University, MIT, and Harvard. He has contributed to initiatives like BlueDot Impact Connect, a global mentorship platform for AI safety, and co-organized events such as the Women in AI Safety Hackathon.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAqAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAqAAAAAAAAAA==/", "_etag": "\"e8002a5a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "63065124-8b38-4db7-b078-0448d981cd63", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON>", "current_role": "Co-Founder", "current_company": "GoSkills.com", "location": "San Francisco, CA", "profile_pic": "/pictures/person/63065124-8b38-4db7-b078-0448d981cd63", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:20.527417+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+***********", "username": "+***********", "id": "+***********", "verified": false, "connected_at": "2025-04-03T23:26:20.527417"}, {"platform": "linkedin", "url": "https://linkedin.com/bhavneet466", "username": "bhavneet466", "id": "bhavneet466", "verified": false, "connected_at": "2025-04-03T23:26:20.527417"}, {"platform": "twitter", "url": "https://twitter.com/bhavneet313", "username": "bhavneet313", "id": "bhavneet313", "verified": false, "connected_at": "2025-04-03T23:26:20.527417"}, {"platform": "facebook", "url": "https://facebook.com/bhavneet845", "username": "bhavneet845", "id": "bhavneet845", "verified": false, "connected_at": "2025-04-03T23:26:20.527417"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+***********", "active_since": "2025-04-03T23:26:20.527417", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "5c6f5b3f-d22e-4300-b665-b7cca2d528d7", "title": "AMA with Founders", "start_time": "2025-08-29T09:16:18.157416Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON><PERSON><PERSON><PERSON> is the co-founder and CEO of GoSkills.com, an online platform offering bite-sized business courses. With a background in molecular biotechnology and a Master of Entrepreneurship from the University of Otago, she previously held roles at Groupon and Neon Stingray. <PERSON><PERSON> is committed to accessible education and supports aspiring female entrepreneurs through initiatives like the “Be the Boss” scholarship.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkArAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkArAAAAAAAAAA==/", "_etag": "\"e8002b5a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "ab274c4b-bc2a-4446-82ca-2c194c21e881", "name": "Sertaç", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "current_role": "Founder", "current_company": "Red Elephant Creative", "location": "Hong Kong", "profile_pic": "/pictures/person/ab274c4b-bc2a-4446-82ca-2c194c21e881", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:22.591872+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+15069224313", "username": "+15069224313", "id": "+15069224313", "verified": false, "connected_at": "2025-04-03T23:26:22.591872"}, {"platform": "facebook", "url": "https://facebook.com/sertaç623", "username": "sertaç623", "id": "sertaç623", "verified": false, "connected_at": "2025-04-03T23:26:22.591872"}, {"platform": "linkedin", "url": "https://linkedin.com/sertaç958", "username": "sertaç958", "id": "sertaç958", "verified": false, "connected_at": "2025-04-03T23:26:22.591872"}, {"platform": "twitter", "url": "https://twitter.com/sertaç377", "username": "sertaç377", "id": "sertaç377", "verified": false, "connected_at": "2025-04-03T23:26:22.591872"}], "emails": [{"type": "work", "address": "sertaç.mustafaoğ**************************", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "sertaç.mustafaoğ************", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+15069224313", "active_since": "2025-04-03T23:26:22.591872", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "1218d15a-37f3-43f5-a377-eeddf6ad970e", "title": "Community Meetup", "start_time": "2025-06-22T20:16:18.156203Z", "location": "Seattle"}, {"id": "000c3582-e686-4bdc-9dbd-8a9d4625a0e1", "title": "Coffee & Connect", "start_time": "2025-07-03T11:16:18.155734Z", "location": "Zoom"}, {"id": "fb073407-1ed5-4bdf-b06e-fa2adff4a5ce", "title": "Product Jam", "start_time": "2025-09-05T07:16:18.157315Z", "location": "Remote"}, {"id": "344ac7d1-9feb-4226-8f55-5c0c98d77c16", "title": "Demo Day", "start_time": "2025-09-06T07:16:18.156034Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON><PERSON> is the founder of Red Elephant Creative, a Hong Kong-based design consultancy specializing in human-centered branding, UX, and service design. With over 20 years of experience, he helps businesses create meaningful, people-focused experiences.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAsAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAsAAAAAAAAAA==/", "_etag": "\"e8002c5a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "c4556c5e-aace-49bf-af21-ad1e5e02cecd", "name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "current_role": "Engineer", "current_company": "Independent", "location": "Taipei, Taiwan", "profile_pic": "/pictures/person/c4556c5e-aace-49bf-af21-ad1e5e02cecd", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:21.552301+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+***********", "username": "+***********", "id": "+***********", "verified": false, "connected_at": "2025-04-03T23:26:21.552301"}, {"platform": "facebook", "url": "https://facebook.com/fedor733", "username": "fedor733", "id": "fedor733", "verified": false, "connected_at": "2025-04-03T23:26:21.552301"}, {"platform": "linkedin", "url": "https://linkedin.com/fedor937", "username": "fedor937", "id": "fedor937", "verified": false, "connected_at": "2025-04-03T23:26:21.552301"}], "emails": [{"type": "personal", "address": "fedor.k<PERSON><PERSON><PERSON>@gmail.com", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+***********", "active_since": "2025-04-03T23:26:21.552301", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "3ddae829-4215-406e-8aeb-e8183432f5ce", "title": "Pitch Practice", "start_time": "2025-07-09T14:16:18.155112Z", "location": "Seattle"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON> is a seasoned software engineer, architect, and team leader with over 14 years of experience in building scalable and cost-effective systems. Based in Hong Kong, he has held leadership roles at companies like Lalamove, AfterShip, and Privé Technologies, where he led engineering teams and delivered enterprise-grade solutions. <PERSON><PERSON> is also the creator of the open-source Node.js configuration tool “no-config” and is passionate about minimalism and software craftsmanship.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAtAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAtAAAAAAAAAA==/", "_etag": "\"e8002d5a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "b80f3bcd-7472-46b7-95c9-8918a723b937", "name": "Danial", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "current_role": "Engineering", "current_company": "Know Your People Inc.", "location": "Hong Kong", "profile_pic": "/pictures/person/b80f3bcd-7472-46b7-95c9-8918a723b937", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:20.511733+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+13241658046", "username": "+13241658046", "id": "+13241658046", "verified": false, "connected_at": "2025-04-03T23:26:20.511733"}, {"platform": "twitter", "url": "https://twitter.com/danial127", "username": "danial127", "id": "danial127", "verified": false, "connected_at": "2025-04-03T23:26:20.511733"}, {"platform": "facebook", "url": "https://facebook.com/danial773", "username": "danial773", "id": "danial773", "verified": false, "connected_at": "2025-04-03T23:26:20.511733"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "danial.ute<PERSON><PERSON>@gmail.com", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+13241658046", "active_since": "2025-04-03T23:26:20.511733", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "344ac7d1-9feb-4226-8f55-5c0c98d77c16", "title": "Demo Day", "start_time": "2025-09-06T07:16:18.156034Z", "location": "Zoom"}], "notes": null, "reminders": null, "achievements": [], "bio": "I’m from Kazakhstan. Currently studying in HKU for Data Science. Passionate about developing and designingthings.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAuAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAuAAAAAAAAAA==/", "_etag": "\"e8002e5a-0000-0700-0000-683a812e0000\"", "_attachments": "attachments/", "_ts": 1748664622}, {"id": "1b33976c-3082-4f7b-85bf-cac8740c14f7", "name": "<PERSON>", "last_name": "Greenhalgh", "current_role": "Co-Founder", "current_company": "Angelflow", "location": "Hong Kong", "profile_pic": "/pictures/person/1b33976c-3082-4f7b-85bf-cac8740c14f7", "invited_by_id": "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a", "member_since": "2025-04-03 23:26:21.567564+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+85293117219", "username": "+85293117219", "id": "+85293117219", "verified": false, "connected_at": "2025-04-03T23:26:21.567564"}, {"platform": "linkedin", "url": "https://linkedin.com/sean951", "username": "sean951", "id": "sean951", "verified": false, "connected_at": "2025-04-03T23:26:21.567564"}, {"platform": "facebook", "url": "https://facebook.com/sean723", "username": "sean723", "id": "sean723", "verified": false, "connected_at": "2025-04-03T23:26:21.567564"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85293117219", "active_since": "2025-04-03T23:26:21.567564", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "3ddae829-4215-406e-8aeb-e8183432f5ce", "title": "Pitch Practice", "start_time": "2025-07-09T14:16:18.155112Z", "location": "Seattle"}, {"id": "fb073407-1ed5-4bdf-b06e-fa2adff4a5ce", "title": "Product Jam", "start_time": "2025-09-05T07:16:18.157315Z", "location": "Remote"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is the co-founder and CEO of Angelflow, a Hong Kong-based platform that empowers investors to build and manage angel syndicates across Asia. A passionate technology evangelist and startup mentor, <PERSON> is dedicated to fostering innovation and collaboration within the startup ecosystem.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAvAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAvAAAAAAAAAA==/", "_etag": "\"e8002f5a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "3f068db9-0291-414d-8500-8242cd531cee", "name": "<PERSON>", "last_name": "<PERSON>", "current_role": "Product", "current_company": "Deepgram", "location": "Hong Kong", "profile_pic": "/pictures/person/3f068db9-0291-414d-8500-8242cd531cee", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-03 23:26:21.560229+00:00", "social_links": [{"platform": "whatsapp", "url": "https://whatsapp.com/+85223344597", "username": "+85223344597", "id": "+85223344597", "verified": false, "connected_at": "2025-04-03T23:26:21.560229"}, {"platform": "linkedin", "url": "https://linkedin.com/michelle812", "username": "michelle812", "id": "michelle812", "verified": false, "connected_at": "2025-04-03T23:26:21.560229"}, {"platform": "twitter", "url": "https://twitter.com/michelle746", "username": "michelle746", "id": "michelle746", "verified": false, "connected_at": "2025-04-03T23:26:21.560229"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2022-01-01T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2020-01-01T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85223344597", "active_since": "2025-04-03T23:26:21.560229", "deactivated_at": null, "verified": false}], "upcoming_events": [{"id": "3ddae829-4215-406e-8aeb-e8183432f5ce", "title": "Pitch Practice", "start_time": "2025-07-09T14:16:18.155112Z", "location": "Seattle"}], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is a Senior Product Manager at Deepgram, where she leads the development of Aura, the company’s advanced text-to-speech product. With prior experience in product roles at SCMP (Alibaba), design at Amazon and 9GAG, and as the founder of Weava Tools, <PERSON> brings a diverse background to her work in voice AI.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAwAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAwAAAAAAAAAA==/", "_etag": "\"e800305a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "a1b2c3d4-e5f6-4a5b-8c7d-9e0f1a2b3c4d", "name": "Nancy", "last_name": "Lokos", "current_role": "Artist", "current_company": "Independent", "location": "Hong Kong", "profile_pic": "/pictures/person/a1b2c3d4-e5f6-4a5b-8c7d-9e0f1a2b3c4d", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-08 00:00:00+00:00", "social_links": [{"platform": "instagram", "url": "https://instagram.com/nancylokos", "username": "nancy<PERSON><PERSON>", "id": "nancy<PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "twitter", "url": "https://twitter.com/nancylokos", "username": "nancy<PERSON><PERSON>", "id": "nancy<PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}], "emails": [{"type": "personal", "address": "<EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+14155552671", "active_since": "2025-04-08T00:00:00", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [{"name": "The First", "color": "#FFD700", "comment": "The first Founding Member"}], "bio": "<PERSON> is a Hong Kong-based design consultant and artist. She is the founder of N52 Design Ltd and previously served as Head of Design and Experience at Coherent. <PERSON> is known for transforming complex systems into intuitive user experiences and, under the name <PERSON><PERSON>, explores themes of order and chaos through abstract art.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAxAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAxAAAAAAAAAA==/", "_etag": "\"e800315a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "b2c3d4e5-f6a7-5b6c-9d8e-0f1a2b3c4d5e", "name": "<PERSON>", "last_name": "<PERSON>", "current_role": "Podcaster", "current_company": "Independent", "location": "Hong Kong", "profile_pic": "/pictures/person/b2c3d4e5-f6a7-5b6c-9d8e-0f1a2b3c4d5e", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-08 00:00:00+00:00", "social_links": [{"platform": "twitter", "url": "https://twitter.com/ericbyron", "username": "<PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "instagram", "url": "https://instagram.com/ericbyron", "username": "<PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "linkedin", "url": "https://linkedin.com/ericbyron", "username": "<PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}], "emails": [{"type": "personal", "address": "<EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+12135551234", "active_since": "2025-04-08T00:00:00", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is a Hong Kong-based podcaster and educator. He hosts The Education Innovators Podcast, spotlighting global stories of educational innovation, and co-hosts <PERSON> Harm In Asking, analyzing what makes podcasts succeed. He also shares insights into podcasting through his Substack newsletter, A Podcasting Journal.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAyAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAyAAAAAAAAAA==/", "_etag": "\"e800325a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "c3d4e5f6-a7b8-6c7d-9e0f-1a2b3c4d5e6f", "name": "<PERSON>", "last_name": "<PERSON><PERSON>", "current_role": "Motivational Speaker", "current_company": "Independent", "location": "Hong Kong", "profile_pic": "/pictures/person/c3d4e5f6-a7b8-6c7d-9e0f-1a2b3c4d5e6f", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-08 00:00:00+00:00", "social_links": [{"platform": "linkedin", "url": "https://linkedin.com/aaronpang", "username": "aaron<PERSON>g", "id": "aaron<PERSON>g", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "twitter", "url": "https://twitter.com/aaronpang", "username": "aaron<PERSON>g", "id": "aaron<PERSON>g", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "instagram", "url": "https://instagram.com/aaronpang", "username": "aaron<PERSON>g", "id": "aaron<PERSON>g", "verified": false, "connected_at": "2025-04-08T00:00:00"}], "emails": [{"type": "personal", "address": "<EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85291234567", "active_since": "2025-04-08T00:00:00", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is a Hong Kong-based motivational speaker, author, and host of the award-winning Catch Your Purpose podcast. As the founder of Transformative Purpose, he empowers individuals to build purposeful lives through self-leadership and resilience. His books, including <PERSON>st<PERSON> and Shh…Give Me a Moment, focus on mindset and personal growth.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkAzAAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkAzAAAAAAAAAA==/", "_etag": "\"e800335a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "d4e5f6a7-b8c9-7d8e-9f0a-1b2c3d4e5f6a", "name": "<PERSON>", "last_name": "<PERSON>", "current_role": "Poet of Tech", "current_company": "Independent", "location": "Washington DC", "profile_pic": "/pictures/person/d4e5f6a7-b8c9-7d8e-9f0a-1b2c3d4e5f6a", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-08 00:00:00+00:00", "social_links": [{"platform": "linkedin", "url": "https://linkedin.com/antoinerjwright", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "twitter", "url": "https://twitter.com/antoinerjwright", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "instagram", "url": "https://instagram.com/antoinerjwright", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}], "emails": [{"type": "personal", "address": "<EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+12025550123", "active_since": "2025-04-08T00:00:00", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is a Washington, D.C.-based product and process design expert, futurist, and founder of Avanceé, a strategic design consultancy. He specializes in aligning technology, strategy, and human potential to create innovative solutions. A passionate advocate for multimodal transportation and digital ethics, <PERSON> shares his insights through writing and speaking engagements.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkA0AAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkA0AAAAAAAAAA==/", "_etag": "\"e800345a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "e5f6a7b8-c9d0-8e1f-2a3b-4c5d6e7f8a9b", "name": "<PERSON>", "last_name": "<PERSON>", "current_role": "Founder", "current_company": "MoveMen!", "location": "Hong Kong", "profile_pic": "/pictures/person/e5f6a7b8-c9d0-8e1f-2a3b-4c5d6e7f8a9b", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-08 00:00:00+00:00", "social_links": [{"platform": "linkedin", "url": "https://linkedin.com/brian<PERSON>erson", "username": "<PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "twitter", "url": "https://twitter.com/brian<PERSON>erson", "username": "<PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "instagram", "url": "https://instagram.com/brian<PERSON>erson", "username": "<PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}], "emails": [{"type": "work", "address": "brian.he<PERSON><PERSON>@movemen.com", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "brian.he<PERSON><EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+***********", "active_since": "2025-04-08T00:00:00", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON> is the founder of MoveMEN!, a Hong Kong-based community that provides safe, supportive spaces for men to explore mindfulness, emotional wellbeing, and authentic connection. A yoga and meditation teacher, <PERSON> also leads Whole Business Wellness, helping organizations build inclusive, high-performing cultures through psychological safety and mental health initiatives.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkA1AAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkA1AAAAAAAAAA==/", "_etag": "\"e800355a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "f6a7b8c9-d0e1-8f2a-3b4c-5d6e7f8a9b0c", "name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "<PERSON>", "current_role": "CX Professional", "current_company": "Independent", "location": "Hong Kong", "profile_pic": "/pictures/person/f6a7b8c9-d0e1-8f2a-3b4c-5d6e7f8a9b0c", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-08 00:00:00+00:00", "social_links": [{"platform": "linkedin", "url": "https://linkedin.com/cristianeross", "username": "crist<PERSON><PERSON><PERSON>", "id": "crist<PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "twitter", "url": "https://twitter.com/cristianeross", "username": "crist<PERSON><PERSON><PERSON>", "id": "crist<PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "instagram", "url": "https://instagram.com/cristianeross", "username": "crist<PERSON><PERSON><PERSON>", "id": "crist<PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}], "emails": [{"type": "personal", "address": "<EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+85291234569", "active_since": "2025-04-08T00:00:00", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON><PERSON><PERSON> is the Director of Customer Experience at PURE Group International in Hong Kong. With over 20 years of global experience, she leads client engagement, corporate sales, and sustainability initiatives across the wellness and hospitality sectors. <PERSON><PERSON><PERSON><PERSON> is recognized for designing impactful omnichannel strategies and fostering meaningful customer relationships.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkA2AAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkA2AAAAAAAAAA==/", "_etag": "\"e800365a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}, {"id": "00000000-0000-0000-2345-ca88cdc0f927", "name": "<PERSON><PERSON>", "last_name": "<PERSON>", "current_role": "Founder", "current_company": "Generative Beings", "location": "Singapore", "profile_pic": "/pictures/person/00000000-0000-0000-2345-ca88cdc0f927", "invited_by_id": "661dd3e3-9aab-4554-9883-dfdd02fb42da", "member_since": "2025-04-08 00:00:00+00:00", "social_links": [{"platform": "linkedin", "url": "https://linkedin.com/shivang<PERSON>pta", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "twitter", "url": "https://twitter.com/shivanggupta", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}, {"platform": "instagram", "url": "https://instagram.com/shivanggupta", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verified": false, "connected_at": "2025-04-08T00:00:00"}], "emails": [{"type": "work", "address": "<EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}, {"type": "personal", "address": "<EMAIL>", "active_since": "2025-04-08T00:00:00Z", "deactivated_at": null, "verified": false}], "phone_numbers": [{"type": "mobile", "number": "+6591234567", "active_since": "2025-04-08T00:00:00", "deactivated_at": null, "verified": false}], "upcoming_events": [], "notes": null, "reminders": null, "achievements": [], "bio": "<PERSON><PERSON> is the founder of The Generative Beings, Asia’s largest community of over 4,000 generative AI founders, builders, and investors. Based in Singapore, he leads initiatives such as hackathons, masterclasses, and networking events to foster collaboration and innovation in the AI ecosystem.", "profile_completed": false, "has_passkey": false, "primary_identifier_type": "", "primary_identifier_value": "", "connection_status": null, "connection_requestee": null, "_rid": "4V92ALR1vkA3AAAAAAAAAA==", "_self": "dbs/4V92AA==/colls/4V92ALR1vkA=/docs/4V92ALR1vkA3AAAAAAAAAA==/", "_etag": "\"e800375a-0000-0700-0000-683a812f0000\"", "_attachments": "attachments/", "_ts": **********}]