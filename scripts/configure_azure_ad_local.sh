#!/bin/bash
# Script to configure Azure AD SSO for local development

echo "🔑 Configuring Azure AD SSO settings for local development..."

# Define local environment variables
ENV="local"
RESOURCE_GROUP="peepsapp-${ENV}-rg"
KEY_VAULT_NAME="peepsapp-vault-${ENV}"
AZURE_AD_APP_NAME="Peeps App SSO Local"
AZURE_AD_REDIRECT_URIS="https://local.peepsapp.ai:8443/auth/azure-ad/callback"
AZURE_AD_LOGOUT_URL="https://local.peepsapp.ai:8443/auth/logout"

# Check if resource group exists, create if it doesn't
echo "🔍 Checking resource group..."
if ! az group show --name "$RESOURCE_GROUP" --query name -o tsv &>/dev/null; then
    echo "⚙️ Creating resource group $RESOURCE_GROUP..."
    az group create --name "$RESOURCE_GROUP" --location westus --output none
    echo "⏳ Waiting for resource group creation to propagate..."
    sleep 10
    if ! az group show --name "$RESOURCE_GROUP" --query name -o tsv &>/dev/null; then
        echo "❌ Resource group creation failed or has not propagated yet."
        exit 1
    fi
    echo "✅ Resource group created successfully."
else
    echo "✅ Resource group $RESOURCE_GROUP already exists."
fi

# Check if key vault exists, create if it doesn't
echo "🔍 Checking key vault..."
if ! az keyvault show --name "$KEY_VAULT_NAME" --query name -o tsv &>/dev/null; then
    echo "⚙️ Creating key vault $KEY_VAULT_NAME..."
    az keyvault create --name "$KEY_VAULT_NAME" --resource-group "$RESOURCE_GROUP" --location westus --output none
    echo "⏳ Waiting for key vault creation to propagate..."
    sleep 10
    if ! az keyvault show --name "$KEY_VAULT_NAME" --query name -o tsv &>/dev/null; then
        echo "❌ Key vault creation failed or has not propagated yet."
        exit 1
    fi
    echo "✅ Key vault created successfully."
else
    echo "✅ Key vault $KEY_VAULT_NAME already exists."
fi

# Source the utility functions
. scripts/azure_utils.sh

# Set up Key Vault access for current user
echo "🔐 Setting up Key Vault access..."
USER_OBJECT_ID=$(az ad signed-in-user show --query id -o tsv)

create_role_assignment \
    "$USER_OBJECT_ID" \
    "Key Vault Secrets Officer" \
    "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$KEY_VAULT_NAME" \
    "User access to Key Vault: $KEY_VAULT_NAME"

wait_for_role_propagation \
    "$USER_OBJECT_ID" \
    "Key Vault Secrets Officer" \
    "/subscriptions/$(az account show --query id -o tsv)/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.KeyVault/vaults/$KEY_VAULT_NAME" \
    5 30

echo "📝 Creating/updating Azure AD application: $AZURE_AD_APP_NAME"

# Check if the app already exists
EXISTING_APP_ID=$(az ad app list --display-name "$AZURE_AD_APP_NAME" --query "[0].appId" -o tsv 2>/dev/null)

if [ -n "$EXISTING_APP_ID" ]; then
    echo "🔄 Updating existing Azure AD application..."
    AZURE_AD_APP_ID="$EXISTING_APP_ID"

    # Update the existing app
    az ad app update \
        --id "$AZURE_AD_APP_ID" \
        --web-redirect-uris $AZURE_AD_REDIRECT_URIS \
        --output none || echo "⚠️ Failed to update redirect URIs"

    if [ -n "$AZURE_AD_LOGOUT_URL" ]; then
        set_azure_ad_logout_url "$AZURE_AD_APP_ID" "$AZURE_AD_LOGOUT_URL" "https://local.peepsapp.ai:8443"
    fi
else
    echo "🆕 Creating new Azure AD application..."

    # Create a new app
    AZURE_AD_APP_ID=$(az ad app create \
        --display-name "$AZURE_AD_APP_NAME" \
        --sign-in-audience AzureADMyOrg \
        --web-redirect-uris $AZURE_AD_REDIRECT_URIS \
        --enable-id-token-issuance true \
        --query appId -o tsv)

    if [ -z "$AZURE_AD_APP_ID" ]; then
        echo "❌ Failed to create Azure AD application."
        exit 1
    else
        if [ -n "$AZURE_AD_LOGOUT_URL" ]; then
            set_azure_ad_logout_url "$AZURE_AD_APP_ID" "$AZURE_AD_LOGOUT_URL" "https://local.peepsapp.ai:8443"
        fi

        # Create a service principal for the app
        az ad sp create --id "$AZURE_AD_APP_ID" --output none || echo "⚠️ Failed to create service principal"

        echo "⏳ Waiting for Azure AD application to propagate..."
        sleep 30
    fi
fi

if [ -n "$AZURE_AD_APP_ID" ]; then
    echo "📝 Creating and setting Azure AD client secret..."
    # Generate a client secret
    CLIENT_SECRET=$(openssl rand -base64 32)

    # Get the tenant ID
    TENANT_ID=$(az account show --query tenantId -o tsv)

    # Create a new password credential for the application
    echo "📝 Setting client secret in Azure AD application..."
    RESULT=$(az ad app credential reset --id "$AZURE_AD_APP_ID" --append --query password -o tsv)

    if [ $? -eq 0 ] && [ -n "$RESULT" ]; then
        CLIENT_SECRET="$RESULT"
        echo "✅ Successfully set client secret in Azure AD application"
    else
        echo "⚠️ Failed to set client secret in Azure AD application. Using generated secret."
    fi

    # Store secrets in Key Vault
    echo "📝 Storing Azure AD secrets in Key Vault..."
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-TENANT-ID" "$TENANT_ID"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-CLIENT-ID" "$AZURE_AD_APP_ID"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-CLIENT-SECRET" "$CLIENT_SECRET"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-REDIRECT-URI" "https://local.peepsapp.ai:8443/auth/azure-ad/callback"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-SCOPES" "User.Read email profile"

    # We're not storing values in .env file anymore, only in Key Vault
    echo "📝 Azure AD configuration stored in Key Vault only"

    echo "✅ Azure AD SSO configuration for local development completed."
    echo ""
    echo "📋 Azure AD Configuration Summary:"
    echo "  - Application Name: $AZURE_AD_APP_NAME"
    echo "  - Application ID: $AZURE_AD_APP_ID"
    echo "  - Tenant ID: $TENANT_ID"
    echo "  - Redirect URI: $AZURE_AD_REDIRECT_URIS"
    echo "  - Logout URL: $AZURE_AD_LOGOUT_URL"
    echo ""
    echo "🔐 Configuration has been stored in Key Vault $KEY_VAULT_NAME"
    echo "🚀 You can now run 'make start-https' to start the application with Azure AD SSO"
    echo "💡 To use these secrets, retrieve them directly from Key Vault using Azure CLI or SDK"
else
    echo "❌ Failed to create or update Azure AD application."
    exit 1
fi
