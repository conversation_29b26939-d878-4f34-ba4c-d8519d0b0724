#!/bin/bash
# Script to configure Azure AD SSO

# Check if environment is provided
if [ -z "$1" ]; then
    echo "❌ Environment not specified. Usage: $0 <environment>"
    exit 1
fi

ENV=$1
RESOURCE_GROUP="peepsapp-${ENV}-rg"
KEY_VAULT_NAME="peepsapp-vault-${ENV}"

# Source the utility functions
. scripts/azure_utils.sh

echo "🔑 Configuring Azure AD SSO settings for environment: ${ENV}..."

# Check if deployment is enabled
deployAzureAdSso=$(jq -r '.parameters.deployAzureAdSso.value' Infra/main-parameters-${ENV}.json 2>/dev/null || echo "true")

if [ "$deployAzureAdSso" != "true" ]; then
    echo "⏭️ Azure AD SSO deployment is disabled. Skipping configuration."
    exit 0
fi

# Get parameters from the parameter file
AZURE_AD_APP_NAME=$(jq -r '.parameters.azureAdAppName.value' Infra/main-parameters-${ENV}.json)
AZURE_AD_REDIRECT_URIS=$(jq -r '.parameters.azureAdRedirectUris.value | join(" ")' Infra/main-parameters-${ENV}.json)
AZURE_AD_LOGOUT_URL=$(jq -r '.parameters.azureAdLogoutUrl.value' Infra/main-parameters-${ENV}.json)

echo "📝 Creating/updating Azure AD application: $AZURE_AD_APP_NAME"

# Check if the app already exists
EXISTING_APP_ID=$(az ad app list --display-name "$AZURE_AD_APP_NAME" --query "[0].appId" -o tsv 2>/dev/null)

if [ -n "$EXISTING_APP_ID" ]; then
    echo "🔄 Updating existing Azure AD application..."
    AZURE_AD_APP_ID="$EXISTING_APP_ID"

    # Update the existing app
    az ad app update \
        --id "$AZURE_AD_APP_ID" \
        --web-redirect-uris $AZURE_AD_REDIRECT_URIS \
        --output none || echo "⚠️ Failed to update redirect URIs"

    if [ -n "$AZURE_AD_LOGOUT_URL" ]; then
        set_azure_ad_logout_url "$AZURE_AD_APP_ID" "$AZURE_AD_LOGOUT_URL" "https://${ENV}.peepsapp.ai"
    fi
else
    echo "🆕 Creating new Azure AD application..."

    # Create a new app
    AZURE_AD_APP_ID=$(az ad app create \
        --display-name "$AZURE_AD_APP_NAME" \
        --sign-in-audience AzureADMyOrg \
        --web-redirect-uris $AZURE_AD_REDIRECT_URIS \
        --enable-id-token-issuance true \
        --query appId -o tsv)

    if [ -z "$AZURE_AD_APP_ID" ]; then
        echo "❌ Failed to create Azure AD application."
        exit 1
    else
        if [ -n "$AZURE_AD_LOGOUT_URL" ]; then
            set_azure_ad_logout_url "$AZURE_AD_APP_ID" "$AZURE_AD_LOGOUT_URL" "https://${ENV}.peepsapp.ai"
        fi

        # Create a service principal for the app
        az ad sp create --id "$AZURE_AD_APP_ID" --output none || echo "⚠️ Failed to create service principal"

        echo "⏳ Waiting for Azure AD application to propagate..."
        sleep 30
    fi
fi

if [ -n "$AZURE_AD_APP_ID" ]; then
    echo "📝 Creating and setting Azure AD client secret..."
    # Get the tenant ID
    TENANT_ID=$(az account show --query tenantId -o tsv)

    # Create a new password credential for the application
    echo "📝 Setting client secret in Azure AD application..."
    RESULT=$(az ad app credential reset --id "$AZURE_AD_APP_ID" --append --query password -o tsv)
    CLIENT_SECRET="$RESULT"

    if [ $? -eq 0 ] && [ -n "$RESULT" ]; then
        echo "✅ Successfully set client secret in Azure AD application"
    else
        echo "⚠️ Failed to set client secret in Azure AD application. Using generated secret."
        CLIENT_SECRET=$(openssl rand -base64 32)
    fi

    # Store secrets in Key Vault
    echo "📝 Storing Azure AD secrets in Key Vault..."
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-CLIENT-ID" "$AZURE_AD_APP_ID"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-TENANT-ID" "$TENANT_ID"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-CLIENT-SECRET" "$CLIENT_SECRET"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-REDIRECT-URI" "https://${ENV}.peepsapp.ai/auth/azure-ad/callback"
    set_keyvault_secret "$KEY_VAULT_NAME" "AZURE-AD-SCOPES" "User.Read email profile"

    echo "✅ Azure AD SSO configuration completed."
else
    echo "❌ Failed to create or update Azure AD application."
    exit 1
fi
