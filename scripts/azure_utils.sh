#!/bin/bash
# Azure utility functions for infrastructure deployment and configuration

# Set a secret in Key Vault with proper error handling and human-readable output
# Usage: set_keyvault_secret <vault_name> <secret_name> <secret_value>
set_keyvault_secret() {
    local vault_name="$1"
    local secret_name="$2"
    local secret_value="$3"

    echo "🔑 Creating Secret: $secret_name in Vault: $vault_name"
    RESULT=$(az keyvault secret set --vault-name "$vault_name" --name "$secret_name" --value "$secret_value" 2>&1)
    if [ $? -ne 0 ]; then
        echo "❌ Error creating Secret: $secret_name"
        echo "Error details: $RESULT"
        return 1
    else
        echo "✅ Secret: $secret_name created successfully in Vault: $vault_name"
        return 0
    fi
}

# Create a role assignment with proper error handling and human-readable output
# Usage: create_role_assignment <principal_id> <role_name> <scope> [description]
create_role_assignment() {
    local principal_id="$1"
    local role_name="$2"
    local scope="$3"
    local description="${4:-Role assignment}"

    echo "🔑 Creating Role Assignment: $role_name for Principal: $principal_id"
    echo "    $description"
    RESULT=$(az role assignment create \
        --assignee "$principal_id" \
        --role "$role_name" \
        --scope "$scope" 2>&1)
    if [ $? -ne 0 ]; then
        echo "❌ Error creating Role Assignment for $role_name"
        echo "Error details: $RESULT"
        return 1
    else
        echo "✅ Role Assignment created successfully. Principal now has $role_name access."
        return 0
    fi
}

# Wait for role assignment to propagate by checking its existence
# Usage: wait_for_role_propagation <principal_id> <role_name> <scope> [max_retries] [retry_interval_seconds]
wait_for_role_propagation() {
    local principal_id="$1"
    local role_name="$2"
    local scope="$3"
    local max_retries="${4:-5}"
    local retry_interval="${5:-30}"

    echo "⏳ Waiting for role assignment to propagate (initial $retry_interval seconds)..."
    sleep $retry_interval
    echo "🔍 Verifying role assignment propagation..."

    local retry_count=0
    local propagated=false

    while [ $retry_count -lt $max_retries ] && [ "$propagated" != "true" ]; do
        # Query role assignments to check if our permission has propagated
        ROLE_CHECK=$(az role assignment list \
            --assignee "$principal_id" \
            --scope "$scope" \
            --query "[?roleDefinitionName=='$role_name'].roleDefinitionName" -o tsv 2>&1)

        if [ "$ROLE_CHECK" == "$role_name" ]; then
            echo "✅ Role assignment has propagated. Access verified."
            propagated=true
            return 0
        else
            retry_count=$((retry_count+1))
            if [ $retry_count -lt $max_retries ]; then
                echo "⏳ Role assignment still propagating. Waiting $retry_interval seconds... (Attempt $retry_count of $max_retries)"
                sleep $retry_interval
            else
                echo "⚠️ Role assignment verification timed out after $max_retries attempts."
                echo "⚠️ Proceeding anyway, but some operations might fail."
                return 1
            fi
        fi
    done
}

# Set Azure AD application logout URL with fallback mechanisms
# Usage: set_azure_ad_logout_url <app_id> <logout_url> <home_page_url>
set_azure_ad_logout_url() {
    local app_id="$1"
    local logout_url="$2"
    local home_page_url="$3"

    echo "Setting logout URL to: $logout_url"

    RESULT=$(az ad app update \
        --id "$app_id" \
        --set "web={\"logoutUrl\":\"$logout_url\",\"homePageUrl\":\"$home_page_url\"}" 2>&1)

    if [ $? -eq 0 ]; then
            echo "✅ Successfully set logout URL using JSON approach"
        return 0
    else
        echo "⚠️ Failed to set logout URL using both methods"
        echo "Error details: $RESULT"
        return 1
    fi
}

# Reset Azure AD application credentials and return the new secret
# Usage: reset_azure_ad_credentials <app_id>
reset_azure_ad_credentials() {
    local app_id="$1"
    local client_secret=""

    echo "🔑 Resetting credentials for Azure AD App: $app_id"
    RESET_RESULT=$(az ad app credential reset --id "$app_id" 2>&1)

    if [ $? -ne 0 ]; then
        echo "❌ Error resetting client secret in Azure AD application"
        echo "Error details: $RESET_RESULT"
        return 1
    else
        echo "✅ Successfully reset credentials for Azure AD App: $app_id"

        # Check if the result is valid JSON
        if echo "$RESET_RESULT" | jq . >/dev/null 2>&1; then
            # Extract the password from the result
            client_secret=$(echo "$RESET_RESULT" | jq -r '.password')
            if [ -n "$client_secret" ] && [ "$client_secret" != "null" ]; then
                echo "$client_secret"
                return 0
            else
                echo "⚠️ Could not extract password from JSON response"
                echo "Response: $RESET_RESULT"
                return 1
            fi
        else
            echo "⚠️ Response is not valid JSON"
            echo "Response: $RESET_RESULT"
            return 1
        fi
    fi
}
