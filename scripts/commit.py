#!/usr/bin/env python3
"""Interactive commit script for PeepsAPI.

This script runs all checks and then guides the user through the commit process.
It performs the following steps:
1. Runs code formatters (black, isort)
2. Runs linting (flake8)
3. Runs tests (pytest)
4. Stages changes
5. Guides the user through creating a standardized commit message
6. Commits the changes
7. Optionally pushes the changes
"""
import subprocess
import sys
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(
            command, shell=True, check=check, capture_output=True, text=True
        )
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(e.stderr)
        sys.exit(1)


def main():
    """Execute the interactive commit process.

    This function runs code quality checks, stages changes, and guides the user
    through creating a standardized commit message following conventional commits format.
    It also handles pre-commit hooks and optionally pushes the changes.
    """
    print("🔍 Running code quality checks...")

    # Run formatting
    print("\n✨ Running code formatters...")
    run_command("black .")
    run_command("isort .")

    # Check if formatting made any changes
    format_status = run_command("git diff --name-only", check=False)
    if format_status.stdout.strip():
        print("\n⚠️ Formatting made changes to the following files:")
        print(format_status.stdout)
        print("These changes will be included in the commit.")

    # Run linting
    print("\n🔍 Running linting...")
    lint_result = run_command("flake8", check=False)
    if lint_result.returncode != 0:
        print("\n❌ Linting failed. Please fix the issues and try again.")
        print(lint_result.stdout)
        sys.exit(1)

    # Run tests
    print("\n⚙ Setting up tests...")
    run_command("pip uninstall peepsapi -y")
    run_command("pip install -e .")
    print("\n🧪 Running tests...")
    test_result = run_command("pytest", check=False)
    if test_result.returncode != 0:
        # Check if the error is just that no tests were found
        if "no tests ran" in test_result.stdout:
            print("\n⚠️ No tests found. Continuing anyway...")
        else:
            print("\n❌ Tests failed. Please fix the issues and try again.")
            print(test_result.stdout)
            sys.exit(1)

    # All checks passed, stage changes
    print("\n✅ All checks passed!")
    # Check if any changes are already staged
    staged_diff = run_command("git diff --cached --name-only", check=False)

    if not staged_diff.stdout.strip():
        print("\n📥 No staged changes detected. Staging all changes...")
        run_command("git add .")
    else:
        print("\n📦 Detected staged changes. Skipping automatic staging.")

    # Check if there are any changes to commit
    status_result = run_command("git status --porcelain", check=False)
    if not status_result.stdout.strip():
        print("\n⚠️ No changes to commit. Exiting.")
        sys.exit(0)

    # Check for untracked files
    untracked_files = run_command(
        "git ls-files --others --exclude-standard", check=False
    )
    if untracked_files.stdout.strip():
        print("\n⚠️ The following files are not tracked by git:")
        print(untracked_files.stdout)
        add_untracked = input(
            "Do you want to add these files to the commit? (y/n): "
        ).lower()
        if add_untracked == "y":
            print("Adding untracked files...")
            run_command("git add --all")

    # Get commit type
    print("\n📝 Select commit type:")
    commit_types = [
        "feat: A new feature",
        "fix: A bug fix",
        "docs: Documentation changes",
        "style: Code style changes (formatting, etc.)",
        "refactor: Code refactoring",
        "test: Adding or updating tests",
        "chore: Maintenance tasks",
        "infra: Infrastructure tasks",
    ]

    for i, commit_type in enumerate(commit_types, 1):
        print(f"{i}. {commit_type}")

    while True:
        try:
            choice = int(input("\nEnter number (1-8): "))
            if 1 <= choice <= 8:
                break
            print("Please enter a number between 1 and 8.")
        except ValueError:
            print("Please enter a valid number.")

    # Get the prefix (e.g., "feat: ", "fix: ")
    prefix = commit_types[choice - 1].split(":")[0] + ": "

    # Get commit message
    while True:
        message = input("\nEnter commit message (without prefix): ")
        if message.strip():
            break
        print("Commit message cannot be empty. Please try again.")

    full_message = prefix + message

    # Commit changes
    print(f"\n🚀 Committing with message: {full_message}")

    # Check if pre-commit hooks are installed
    pre_commit_config = Path(".pre-commit-config.yaml")
    print("\n⚠️ Pre-commit hooks are installed. They will run automatically.")
    print("If they fail, you may need to fix the issues and try again.")
    commit_cmd = f'git commit -m "{full_message}"'

    commit_result = run_command(commit_cmd, check=False)
    if commit_result.returncode != 0:
        print("\n❌ Commit failed. Please fix the issues and try again.")
        print(commit_result.stderr)

        # Check if it's a pre-commit hook failure
        if "hook" in commit_result.stderr.lower():
            print("\n⚠️ Pre-commit hooks failed. You can:")
            print("1. Fix the issues and try again")
            print(
                "2. Run 'make commit' with the --no-verify flag to bypass hooks (not recommended)"
            )
            print("3. Temporarily disable specific hooks in .pre-commit-config.yaml")

        sys.exit(1)

    # Verify the commit was successful
    print("\n✅ Changes committed successfully!")
    print("\n📝 Commit details:")
    last_commit = run_command("git log -1 --oneline")
    print(last_commit.stdout)

    # Ask if the user wants to push the changes
    push_changes = input("\nDo you want to push the changes? (y/n): ").lower()
    if push_changes == "y":
        current_branch = run_command("git branch --show-current").stdout.strip()
        print(f"\n🔜 Pushing changes to {current_branch}...")
        push_result = run_command(f"git push origin {current_branch}", check=False)
        if push_result.returncode != 0:
            print("\n❌ Push failed. Please fix the issues and try again.")
            print(push_result.stderr)
            print("\nYou can push manually later with:")
            print(f"  git push origin {current_branch}")
        else:
            print("\n✅ Changes pushed successfully!")


if __name__ == "__main__":
    main()
