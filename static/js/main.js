/**
 * Main entry point for the accounts page
 * Imports all necessary modules and initializes the page
 */

import { initializeUI, showSection, updateSidebarVisibility } from './ui.js';
import { checkWebAuthnSupport } from './webauthn.js';
import {
    toggleIdentifierInput,
    toggleRecoveryIdentifierInput,
    toggleLoginIdentifierInput
} from './forms.js';
import { checkExistingIdentity } from './identity.js';
import { INVITE_CONSTANTS } from './constants.js';

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize UI components
    initializeUI();

    // Check WebAuthn support
    checkWebAuthnSupport();

    // Initialize the identifier type selectors
    toggleIdentifierInput();
    toggleRecoveryIdentifierInput();
    toggleLoginIdentifierInput();

    // Set dynamic values from constants
    const inviteExpiresInput = document.getElementById('invite-expires');
    if (inviteExpiresInput) {
        inviteExpiresInput.value = INVITE_CONSTANTS.DEFAULT_EXPIRATION_DAYS;
        inviteExpiresInput.max = INVITE_CONSTANTS.MAX_EXPIRATION_DAYS;
    }

    // Check for existing identity
    checkExistingIdentity();

    // First, hide all content sections to ensure a clean state
    const contentSections = document.querySelectorAll('.content-section');
    contentSections.forEach(section => {
        if (section.id !== 'peep-identity-section') {
            section.classList.add('hidden');
        }
    });

    // Make sure Peep Identity section is visible
    document.getElementById('peep-identity-section').classList.remove('hidden');

    // Set the Peep Identity nav item as active
    const peepIdentityNav = document.getElementById('peep-identity-nav');
    if (peepIdentityNav) {
        const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
        sidebarLinks.forEach(link => link.classList.remove('active'));
        peepIdentityNav.classList.add('active');
    }

    // Show/hide sections based on URL parameters (this will override the default if a section is specified)
    const urlParams = new URLSearchParams(window.location.search);
    const section = urlParams.get('section');
    if (section) {
        showSection(section);
    }
});
