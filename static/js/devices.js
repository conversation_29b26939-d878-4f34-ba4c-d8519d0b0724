/**
 * Device management module
 * Handles device listing, selection, and management
 */

import { makeApiRequest } from './api.js';
import { formatDate } from './utils.js';
import { API_ENDPOINTS } from './constants.js';

// Global variable to store the currently selected device
let selectedDevice = null;

/**
 * Function to fetch devices for the current person
 * @returns {Promise<void>}
 */
export async function fetchDevices() {
    // Check if a person is selected
    const personId = document.getElementById('selected-person-id').value;
    if (!personId) {
        // Show warning and hide content
        document.getElementById('device-management-warning').classList.remove('hidden');
        document.getElementById('device-management-content').classList.add('hidden');
        return;
    }

    // Hide warning and show content
    document.getElementById('device-management-warning').classList.add('hidden');
    document.getElementById('device-management-content').classList.remove('hidden');

    // Show loading indicator
    document.getElementById('devices-loading').classList.remove('hidden');
    document.getElementById('devices-list-container').classList.add('hidden');
    document.getElementById('device-details-container').classList.add('hidden');

    try {
        // Fetch devices from the API
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_DEVICES_LIST,
            'GET',
            null,
            null,
            null
        );

        // Hide loading indicator
        document.getElementById('devices-loading').classList.add('hidden');

        // Process and display devices
        displayDevices(data.devices);
    } catch (error) {
        // Hide loading indicator
        document.getElementById('devices-loading').classList.add('hidden');

        // Show error message
        alert('Error fetching devices: ' + error.message);
    }
}

/**
 * Function to display devices in a table
 * @param {Array} devices - Array of device objects
 */
export function displayDevices(devices) {
    const devicesTableContainer = document.getElementById('devices-table-container');
    const noDevicesMessage = document.getElementById('no-devices-message');

    // Clear any existing content
    devicesTableContainer.innerHTML = '';

    // Show/hide no devices message
    if (!devices || devices.length === 0) {
        noDevicesMessage.classList.remove('hidden');
        document.getElementById('devices-list-container').classList.remove('hidden');
        return;
    }

    // Hide no devices message and show container
    noDevicesMessage.classList.add('hidden');
    document.getElementById('devices-list-container').classList.remove('hidden');

    // Create table
    const table = document.createElement('table');
    table.className = 'device-table';

    // Create table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Device Name</th>
            <th>Created</th>
            <th>Last Used</th>
            <th>Status</th>
            <th>Details</th>
        </tr>
    `;
    table.appendChild(thead);

    // Create table body
    const tbody = document.createElement('tbody');
    devices.forEach(device => {
        const tr = document.createElement('tr');
        tr.setAttribute('data-device-id', device.id);

        // Format dates - handle both registeredAt and createdAt for backward compatibility
        const createdDate = new Date(device.registeredAt || device.createdAt);
        const lastUsedDate = device.lastUsedAt ? new Date(device.lastUsedAt) : null;

        const createdFormatted = formatDate(createdDate);
        const lastUsedFormatted = lastUsedDate ? formatDate(lastUsedDate) : 'Never';

        // Check if isActive is directly on the device or in deviceInfo
        const isActive = device.deviceInfo ? device.deviceInfo.isActive : device.isActive;

        // Create status badge
        const statusBadge = `<span class="device-status-${isActive ? 'active' : 'inactive'}">${isActive ? 'Active' : 'Inactive'}</span>`;

        // Get device info from either deviceInfo or directly from device
        const deviceInfo = device.deviceInfo || device;

        tr.innerHTML = `
            <td>${deviceInfo.name}</td>
            <td>${createdFormatted}</td>
            <td>${lastUsedFormatted}</td>
            <td>${statusBadge}</td>
            <td>
                <button class="view-device-btn" data-device-id="${device.id}">View</button>
            </td>
        `;

        // Make the entire row clickable
        tr.style.cursor = 'pointer';
        tr.addEventListener('click', function(e) {
            // Don't trigger if they clicked the button (button has its own handler)
            if (e.target.tagName !== 'BUTTON') {
                const deviceId = this.getAttribute('data-device-id');
                selectDevice(deviceId);
            }
        });
        tbody.appendChild(tr);
    });
    table.appendChild(tbody);

    // Add table to container
    devicesTableContainer.appendChild(table);

    // Add event listeners to view buttons
    const viewButtons = document.querySelectorAll('.view-device-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const deviceId = this.getAttribute('data-device-id');
            selectDevice(deviceId);
        });
    });
}



/**
 * Function to select a device and show details
 * @param {string} deviceId - ID of device to select
 * @returns {Promise<void>}
 */
export async function selectDevice(deviceId) {
    // Highlight the selected row
    const rows = document.querySelectorAll('.device-table tr');
    rows.forEach(row => {
        if (row.getAttribute('data-device-id') === deviceId) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
        }
    });

    try {
        // Find the device in the table
        const devices = document.querySelectorAll('.device-table tr[data-device-id]');
        let selectedDeviceData = null;

        for (const row of devices) {
            if (row.getAttribute('data-device-id') === deviceId) {
                // Extract device data from the row
                const cells = row.querySelectorAll('td');
                selectedDeviceData = {
                    id: deviceId,
                    deviceInfo: {
                        name: cells[0].textContent,
                        isActive: cells[3].querySelector('span').textContent === 'Active'
                    },
                    registeredAt: cells[1].textContent,
                    lastUsedAt: cells[2].textContent
                };
                break;
            }
        }

        if (!selectedDeviceData) {
            throw new Error('Device not found in the table');
        }

        // Store the selected device globally
        selectedDevice = selectedDeviceData;

        // We don't need the separate device details container anymore
        document.getElementById('device-details-container').classList.add('hidden');

        // Fetch the full device details from the API to get OS and browser info
        try {
            const deviceDetailsEndpoint = API_ENDPOINTS.AUTH_DEVICES_LIST;
            const deviceListResponse = await makeApiRequest(
                deviceDetailsEndpoint,
                'GET',
                null,
                null,
                null
            );

            // Find the selected device in the response
            const fullDeviceDetails = deviceListResponse.devices.find(d => d.id === deviceId);

            if (fullDeviceDetails) {
                // Get device info from either deviceInfo or directly from the device
                const deviceInfo = fullDeviceDetails.deviceInfo || fullDeviceDetails;

                // Update the deviceInfo in our selected device data
                if (!selectedDeviceData.deviceInfo) {
                    selectedDeviceData.deviceInfo = {};
                }

                // Add OS, browser, and type info to the selected device data
                selectedDeviceData.deviceInfo.os = deviceInfo.os || 'Unknown';
                selectedDeviceData.deviceInfo.browser = deviceInfo.browser || 'Unknown';
                selectedDeviceData.deviceInfo.type = deviceInfo.type || 'Unknown';
            }
        } catch (error) {
            console.error('Error fetching full device details:', error);
        }

        // Token count fetching has been removed

        // Get device info from either deviceInfo or directly from the device
        const deviceInfo = selectedDeviceData.deviceInfo || selectedDeviceData;
        const isActive = deviceInfo.isActive;

        // Format the device details
        let detailsHTML = `
            <div class="device-details-grid">
                <div class="device-detail-item">
                    <span class="detail-label">Type</span>
                    <span class="detail-value">${deviceInfo.type || 'Unknown'}</span>
                </div>
                <div class="device-detail-item">
                    <span class="detail-label">OS</span>
                    <span class="detail-value">${deviceInfo.os || 'Unknown'}</span>
                </div>
                <div class="device-detail-item">
                    <span class="detail-label">Browser</span>
                    <span class="detail-value">${deviceInfo.browser || 'Unknown'}</span>
                </div>
                <div class="device-detail-item">
                    <span class="detail-label">Status</span>
                    <span class="detail-value">${isActive ? 'Active' : 'Inactive'}</span>
                </div>
                <div class="device-detail-item" style="grid-column: span 2;">
                    <span class="detail-label">Device ID</span>
                    <span class="detail-value device-id">${selectedDeviceData.id}</span>
                </div>
            </div>
        `;

        detailsElement.innerHTML = detailsHTML;

        // Insert the details directly after the selected row as an expanded row
        const selectedRow = document.querySelector('.device-table tr.selected');
        if (selectedRow) {
            // Remove any existing detail rows
            const existingDetailRows = document.querySelectorAll('.device-detail-row');
            existingDetailRows.forEach(row => row.remove());

            // Create a new row for the details
            const detailRow = document.createElement('tr');
            detailRow.className = 'device-detail-row';

            // Create a cell that spans all columns
            const detailCell = document.createElement('td');
            const columnCount = selectedRow.querySelectorAll('td').length;
            detailCell.setAttribute('colspan', columnCount);
            detailCell.innerHTML = detailsHTML;

            // Add the cell to the row
            detailRow.appendChild(detailCell);

            // Insert the detail row after the selected row
            if (selectedRow.nextSibling) {
                selectedRow.parentNode.insertBefore(detailRow, selectedRow.nextSibling);
            } else {
                selectedRow.parentNode.appendChild(detailRow);
            }
        }
    } catch (error) {
        console.error('Error selecting device:', error);
        alert('Error selecting device: ' + error.message);
    }
}

export { selectedDevice };
