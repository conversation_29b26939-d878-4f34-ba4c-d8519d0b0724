/**
 * Constants for the PeepsAPI client-side code.
 * This file contains constants used throughout the client-side code,
 * including cookie names, API endpoints, and other configuration values.
 */

// Cookie constants
export const COOKIE_CONSTANTS = {
    // Cookie names
    OVERRIDE_PERSON_ID_COOKIE_NAME: 'x-peeps-id',
    SESSION_TOKEN_COOKIE_NAME: 'session_token',
    AZURE_AD_COOKIE_NAME: 'azure_ad_session_token',

    // <PERSON><PERSON> settings
    COOKIE_MAX_AGE: 28800, // 8 hours in seconds
    COOKIE_SECURE: true,
    COOKIE_SAMESITE: 'lax',
    COOKIE_PATH: '/',
};

// API endpoints
export const API_ENDPOINTS = {
    // Authentication endpoints
    AUTH_LOGIN_CHALLENGE: '/auth/login/challenge',
    AUTH_LOGIN_VERIFY: '/auth/login/verify',
    AUTH_REGISTER_CHALLENGE: '/auth/register/challenge',
    AUTH_REGISTER_VERIFY: '/auth/register/verify',
    AUTH_RECOVERY_INITIATE: '/auth/recover/initiate',
    AUTH_RECOVERY_REGISTER_CHALLENGE: '/auth/recover/challenge',
    AUTH_RECOVERY_REGISTER_VERIFY: '/auth/recover/verify',
    AUTH_INVITE: '/auth/invite',

    // Device management endpoints
    AUTH_DEVICES_LIST: '/auth/devices',
    AUTH_DEVICES_DELETE: '/auth/devices/{id}',
    AUTH_DEVICES_RENAME: '/auth/devices/{id}/rename',
    AUTH_DEVICES_TOGGLE_STATUS: '/auth/devices/{id}/toggle-status',

    // Token management endpoints

    // People endpoints
    PEOPLE_SEARCH: '/people/search',
    PEOPLE_GET: '/people/{id}',
};

// WebAuthn settings
export const WEBAUTHN_CONSTANTS = {
    TIMEOUT_MS: 60000, // 60 seconds in milliseconds
};

// Invite settings
export const INVITE_CONSTANTS = {
    DEFAULT_EXPIRATION_DAYS: 7,
    MAX_EXPIRATION_DAYS: 30,
};

// Domain settings
export const DOMAIN_CONSTANTS = {
    LOCAL: 'https://local.peepsapp.ai:8443',
    DEV: 'https://dev.peepsapp.ai',
    STAGE: 'https://stage.peepsapp.ai',
    PRODUCTION: 'https://api.peepsapp.ai',
};
