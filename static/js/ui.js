/**
 * UI module for handling UI-related functionality
 */

import { fetchDevices } from './devices.js';

/**
 * Function to initialize UI components
 */
export function initializeUI() {
    // Set up sidebar navigation
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            showSection(sectionId);
        });
    });

    // Set up identifier type selectors
    const identifierTypeSelectors = [
        'invite-identifier-type',
        'recovery-identifier-type',
        'login-identifier-type'
    ];

    identifierTypeSelectors.forEach(id => {
        const selector = document.getElementById(id);
        if (selector) {
            selector.addEventListener('change', function() {
                import('./forms.js').then(module => {
                    const formPrefix = this.id.replace('-identifier-type', '');
                    if (formPrefix === 'recovery') {
                        module.toggleRecoveryIdentifierInput();
                    } else if (formPrefix === 'login') {
                        module.toggleLoginIdentifierInput();
                    } else if (formPrefix === 'invite') {
                        module.toggleIdentifierInput();
                    }
                });
            });
        }
    });

    // Set up search input for Enter key
    const searchInput = document.getElementById('search-value');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // Import dynamically to avoid circular dependencies
                import('./identity.js').then(module => {
                    module.searchPeople();
                });
            }
        });
    }

    // Set up search button click
    const searchButtonElement = document.getElementById('search-people-btn');
    if (searchButtonElement) {
        searchButtonElement.addEventListener('click', function() {
            import('./identity.js').then(module => {
                module.searchPeople();
            });
        });
    }

    // Set up clear identity button
    const clearIdentityButton = document.getElementById('clear-identity-btn');
    if (clearIdentityButton) {
        clearIdentityButton.addEventListener('click', function() {
            import('./identity.js').then(module => {
                module.clearIdentity();
            });
        });
    }

    // Set up show search interface button
    const showSearchInterfaceButton = document.getElementById('show-search-interface-btn');
    if (showSearchInterfaceButton) {
        showSearchInterfaceButton.addEventListener('click', function() {
            import('./identity.js').then(module => {
                module.showSearchInterface();
            });
        });
    }

    // Set up create invite button
    const createInviteButton = document.getElementById('create-invite-btn');
    if (createInviteButton) {
        createInviteButton.addEventListener('click', function() {
            import('./forms.js').then(module => {
                module.createInvite();
            });
        });
    }

    // Set up create registration challenge button
    const createRegistrationChallengeButton = document.getElementById('create-registration-challenge-btn');
    if (createRegistrationChallengeButton) {
        createRegistrationChallengeButton.addEventListener('click', function() {
            const token = document.getElementById('invite-token').value;
            import('./webauthn.js').then(module => {
                module.createRegistrationChallenge(token, 'reg-challenge-display', 'reg-challenge-result');
            });
        });
    }

    // Set up complete registration button
    const completeRegistrationButton = document.getElementById('complete-registration-btn');
    if (completeRegistrationButton) {
        completeRegistrationButton.addEventListener('click', function() {
            import('./forms.js').then(module => {
                module.completeRegistration();
            });
        });
    }

    // Set up initiate recovery button
    const initiateRecoveryButton = document.getElementById('initiate-recovery-btn');
    if (initiateRecoveryButton) {
        initiateRecoveryButton.addEventListener('click', function() {
            import('./forms.js').then(module => {
                module.initiateRecovery();
            });
        });
    }

    // Set up create recovery challenge button
    const createRecoveryChallengeButton = document.getElementById('create-recovery-challenge-btn');
    if (createRecoveryChallengeButton) {
        createRecoveryChallengeButton.addEventListener('click', function() {
            const token = document.getElementById('recovery-token').value;
            import('./webauthn.js').then(module => {
                module.createRecoveryChallenge(token, 'challenge-display', 'challenge-result');
            });
        });
    }

    // Set up register passkey button
    const registerPasskeyButton = document.getElementById('register-passkey-btn');
    if (registerPasskeyButton) {
        registerPasskeyButton.addEventListener('click', function() {
            import('./forms.js').then(module => {
                module.registerPasskey();
            });
        });
    }

    // Set up get authentication challenge button
    const getAuthenticationChallengeButton = document.getElementById('get-authentication-challenge-btn');
    if (getAuthenticationChallengeButton) {
        getAuthenticationChallengeButton.addEventListener('click', async function() {
            const formsModule = await import('./forms.js');
            const webauthnModule = await import('./webauthn.js');

            const { requestBody, isValid } = formsModule.getIdentifierFromForm('login');
            if (!isValid) return;

            webauthnModule.getAuthenticationChallenge(requestBody, 'auth-challenge-display', 'auth-challenge-result');
        });
    }

    // Set up authenticate with passkey button
    const authenticateWithPasskeyButton = document.getElementById('authenticate-with-passkey-btn');
    if (authenticateWithPasskeyButton) {
        authenticateWithPasskeyButton.addEventListener('click', function() {
            import('./forms.js').then(module => {
                module.authenticateWithPasskey();
            });
        });
    }

    // Set up refresh devices button
    const refreshDevicesButton = document.getElementById('refresh-devices-btn');
    if (refreshDevicesButton) {
        refreshDevicesButton.addEventListener('click', function() {
            import('./devices.js').then(module => {
                module.fetchDevices();
            });
        });
    }

    // Device management buttons have been removed
}

/**
 * Function to show a specific content section
 * @param {string} sectionId - ID of section to show
 */
export function showSection(sectionId) {
    // If no section ID is provided, default to Peeps Identity
    if (!sectionId) {
        sectionId = 'peep-identity-section';
    }

    // Hide all content sections
    const contentSections = document.querySelectorAll('.content-section');
    contentSections.forEach(section => {
        section.classList.add('hidden');
    });

    // Show the selected section
    const selectedSection = document.getElementById(sectionId);
    if (selectedSection) {
        selectedSection.classList.remove('hidden');

        // If this is the device management section, load devices
        if (sectionId === 'device-info-section') {
            fetchDevices();
        }
    } else {
        // If the section doesn't exist, show Peeps Identity as fallback
        document.getElementById('peep-identity-section').classList.remove('hidden');
        sectionId = 'peep-identity-section';
    }

    // Update active state in sidebar
    const sidebarLinks = document.querySelectorAll('.sidebar-nav a');
    sidebarLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-section') === sectionId) {
            link.classList.add('active');
        }
    });

    // If no sidebar link is active, set Peeps Identity as active
    const hasActiveLink = Array.from(sidebarLinks).some(link => link.classList.contains('active'));
    if (!hasActiveLink) {
        const peepIdentityNav = document.getElementById('peep-identity-nav');
        if (peepIdentityNav) {
            peepIdentityNav.classList.add('active');
        }
    }
}

/**
 * Function to update sidebar visibility based on peep-id availability
 * @param {boolean} hasPeepId - Whether a peep ID is available
 */
export function updateSidebarVisibility(hasPeepId) {
    const conditionalNavItems = document.querySelectorAll('.conditional-nav-item');

    conditionalNavItems.forEach(item => {
        if (hasPeepId) {
            item.classList.remove('hidden');
        } else {
            item.classList.add('hidden');

            // If we're hiding a section that's currently active, switch to Peep Identity
            const link = item.querySelector('a');
            if (link && link.classList.contains('active')) {
                showSection('peep-identity-section');
            }
        }
    });
}
