/**
 * WebAuthn module for handling WebAuthn operations
 * Includes functions for registration and authentication
 */

import { WEBAUTHN_CONSTANTS, API_ENDPOINTS } from './constants.js';
import { makeApiRequest } from './api.js';

// Global variable to store challenge data
let challengeData = null;

/**
 * Function to convert base64url to ArrayBuffer
 * @param {string} base64url - Base64URL encoded string
 * @returns {ArrayBuffer} - Decoded ArrayBuffer
 */
export function base64urlToArrayBuffer(base64url) {
    const base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
    const binaryString = window.atob(base64);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
}

/**
 * Function to convert ArrayBuffer to base64url
 * @param {<PERSON><PERSON><PERSON><PERSON>uffer} buffer - ArrayBuffer to encode
 * @returns {string} - Base64URL encoded string
 */
export function arrayBufferToBase64url(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    const base64 = window.btoa(binary);
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Utility function to prepare WebAuthn registration options
 * @param {object} publicKeyOptions - Options from the server
 * @returns {object} - Options formatted for the browser
 */
export function prepareWebAuthnRegistrationOptions(publicKeyOptions) {
    return {
        challenge: base64urlToArrayBuffer(publicKeyOptions.challenge),
        rp: {
            name: publicKeyOptions.rp.name,
            id: publicKeyOptions.rp.id
        },
        user: {
            id: base64urlToArrayBuffer(publicKeyOptions.user.id),
            name: publicKeyOptions.user.name,
            displayName: publicKeyOptions.user.displayName
        },
        pubKeyCredParams: publicKeyOptions.pubKeyCredParams,
        timeout: publicKeyOptions.timeout || WEBAUTHN_CONSTANTS.TIMEOUT_MS, // Default timeout if not provided
        excludeCredentials: (publicKeyOptions.excludeCredentials || []).map(credential => ({
            id: base64urlToArrayBuffer(credential.id),
            type: credential.type,
            transports: credential.transports
        })),
        authenticatorSelection: publicKeyOptions.authenticatorSelection,
        attestation: publicKeyOptions.attestation
    };
}

/**
 * Utility function to prepare WebAuthn authentication options
 * @param {object} publicKeyOptions - Options from the server
 * @returns {object} - Options formatted for the browser
 */
export function prepareWebAuthnAuthenticationOptions(publicKeyOptions) {
    return {
        challenge: base64urlToArrayBuffer(publicKeyOptions.challenge),
        rpId: publicKeyOptions.rpId || window.location.hostname,
        allowCredentials: (publicKeyOptions.allowCredentials || []).map(cred => ({
            id: base64urlToArrayBuffer(cred.id),
            type: cred.type || 'public-key',
            transports: cred.transports || ['internal']
        })),
        timeout: publicKeyOptions.timeout || WEBAUTHN_CONSTANTS.TIMEOUT_MS,
        userVerification: publicKeyOptions.userVerification || 'preferred'
    };
}

/**
 * Utility function to convert registration credential to server format
 * @param {object} credential - Credential from the browser
 * @returns {object} - Credential formatted for the server
 */
export function formatRegistrationCredential(credential) {
    return {
        id: credential.id,
        rawId: arrayBufferToBase64url(credential.rawId),
        response: {
            clientDataJSON: arrayBufferToBase64url(credential.response.clientDataJSON),
            attestationObject: arrayBufferToBase64url(credential.response.attestationObject)
        },
        type: credential.type
    };
}

/**
 * Utility function to convert authentication credential to server format
 * @param {object} credential - Credential from the browser
 * @returns {object} - Credential formatted for the server
 */
export function formatAuthenticationCredential(credential) {
    return {
        id: credential.id,
        rawId: arrayBufferToBase64url(credential.rawId),
        response: {
            clientDataJSON: arrayBufferToBase64url(credential.response.clientDataJSON),
            authenticatorData: arrayBufferToBase64url(credential.response.authenticatorData),
            signature: arrayBufferToBase64url(credential.response.signature),
            userHandle: credential.response.userHandle ? arrayBufferToBase64url(credential.response.userHandle) : null
        },
        type: credential.type
    };
}

/**
 * Function to check if WebAuthn is supported
 * @returns {boolean} - Whether WebAuthn is supported
 */
export function checkWebAuthnSupport() {
    // This function now only checks WebAuthn support but doesn't display it
    // The display is now handled by the device management UI
    if (window.PublicKeyCredential) {
        return true;
    } else {
        return false;
    }
}

/**
 * Function to create a registration challenge
 * @param {string} token - Invite token
 * @param {string} displayElementId - ID of element to display response in
 * @param {string} resultContainerId - ID of container to show/hide
 * @returns {Promise<object>} - Promise resolving to challenge data
 */
export async function createRegistrationChallenge(token, displayElementId, resultContainerId) {
    try {
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_REGISTER_CHALLENGE,
            'POST',
            {
                token: token
            },
            displayElementId,
            resultContainerId
        );

        console.log("createRegistrationChallenge - data: ", data);

        // Store the challenge data for registration
        challengeData = data;
        return data;
    } catch (error) {
        // Handle specific validation errors from the server
        if (error.message.includes("Invalid invite token") ||
            error.message.includes("Invite token has expired") ||
            error.message.includes("Invite token has already been used")) {

            // Display the error in the challenge result area
            if (displayElementId) {
                document.getElementById(displayElementId).textContent =
                    `Error: ${error.message}\n\nPlease create a new invite.`;
                if (resultContainerId) {
                    document.getElementById(resultContainerId).classList.remove('hidden');
                }
            }
        }
        throw error;
    }
}

/**
 * Function to create a recovery challenge
 * @param {string} token - Recovery token
 * @param {string} displayElementId - ID of element to display response in
 * @param {string} resultContainerId - ID of container to show/hide
 * @returns {Promise<object>} - Promise resolving to challenge data
 */
export async function createRecoveryChallenge(token, displayElementId, resultContainerId) {
    try {
        // Use the recovery token to create a registration challenge
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_RECOVERY_REGISTER_CHALLENGE,
            'POST',
            {
                token: token,
                isRecovery: true
            },
            displayElementId,
            resultContainerId
        );

        console.log("createRecoveryChallenge - data: ", data);

        // Store the challenge data for registration
        challengeData = data;
        return data;
    } catch (error) {
        throw error;
    }
}

/**
 * Function to get authentication challenge
 * @param {object} requestBody - Request body with identifier
 * @param {string} displayElementId - ID of element to display response in
 * @param {string} resultContainerId - ID of container to show/hide
 * @returns {Promise<object>} - Promise resolving to challenge data
 */
export async function getAuthenticationChallenge(requestBody, displayElementId, resultContainerId) {
    try {
        // Create a request with the selected identifier for authentication
        const data = await makeApiRequest(
            API_ENDPOINTS.AUTH_LOGIN_CHALLENGE,
            'POST',
            requestBody,
            displayElementId,
            resultContainerId
        );

        // Get the publicKey options from the challenge data
        const pkOptions = data.publicKey;
        console.log("getAuthenticationChallenge - pkOptions: ", pkOptions);

        // Create the publicKeyCredentialRequestOptions for the browser
        const publicKeyCredentialRequestOptions = prepareWebAuthnAuthenticationOptions(pkOptions);
        console.log("getAuthenticationChallenge - publicKeyCredentialRequestOptions: ", publicKeyCredentialRequestOptions);

        // Store the challenge data for authentication
        challengeData = {
            original: data,
            publicKeyCredentialRequestOptions: publicKeyCredentialRequestOptions
        };
        console.log("getAuthenticationChallenge - challengeData: ", challengeData);
        return challengeData;
    } catch (error) {
        throw error;
    }
}

// Export the challengeData for use in other modules
export { challengeData };
