// Utility to set the override user cookie for integration tests
export function setOverrideUserCookie(userId) {
    document.cookie = `x-peeps-id=${userId}; path=/; max-age=28800; samesite=lax`;
    document.cookie = `x-peeps-auth-source=jwt; path=/; max-age=28800; samesite=lax`;
}

// Utility to remove override cookies for user id and auth source
export function removeOverrideCookies() {
    document.cookie = 'x-peeps-id=; path=/; max-age=0; samesite=lax';
    document.cookie = 'x-peeps-auth-source=; path=/; max-age=0; samesite=lax';
}

// Assertion helper for integration tests
export function assert(condition, message) {
    if (!condition) throw new Error(message || 'Assertion failed');
}

// Helper to get timestamp in HH:mm:ss.SSS format
function getTimestamp() {
    const d = new Date();
    return d.toLocaleTimeString('en-US', { hour12: false }) + '.' + d.getMilliseconds().toString().padStart(3, '0');
}

// Output formatting helpers for integration test output
export function info(msg) {
    document.getElementById('integration-test-output').textContent += `[${getTimestamp()}] ${msg}\n`;
}
export function good(msg) {
    document.getElementById('integration-test-output').textContent += `[${getTimestamp()}] ✅ ${msg}\n`;
}
export function fail(msg) {
    document.getElementById('integration-test-output').textContent += `[${getTimestamp()}] ❌ ${msg}\n`;
}
