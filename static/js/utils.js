/**
 * Utility functions for the accounts page
 */

import { COOKIE_CONSTANTS } from './constants.js';

/**
 * Function to toggle sections
 * @param {string} sectionId - ID of section to toggle
 */
export function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section.classList.contains('hidden')) {
        section.classList.remove('hidden');
    } else {
        section.classList.add('hidden');
    }
}

/**
 * Function to format a date
 * @param {Date} date - Date to format
 * @returns {string} - Formatted date string
 */
export function formatDate(date) {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
        return 'Today';
    } else if (diffDays === 1) {
        return 'Yesterday';
    } else if (diffDays < 7) {
        return `${diffDays} days ago`;
    } else if (diffDays < 30) {
        const weeks = Math.floor(diffDays / 7);
        return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
    } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        return `${months} ${months === 1 ? 'month' : 'months'} ago`;
    } else {
        return date.toLocaleDateString();
    }
}

/**
 * Function to get a cookie value by name
 * @param {string} name - Cookie name
 * @returns {string|null} - Cookie value or null if not found
 */
export function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
        const result = parts.pop().split(';').shift();
        return result;
    }
    return null;
}

/**
 * Function to set a cookie
 * @param {string} name - Cookie name
 * @param {string} value - Cookie value
 * @param {object} options - Cookie options
 */
export function setCookie(name, value, options = {}) {
    const cookiePath = options.path || COOKIE_CONSTANTS.COOKIE_PATH;
    const cookieMaxAge = options.maxAge || COOKIE_CONSTANTS.COOKIE_MAX_AGE;
    const cookieSecure = options.secure !== undefined ? options.secure : COOKIE_CONSTANTS.COOKIE_SECURE;
    const cookieSameSite = options.sameSite || COOKIE_CONSTANTS.COOKIE_SAMESITE;

    let cookieString = `${name}=${value}; path=${cookiePath}; max-age=${cookieMaxAge}; samesite=${cookieSameSite}`;

    if (cookieSecure) {
        cookieString += '; secure';
    }

    document.cookie = cookieString;
}

/**
 * Function to delete a cookie
 * @param {string} name - Cookie name
 * @param {string} path - Cookie path
 */
export function deleteCookie(name, path = COOKIE_CONSTANTS.COOKIE_PATH) {
    document.cookie = `${name}=; path=${path}; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
}
