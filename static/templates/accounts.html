<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Management</title>
    <link rel="stylesheet" href="/static/css/accounts.css">
</head>
<body>
    <div class="page-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>Account Management</h2>
            </div>
            <ul class="sidebar-nav">
                <li><a href="#" class="active" data-section="peep-identity-section" id="peep-identity-nav">Peeps Identity</a></li>
                <li class="conditional-nav-item hidden" id="invite-nav-item"><a href="#" data-section="invite-registration-section">Invite & Registration</a></li>
                <li class="conditional-nav-item hidden" id="recovery-nav-item"><a href="#" data-section="recovery-section">Recovery Flow</a></li>
                <li class="conditional-nav-item hidden" id="login-nav-item"><a href="#" data-section="login-section">Login Flow</a></li>
                <li><a href="#" data-section="device-info-section">Device Information</a></li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Peep Identity Section -->
            <div id="peep-identity-section" class="section content-section">
                <h2>Peep Identity</h2>
                <div id="current-identity" class="identity-actions hidden">
                    <button id="clear-identity-btn">Clear Identity</button>
                    <button id="show-search-interface-btn">Change Identity</button>
                </div>
                <div id="peep-identity">
                    <div id="current-identity-content" class="hidden">
                        <h3>Current Identity:</h3>
                        <pre id="current-identity-display"></pre>
                    </div>
                    <div id="search-interface">
                        <h3>Search for a Peep</h3>
                        <div>
                            <label for="search-attribute">Search by:</label>
                            <select id="search-attribute">
                                <option value="name">First Name</option>
                                <option value="lastName">Last Name</option>
                                <option value="email">Email</option>
                                <option value="phone">Phone Number</option>
                            </select>
                        </div>
                        <div>
                            <label for="search-value">Search value:</label>
                            <input type="text" id="search-value" placeholder="Enter search value">
                        </div>
                        <button id="search-people-btn">Search</button>
                    </div>
                    <div id="search-results" class="hidden">
                        <h3>Search Results:</h3>
                        <div id="people-list"></div>
                    </div>
                    <div id="selected-person" class="hidden">
                        <h3>Selected Person:</h3>
                        <pre id="selected-person-display"></pre>
                        <input type="hidden" id="selected-person-id">
                    </div>
                </div>
            </div>

            <!-- Invite and Registration Flow Section -->
            <div id="invite-registration-section" class="section content-section hidden">
                <h2>Invite and Registration Flow</h2>
                <div id="invite-flow">
                    <div>
                        <h3>Create Invite</h3>
                        <div class="warning" id="no-person-selected-warning">
                            Please search and select a person first to create an invite.
                        </div>
                        <div>
                            <label for="invite-identifier-type">Identifier Type:</label>
                            <select id="invite-identifier-type">
                                <option value="email">Email</option>
                                <option value="phone">Phone Number</option>
                            </select>
                        </div>
                        <div id="email-input-container">
                            <label for="invite-email">Email:</label>
                            <input type="email" id="invite-email">
                        </div>
                        <div id="phone-input-container" class="hidden">
                            <label for="invite-phone">Phone Number:</label>
                            <input type="tel" id="invite-phone">
                        </div>
                        <div>
                            <label for="invite-expires">Expires in (days):</label>
                            <input type="number" id="invite-expires" value="7" min="1" max="30">
                        </div>
                        <button id="create-invite-btn" disabled>Create Invite</button>
                    </div>
                    <div id="invite-result" class="hidden">
                        <h3>Invite Created:</h3>
                        <pre id="invite-display"></pre>
                        <input type="hidden" id="invite-token">
                        <button id="create-registration-challenge-btn">Create Registration Challenge</button>
                    </div>
                    <div id="reg-challenge-result" class="hidden">
                        <h3>Registration Challenge:</h3>
                        <pre id="reg-challenge-display"></pre>
                        <button id="complete-registration-btn">Register Passkey</button>
                    </div>
                    <div id="reg-result" class="hidden">
                        <h3>Registration Result:</h3>
                        <pre id="reg-result-display"></pre>
                    </div>
                </div>
            </div>

            <!-- Recovery Flow Section -->
            <div id="recovery-section" class="section content-section hidden">
                <h2>Recovery Flow</h2>
                <div id="recovery-flow">
                    <div>
                        <div>
                            <label for="recovery-identifier-type">Identifier Type:</label>
                            <select id="recovery-identifier-type">
                                <option value="email">Email</option>
                                <option value="phone">Phone Number</option>
                            </select>
                        </div>
                        <div id="recovery-email-container">
                            <label for="recovery-email">Email:</label>
                            <input type="email" id="recovery-email">
                        </div>
                        <div id="recovery-phone-container" class="hidden">
                            <label for="recovery-phone">Phone Number:</label>
                            <input type="tel" id="recovery-phone">
                        </div>
                        <button id="initiate-recovery-btn">Initiate Recovery</button>
                    </div>
                    <div id="recovery-result" class="hidden">
                        <h3>Recovery Information:</h3>
                        <pre id="recovery-token-display"></pre>
                        <input type="hidden" id="recovery-token">
                        <input type="hidden" id="user-id">
                        <!-- Recovery code input removed - using token-based recovery flow only -->
                        <button id="create-recovery-challenge-btn">Create Registration Challenge</button>
                    </div>
                    <div id="challenge-result" class="hidden">
                        <h3>Registration Challenge:</h3>
                        <pre id="challenge-display"></pre>
                        <button id="register-passkey-btn">Register Passkey</button>
                    </div>
                    <div id="registration-result" class="hidden">
                        <h3>Registration Result:</h3>
                        <pre id="registration-display"></pre>
                    </div>
                </div>
            </div>

            <!-- Login Flow Section -->
            <div id="login-section" class="section content-section hidden">
                <h2>Login Flow</h2>
                <div id="login-flow">
                    <div>
                        <div>
                            <label for="login-identifier-type">Identifier Type:</label>
                            <select id="login-identifier-type">
                                <option value="email">Email</option>
                                <option value="phone">Phone Number</option>
                            </select>
                        </div>
                        <div id="login-email-container">
                            <label for="login-email">Email:</label>
                            <input type="email" id="login-email">
                        </div>
                        <div id="login-phone-container" class="hidden">
                            <label for="login-phone">Phone Number:</label>
                            <input type="tel" id="login-phone">
                        </div>
                        <button id="get-authentication-challenge-btn">Get Authentication Challenge</button>
                    </div>
                    <div id="auth-challenge-result" class="hidden">
                        <h3>Authentication Challenge:</h3>
                        <pre id="auth-challenge-display"></pre>
                        <button id="authenticate-with-passkey-btn">Authenticate with Passkey</button>
                    </div>
                    <div id="auth-result" class="hidden">
                        <h3>Authentication Result:</h3>
                        <pre id="auth-result-display"></pre>
                    </div>
                </div>
            </div>

            <!-- Device Management Section -->
            <div id="device-info-section" class="section content-section hidden">
                <h2>Device Management</h2>

                <div id="device-management-warning" class="warning hidden">
                    Please select a person first to manage their devices.
                </div>

                <div id="device-management-content" class="hidden">
                    <div class="device-management-actions">
                        <button id="refresh-devices-btn">Refresh Device List</button>
                    </div>

                    <div id="devices-loading" class="hidden">
                        <p>Loading devices...</p>
                    </div>

                    <div id="devices-list-container" class="hidden">
                        <h3>Registered Devices</h3>
                        <div id="no-devices-message" class="hidden">
                            <p>No devices found for this user.</p>
                        </div>
                        <div id="devices-table-container"></div>
                    </div>

                    <div id="device-details-container" class="hidden">
                        <h3>Device Details</h3>
                        <pre id="device-details"></pre>
                        <div id="device-actions">
                            <!-- Device action buttons will be added here dynamically if needed -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include main module with type="module" -->
    <script type="module" src="/static/js/main.js"></script>
</body>
</html>
