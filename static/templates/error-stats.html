<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Statistics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        h1, h2, h3 {
            color: #333;
        }
        .stats-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-item {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-width: 150px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .success-rate {
            color: #28a745;
        }
        .failure-rate {
            color: #dc3545;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .section {
            margin-bottom: 30px;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .timestamp {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .refresh-button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .refresh-button:hover {
            background-color: #0069d9;
        }
        .time-selector {
            margin-bottom: 20px;
        }
        .time-selector select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>Error Statistics</h1>
    <p class="timestamp">Generated at: {{generated_at}}</p>

    <div class="time-selector">
        <label for="time-range">Time Range:</label>
        <select id="time-range" onchange="changeTimeRange()">
            <option value="6" {{time_range_6}}>Last 6 Hours</option>
            <option value="12" {{time_range_12}}>Last 12 Hours</option>
            <option value="24" {{time_range_24}}>Last 24 Hours</option>
            <option value="48" {{time_range_48}}>Last 48 Hours</option>
            <option value="168" {{time_range_168}}>Last 7 Days</option>
        </select>
        <button class="refresh-button" onclick="refreshStats()">Refresh</button>
    </div>

    <div class="section">
        <h2>Summary</h2>
        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-value">{{ total_errors }}</div>
                <div class="stat-label">Total Errors</div>
            </div>
            {% for error_type in error_types %}
            <div class="stat-item">
                <div class="stat-value">{{ error_type.count }}</div>
                <div class="stat-label">{{ error_type.name }} Errors</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="section">
        <h2>Top Error Endpoints</h2>
        <table>
            <thead>
                <tr>
                    <th>Endpoint</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                {% for endpoint in top_endpoints %}
                <tr>
                    <td>{{ endpoint.endpoint }}</td>
                    <td>{{ endpoint.count }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Status Code Distribution</h2>
        <table>
            <thead>
                <tr>
                    <th>Status Code</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                {% for status in status_codes %}
                <tr>
                    <td>{{ status.code }}</td>
                    <td>{{ status.count }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Recent Errors</h2>
        <table>
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Type</th>
                    <th>Code</th>
                    <th>Path</th>
                </tr>
            </thead>
            <tbody>
                {% for error in recent_errors %}
                <tr>
                    <td>{{ error.timestamp }}</td>
                    <td>{{ error.error_type }}</td>
                    <td>{{ error.error_code }}</td>
                    <td>{{ error.method }} {{ error.path }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Suspicious IP Addresses</h2>
        {% if suspicious_ips %}
        <table>
            <thead>
                <tr>
                    <th>IP Address</th>
                </tr>
            </thead>
            <tbody>
                {% for ip in suspicious_ips %}
                <tr>
                    <td>{{ ip }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>No suspicious IP addresses detected.</p>
        {% endif %}
    </div>

    <script>
        function refreshStats() {
            window.location.reload();
        }

        function changeTimeRange() {
            const hours = document.getElementById('time-range').value;
            window.location.href = `/dashboard/error-stats-embed?hours=${hours}`;
        }
    </script>
</body>
</html>
