<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Integration Test Actions</title>
    <style>
        body { font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif; background: #f5f5f5; color: #222; margin: 0; padding: 0; }
        .integration-container {
            max-width: none;
            width: 100vw;
            margin: 0;
            border-radius: 0;
            box-shadow: none;
            padding: 24px;
        }
        h2 { margin-top: 0; }
        button { margin-right: 10px; margin-bottom: 10px; padding: 8px 18px; border: none; border-radius: 4px; background: #0078d4; color: #fff; font-weight: 500; cursor: pointer; }
        button:hover { background: #005fa3; }
        pre { background: #222; color: #eee; padding: 10px; margin-top: 10px; overflow: auto; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="integration-container">
        <h2>Integration Test Actions</h2>
        <button id="run-integration-tests-btn">Run All Integration Tests</button>
        <pre id="integration-test-output"></pre>
    </div>
    <script type="module">
        import { setOverrideUserCookie, assert, info, good, fail, removeOverrideCookies } from '/static/js/integration-test-utils.js';

        let createdUserIds = [];
        async function createThreeUsers() {
            const now = new Date().toISOString();
            info('Creating 3 users...');
            createdUserIds = [];
            for (let i = 0; i < 3; i++) {
                const user = {
                    name: `Integration_Test${i}`,
                    lastName: `User${i}`,
                    currentRole: "Engineer",
                    currentCompany: "Independent",
                    location: "Test City",
                    profilePic: "",
                    invitedById: "6263ae7e-eb2c-4d1d-997b-de5e2da2b35a",
                    memberSince: now,
                    socialLinks: [
                        {
                            platform: "whatsapp",
                            url: `https://whatsapp.com/+1000000000${i}`,
                            username: `+1000000000${i}`,
                            id: `+1000000000${i}`,
                            verified: false,
                            connectedAt: now
                        }
                    ],
                    emails: [
                        {
                            type: "personal",
                            address: `test${i}@example.com`,
                            activeSince: now,
                            deactivatedAt: null,
                            verified: true
                        }
                    ],
                    phoneNumbers: [
                        {
                            type: "mobile",
                            number: `+1000000000${i}`,
                            activeSince: now,
                            deactivatedAt: null,
                            verified: true
                        }
                    ],
                    upcomingEvents: [],
                    notes: null,
                    reminders: null,
                    achievements: [],
                    bio: "Integration test user.",
                    profileCompleted: false,
                    hasPasskey: false,
                    primaryIdentifierType: "email",
                    primaryIdentifierValue: `test${i}@example.com`,
                    connectionStatus: null,
                    connectionRequestee: null,
                    feedSize: 0
                };
                try {
                    const resp = await fetch('/people/', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify(user)
                    });
                    const data = await resp.json();
                    if (resp.ok) {
                        createdUserIds.push(data.id);
                        info(`Created: ${data.id}`);
                    } else {
                        fail(`Error: ${JSON.stringify(data)}`);
                    }
                } catch (e) {
                    fail(`Exception: ${e}`);
                }
            }
        }
        async function getThreeUsers() {
            if (!createdUserIds.length) {
                fail('No users created yet.');
                return;
            }
            info('Getting 3 users...');
            for (let id of createdUserIds) {
                try {
                    const resp = await fetch(`/people/${id}`);
                    const data = await resp.json();
                    if (resp.ok) {
                        info(`User: ${JSON.stringify(data)}`);
                    } else {
                        fail(`Error: ${JSON.stringify(data)}`);
                    }
                } catch (e) {
                    fail(`Exception: ${e}`);
                }
            }
        }
        async function removeThreeUsers() {
            if (!createdUserIds.length) {
                fail('No users created yet.');
                return;
            }
            info('Cleaning 3 users via admin endpoint...');
            for (let id of createdUserIds) {
                try {
                    const resp = await fetch(`/admin/person/clean/${id}`, {method: 'POST'});
                    const data = await resp.json();
                    if (resp.ok && data.ok) {
                        good(`Cleaned: ${id}`);
                    } else {
                        fail(`Error cleaning user ${id}: ${JSON.stringify(data)}`);
                    }
                } catch (e) {
                    fail(`Exception cleaning user ${id}: ${e}`);
                }
            }
            createdUserIds = [];
        }
        async function runAllIntegrationTests() {
            document.getElementById('integration-test-output').textContent = '';
            const startTime = Date.now();
            try {
                await createThreeUsers();
                await getThreeUsers();

                // 1. Send invite to connect to user 1 and user 2
                info('====================================================================');
                info('SECTION 1: SEND CONNECTION INVITES TO USER 1 AND USER 2');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info(`Switched to test user 0: ${createdUserIds[0]}`);
                for (let i = 1; i < 3; i++) {
                    info(`Sending invite from user 0 to user ${i}...`);
                    const connectionPayload = { person_id: createdUserIds[i] };
                    const inviteResp = await fetch('/connections', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify(connectionPayload)
                    });
                    const inviteData = await inviteResp.json();
                    assert(inviteResp.ok, `Invite to user ${i} failed: ${JSON.stringify(inviteData)}`);
                    info(`Invite sent: ${JSON.stringify(inviteData)}`);
                }

                // 2. Fetch user 1 and user 2, check connectionStatus and connectionRequestee
                info('====================================================================');
                info('SECTION 2: VERIFY CONNECTION STATUS AFTER INVITES');
                info('====================================================================');
                for (let i = 1; i < 3; i++) {
                    info(`Fetching user ${i} after invite...`);
                    const checkResp = await fetch(`/people/${createdUserIds[i]}`);
                    const checkData = await checkResp.json();
                    assert(checkResp.ok, `Failed to fetch user ${i}: ${JSON.stringify(checkData)}`);
                    info(`Fetched user ${i} after invite`);
                    // Assert connectionStatus
                    assert(checkData.connectionStatus === 'requested', `User ${i} connectionStatus incorrect: got '${checkData.connectionStatus}', expected 'requested'`);
                    good(`connectionStatus is correct (value: ${checkData.connectionStatus})`);
                    // Assert connectionRequestee
                    assert(checkData.connectionRequestee === true, `User ${i} connectionRequestee incorrect: got '${checkData.connectionRequestee}', expected true`);
                    good(`connectionRequestee is correct (value: ${checkData.connectionRequestee})`);
                }

                // 3. Switch to user 1 and accept invite from user 0
                info('====================================================================');
                info('SECTION 3: USER 1 ACCEPTS INVITE FROM USER 0');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[1]);
                info(`Switched to test user 1: ${createdUserIds[1]}`);
                info(`Accepting invite from user 0...`);
                let patchResp = await fetch(`/connections/${createdUserIds[0]}`, {
                    method: 'PATCH',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ status: 'accepted' })
                });
                let patchData = await patchResp.json();
                assert(patchResp.ok, `Accept invite failed: ${JSON.stringify(patchData)}`);
                good('Invite accepted.');
                // Fetch user 0 and check connectionStatus and connectionRequestee
                let user0Resp = await fetch(`/people/${createdUserIds[0]}`);
                let user0Data = await user0Resp.json();
                assert(user0Resp.ok, `Failed to fetch user 0: ${JSON.stringify(user0Data)}`);
                info('Fetched user 0 after acceptance.');
                assert(user0Data.connectionStatus === 'accepted', `User 0 connectionStatus incorrect: got '${user0Data.connectionStatus}', expected 'accepted'`);
                good(`connectionStatus is correct (value: ${user0Data.connectionStatus})`);
                assert(user0Data.connectionRequestee === false, `User 0 connectionRequestee incorrect: got '${user0Data.connectionRequestee}', expected false`);
                good(`connectionRequestee is correct (value: ${user0Data.connectionRequestee})`);

                // 4. Switch to user 2 and reject invite from user 0
                info('====================================================================');
                info('SECTION 4: USER 2 REJECTS INVITE FROM USER 0');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[2]);
                info(`Switched to test user 2: ${createdUserIds[2]}`);
                info(`Rejecting invite from user 0...`);
                patchResp = await fetch(`/connections/${createdUserIds[0]}`, {
                    method: 'PATCH',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ status: 'rejected' })
                });
                patchData = await patchResp.json();
                assert(patchResp.ok, `Reject invite failed: ${JSON.stringify(patchData)}`);
                good('Invite rejected.');
                // Fetch user 0 and check connectionStatus and connectionRequestee
                user0Resp = await fetch(`/people/${createdUserIds[0]}`);
                user0Data = await user0Resp.json();
                assert(user0Resp.ok, `Failed to fetch user 0: ${JSON.stringify(user0Data)}`);
                info('Fetched user 0 after rejection.');
                assert(user0Data.connectionStatus === 'rejected', `User 0 connectionStatus incorrect: got '${user0Data.connectionStatus}', expected 'rejected'`);
                good(`connectionStatus is correct (value: ${user0Data.connectionStatus})`);
                assert(user0Data.connectionRequestee === false, `User 0 connectionRequestee incorrect: got '${user0Data.connectionRequestee}', expected false`);
                good(`connectionRequestee is correct (value: ${user0Data.connectionRequestee})`);
                // Send a new invitation from user 2 to user 0
                info('Sending new invite from user 2 to user 0...');
                const inviteAgainResp = await fetch('/connections', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ person_id: createdUserIds[0] })
                });
                const inviteAgainData = await inviteAgainResp.json();
                assert(inviteAgainResp.ok, `Invite from user 2 to user 0 failed: ${JSON.stringify(inviteAgainData)}`);
                good('New invite sent from user 2 to user 0.');

                // 5. Switch back to user 0 and accept invite from user 2
                info('====================================================================');
                info('SECTION 5: USER 0 ACCEPTS INVITE FROM USER 2');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info(`Switched back to test user 0: ${createdUserIds[0]}`);
                info('Accepting invite from user 2...');
                patchResp = await fetch(`/connections/${createdUserIds[2]}`, {
                    method: 'PATCH',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ status: 'accepted' })
                });
                patchData = await patchResp.json();
                assert(patchResp.ok, `Accept invite from user 2 failed: ${JSON.stringify(patchData)}`);
                good('Invite from user 2 accepted.');
                // Fetch user 2 and check connectionStatus and connectionRequestee
                let user2Resp = await fetch(`/people/${createdUserIds[2]}`);
                let user2Data = await user2Resp.json();
                assert(user2Resp.ok, `Failed to fetch user 2: ${JSON.stringify(user2Data)}`);
                info('Fetched user 2 after acceptance.');
                assert(user2Data.connectionStatus === 'accepted', `User 2 connectionStatus incorrect: got '${user2Data.connectionStatus}', expected 'accepted'`);
                good(`connectionStatus is correct (value: ${user2Data.connectionStatus})`);
                assert(user2Data.connectionRequestee === false, `User 2 connectionRequestee incorrect: got '${user2Data.connectionRequestee}', expected false`);
                good(`connectionRequestee is correct (value: ${user2Data.connectionRequestee})`);

                // 6. User 0 gets active connections
                info('====================================================================');
                info('SECTION 6: VERIFY ACTIVE CONNECTIONS FOR USER 0');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 fetching active connections...');
                const activeResp = await fetch(`/connections/${createdUserIds[0]}/active`);
                const activeData = await activeResp.json();
                assert(activeResp.ok, `Failed to fetch active connections: ${JSON.stringify(activeData)}`);
                assert(Array.isArray(activeData.items), 'Active connections response missing items array');
                assert(activeData.items.length === 2, `Expected 2 active connections, got ${activeData.items.length}`);
                const activeIds = activeData.items.map(c => c.personPreview && c.personPreview.id);
                assert(activeIds.includes(createdUserIds[1]), `User 1 not found in active connections`);
                assert(activeIds.includes(createdUserIds[2]), `User 2 not found in active connections`);
                good('User 0 has 2 active connections: user 1 and user 2.');

                // 7. Each user creates a post
                info('====================================================================');
                info('SECTION 7: EACH USER CREATES A POST');
                info('====================================================================');
                const postIds = [];
                for (let i = 0; i < 3; i++) {
                    setOverrideUserCookie(createdUserIds[i]);
                    const userName = `Integration_Test${i}`;
                    info(`User ${i} creating post...`);
                    const postResp = await fetch('/posts', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({ content: `Post by ${userName}` })
                    });
                    const postData = await postResp.json();
                    assert(postResp.ok, `User ${i} failed to create post: ${JSON.stringify(postData)}`);
                    postIds.push(postData.id);
                    good(`User ${i} created post: ${postData.id}`);
                }

                // 8. Each user comments on the first post only
                info('====================================================================');
                info('SECTION 8: EACH USER COMMENTS ON FIRST POST');
                info('====================================================================');
                const rootCommentIdsByPost = {};
                rootCommentIdsByPost[postIds[0]] = [];
                for (let i = 0; i < 3; i++) {
                    setOverrideUserCookie(createdUserIds[i]);
                    const userName = `Integration_Test${i}`;
                    info(`User ${i} commenting on post ${postIds[0]}...`);
                    const commentResp = await fetch(`/comments/${createdUserIds[0]}/${postIds[0]}`, {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({ content: `Comment by ${userName} on post ${postIds[0]}` })
                    });
                    const commentData = await commentResp.json();
                    assert(commentResp.ok, `User ${i} failed to comment on post ${postIds[0]}: ${JSON.stringify(commentData)}`);
                    good(`User ${i} commented on post ${postIds[0]}: ${commentData.id}`);
                    rootCommentIdsByPost[postIds[0]].push(commentData.id);
                }
                let sec_wait = 2;
                info(`Waiting ${sec_wait} seconds before fetching posts to allow for comment count updates...`);
                await new Promise(resolve => setTimeout(resolve, sec_wait * 1000));


                // Verify post commentsCount after root comments creation
                info('Verifying post commentsCount after root comments...');
                const postAfterRootCommentsResp = await fetch(`/posts/${createdUserIds[0]}/${postIds[0]}`);
                const postAfterRootCommentsData = await postAfterRootCommentsResp.json();
                assert(postAfterRootCommentsResp.ok, `Failed to fetch post after root comments: ${JSON.stringify(postAfterRootCommentsData)}`);
                const postAfterRootComments = postAfterRootCommentsData.items[0];
                assert(postAfterRootComments.commentsCount === 3, `Expected post commentsCount to be 3 after root comments, got: ${postAfterRootComments.commentsCount}`);
                good(`Post commentsCount correctly updated to ${postAfterRootComments.commentsCount} after root comments creation`);

                // 9. Each user posts a sub-comment on the first root comment of the first post only
                info('====================================================================');
                info('SECTION 9: EACH USER POSTS SUB-COMMENTS');
                info('====================================================================');
                const firstPostId = postIds[0];
                const firstRootCommentId = rootCommentIdsByPost[firstPostId][0];
                // Track sub-comment IDs by owner
                const subCommentIdsByOwner = {};
                for (let i = 0; i < 3; i++) {
                    setOverrideUserCookie(createdUserIds[i]);
                    const userName = `Integration_Test${i}`;
                    info(`User ${i} sub-commenting on post ${firstPostId}, root comment ${firstRootCommentId}...`);
                    const subCommentResp = await fetch(`/comments/${createdUserIds[0]}/${firstPostId}/${firstRootCommentId}`, {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({ content: `Sub-comment by ${userName} on post ${firstPostId}, root comment ${firstRootCommentId}` })
                    });
                    const subCommentData = await subCommentResp.json();
                    assert(subCommentResp.ok, `User ${i} failed to sub-comment on post ${firstPostId}, root comment ${firstRootCommentId}: ${JSON.stringify(subCommentData)}`);
                    good(`User ${i} sub-commented on post ${firstPostId}, root comment ${firstRootCommentId}: ${subCommentData.id}`);
                    subCommentIdsByOwner[createdUserIds[i]] = subCommentData.id;
                }
                sec_wait = 2;
                info(`Waiting ${sec_wait} seconds before fetching posts and comments  to allow for comment count updates...`);
                await new Promise(resolve => setTimeout(resolve, sec_wait * 1000));

                // 9b. Each user posts a sub-sub-comment on the first sub-comment
                info('====================================================================');
                info('SECTION 9B: EACH USER POSTS SUB-SUB-COMMENTS ON FIRST SUB-COMMENT');
                info('====================================================================');
                const firstSubCommentIdForSubSub = subCommentIdsByOwner[createdUserIds[0]]; // First sub-comment created by user 0
                // Track sub-sub-comment IDs by owner
                const subSubCommentIdsByOwner = {};
                for (let i = 0; i < 3; i++) {
                    setOverrideUserCookie(createdUserIds[i]);
                    const userName = `Integration_Test${i}`;
                    info(`User ${i} sub-sub-commenting on post ${firstPostId}, sub-comment ${firstSubCommentIdForSubSub}...`);
                    const subSubCommentResp = await fetch(`/comments/${createdUserIds[0]}/${firstPostId}/${firstSubCommentIdForSubSub}`, {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({ content: `Sub-sub-comment by ${userName} on post ${firstPostId}, sub-comment ${firstSubCommentIdForSubSub}` })
                    });
                    const subSubCommentData = await subSubCommentResp.json();
                    assert(subSubCommentResp.ok, `User ${i} failed to sub-sub-comment on post ${firstPostId}, sub-comment ${firstSubCommentIdForSubSub}: ${JSON.stringify(subSubCommentData)}`);
                    good(`User ${i} sub-sub-commented on post ${firstPostId}, sub-comment ${firstSubCommentIdForSubSub}: ${subSubCommentData.id}`);
                    subSubCommentIdsByOwner[createdUserIds[i]] = subSubCommentData.id;
                }
                sec_wait = 2;
                info(`Waiting ${sec_wait} seconds before fetching posts and comments to allow for comment count updates...`);
                await new Promise(resolve => setTimeout(resolve, sec_wait * 1000));

                // Verify post commentsCount after sub-sub-comments creation
                info('Verifying post commentsCount after sub-sub-comments...');
                const postAfterSubSubCommentsResp = await fetch(`/posts/${createdUserIds[0]}/${postIds[0]}`);
                const postAfterSubSubCommentsData = await postAfterSubSubCommentsResp.json();
                assert(postAfterSubSubCommentsResp.ok, `Failed to fetch post after sub-sub-comments: ${JSON.stringify(postAfterSubSubCommentsData)}`);
                const postAfterSubSubComments = postAfterSubSubCommentsData.items[0];
                assert(postAfterSubSubComments.commentsCount === 9, `Expected post commentsCount to be 9 after sub-sub-comments (3 root + 3 sub + 3 sub-sub), got: ${postAfterSubSubComments.commentsCount}`);
                good(`Post commentsCount correctly updated to ${postAfterSubSubComments.commentsCount} after sub-sub-comments creation`);

                // Verify first root comment commentsCount after sub-sub-comments creation (should be 6: 3 sub + 3 sub-sub)
                info('Verifying first root comment commentsCount after sub-sub-comments...');
                const rootCommentsAfterSubSubResp = await fetch(`/comments/${postIds[0]}`);
                const rootCommentsAfterSubSubData = await rootCommentsAfterSubSubResp.json();
                assert(rootCommentsAfterSubSubResp.ok, `Failed to fetch root comments after sub-sub-comments: ${JSON.stringify(rootCommentsAfterSubSubData)}`);
                const firstRootCommentAfterSubSub = rootCommentsAfterSubSubData.items.find(c => c.id === firstRootCommentId);
                assert(firstRootCommentAfterSubSub, 'First root comment not found in response');
                assert(firstRootCommentAfterSubSub.commentsCount === 6, `Expected first root comment commentsCount to be 6 after sub-sub-comments (3 sub + 3 sub-sub), got: ${firstRootCommentAfterSubSub.commentsCount}`);
                good(`First root comment commentsCount correctly updated to ${firstRootCommentAfterSubSub.commentsCount} after sub-sub-comments creation`);

                // Verify first sub-comment commentsCount after sub-sub-comments creation (should be 3)
                info('Verifying first sub-comment commentsCount after sub-sub-comments...');
                const subCommentsAfterSubSubResp = await fetch(`/comments/${postIds[0]}/${firstRootCommentId}`);
                const subCommentsAfterSubSubData = await subCommentsAfterSubSubResp.json();
                assert(subCommentsAfterSubSubResp.ok, `Failed to fetch sub-comments after sub-sub-comments: ${JSON.stringify(subCommentsAfterSubSubData)}`);
                const firstSubCommentAfterSubSub = subCommentsAfterSubSubData.items.find(c => c.id === firstSubCommentIdForSubSub);
                assert(firstSubCommentAfterSubSub, 'First sub-comment not found in response');
                assert(firstSubCommentAfterSubSub.commentsCount === 3, `Expected first sub-comment commentsCount to be 3 after sub-sub-comments, got: ${firstSubCommentAfterSubSub.commentsCount}`);
                good(`First sub-comment commentsCount correctly updated to ${firstSubCommentAfterSubSub.commentsCount} after sub-sub-comments creation`);

                // Verify other posts still have commentsCount of 0 (only first post received comments)
                info('Verifying other posts still have commentsCount of 0...');
                for (let i = 1; i < 3; i++) {
                    const otherPostResp = await fetch(`/posts/${createdUserIds[i]}/${postIds[i]}`);
                    const otherPostData = await otherPostResp.json();
                    assert(otherPostResp.ok, `Failed to fetch post ${i}: ${JSON.stringify(otherPostData)}`);
                    const otherPost = otherPostData.items[0];
                    assert(otherPost.commentsCount === 0, `Expected post ${i} commentsCount to be 0, got: ${otherPost.commentsCount}`);
                    good(`Post ${i} commentsCount correctly remains 0 (no comments added)`);
                }

                // Verify other root comments still have commentsCount of 0 (only first root comment received sub-comments)
                info('Verifying other root comments still have commentsCount of 0...');
                const otherRootCommentIds = rootCommentIdsByPost[postIds[0]].slice(1); // Get root comments 1 and 2
                for (let i = 0; i < otherRootCommentIds.length; i++) {
                    const otherRootCommentId = otherRootCommentIds[i];
                    const otherRootComment = rootCommentsAfterSubSubData.items.find(c => c.id === otherRootCommentId);
                    assert(otherRootComment, `Root comment ${i + 1} not found in response`);
                    assert(otherRootComment.commentsCount === 0, `Expected root comment ${i + 1} commentsCount to be 0, got: ${otherRootComment.commentsCount}`);
                    good(`Root comment ${i + 1} commentsCount correctly remains 0 (no sub-comments added)`);
                }

                // Verify other sub-comments still have commentsCount of 0 (only first sub-comment received sub-sub-comments)
                info('Verifying other sub-comments still have commentsCount of 0...');
                const otherSubCommentIds = Object.values(subCommentIdsByOwner).slice(1); // Get sub-comments 1 and 2
                for (let i = 0; i < otherSubCommentIds.length; i++) {
                    const otherSubCommentId = otherSubCommentIds[i];
                    const otherSubComment = subCommentsAfterSubSubData.items.find(c => c.id === otherSubCommentId);
                    assert(otherSubComment, `Sub-comment ${i + 1} not found in response`);
                    assert(otherSubComment.commentsCount === 0, `Expected sub-comment ${i + 1} commentsCount to be 0, got: ${otherSubComment.commentsCount}`);
                    good(`Sub-comment ${i + 1} commentsCount correctly remains 0 (no sub-sub-comments added)`);
                }

                // Prepare variables for deletion tests
                let firstPostOwner = createdUserIds[0];
                let secondPostOwner = createdUserIds[1];
                let thirdPostOwner = createdUserIds[2];
                let firstPostIdForDelete = postIds[0];
                let secondPostIdForDelete = postIds[1];
                let thirdPostIdForDelete = postIds[2];
                let firstRootCommentIdForDelete = rootCommentIdsByPost[firstPostId][0];
                let secondRootCommentIdForDelete = rootCommentIdsByPost[firstPostId][1];
                let thirdRootCommentIdForDelete = rootCommentIdsByPost[firstPostId][2];
                let firstSubCommentId = subCommentIdsByOwner[firstPostOwner];
                let secondSubCommentId = subCommentIdsByOwner[secondPostOwner];
                let thirdSubCommentId = subCommentIdsByOwner[thirdPostOwner];

                // 10. Basic post reaction tests
                info('====================================================================');
                info('SECTION 10: BASIC POST REACTION TESTS');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                // User 0 likes post 2
                info('User 0 liking post 2...');
                let reactRespR = await fetch(`/reactions/post/${createdUserIds[2]}/${postIds[2]}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                let reactDataR = await reactRespR.json();
                assert(reactRespR.ok, `User 0 failed to like post 2: ${JSON.stringify(reactDataR)}`);
                good('User 0 liked post 2.');
                // Fetch post 2 and verify reaction fields
                let post2RespR = await fetch(`/posts/${createdUserIds[2]}/${postIds[2]}`);
                let post2DataR = await post2RespR.json();
                assert(post2RespR.ok, `Failed to fetch post 2: ${JSON.stringify(post2DataR)}`);
                info('Fetched post 2 after like: ' + JSON.stringify(post2DataR));
                const post2 = post2DataR.items[0];
                assert(post2.reactionType === 'like', `User 0 should see reactionType "like" on post 2, got: ${JSON.stringify(post2.reactionType)}`);
                const post2LikeCount = (post2.reactionCounts && typeof post2.reactionCounts.like === 'number') ? post2.reactionCounts.like : post2.reactionsCount;
                assert(post2LikeCount === 1, `Expected like count 1 on post 2, got: ${post2LikeCount}`);

                // User 0 unlikes post 2
                info('User 0 unliking post 2...');
                let unlikeRespR = await fetch(`/reactions/post/${createdUserIds[2]}/${postIds[2]}`, { method: 'DELETE' });
                let unlikeDataR = await unlikeRespR.json();
                assert(unlikeRespR.ok, `User 0 failed to unlike post 2: ${JSON.stringify(unlikeDataR)}`);
                post2RespR = await fetch(`/posts/${createdUserIds[2]}/${postIds[2]}`);
                post2DataR = await post2RespR.json();
                info('Fetched post 2 after unlike: ' + JSON.stringify(post2DataR));
                const post2AfterUnlike = post2DataR.items[0];
                assert(post2AfterUnlike.reactionType === null, `reactionType should be null after unlike on post 2, got: ${JSON.stringify(post2AfterUnlike.reactionType)}`);
                const post2LikeCountAfter = (post2AfterUnlike.reactionCounts && typeof post2AfterUnlike.reactionCounts.like === 'number') ? post2AfterUnlike.reactionCounts.like : post2AfterUnlike.reactionsCount;
                assert(post2LikeCountAfter === 0, `Reactions count should decrease to 0 after unlike on post 2, got: ${post2LikeCountAfter}`);

                // 11. Multi-user post reaction tests
                info('====================================================================');
                info('SECTION 11: MULTI-USER POST REACTION TESTS');
                info('====================================================================');
                const rootCommentId2R = rootCommentIdsByPost[postIds[0]][2];
                // User 0 likes post 2
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 liking post 2...');
                let resp = await fetch(`/reactions/post/${createdUserIds[2]}/${postIds[2]}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                if (!resp.ok) {
                    let errorData = await resp.json().catch(() => ({}));
                    info('User 0 like post 2 failed, response: ' + JSON.stringify(errorData));
                }
                // User 1 likes post 2
                setOverrideUserCookie(createdUserIds[1]);
                info('User 1 liking post 2...');
                resp = await fetch(`/reactions/post/${createdUserIds[2]}/${postIds[2]}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                if (!resp.ok) {
                    let errorData = await resp.json().catch(() => ({}));
                    info('User 1 like post 2 failed, response: ' + JSON.stringify(errorData));
                }
                // Verify two likes on post 2
                resp = await fetch(`/reactions/${postIds[2]}`);
                let data = await resp.json();
                if (!resp.ok) {
                    info('Failed to get reactions for post 2, response: ' + JSON.stringify(data));
                }
                assert(resp.ok, 'Failed to get reactions for post 2');
                assert(Array.isArray(data.items), 'Reactions response missing items array');
                assert(data.items.length === 2, `Should be 2 likes on post 2, got ${data.items.length}`);
                good(`Reactions for post 2 received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 2 likes)`);

                // User 1 unlikes post 2
                setOverrideUserCookie(createdUserIds[1]);
                info('User 1 unliking post 2...');
                resp = await fetch(`/reactions/post/${createdUserIds[2]}/${postIds[2]}`, { method: 'DELETE' });
                assert(resp.ok, 'User 1 unlike post 2 failed');
                // Verify one like remaining on post 2
                resp = await fetch(`/reactions/${postIds[2]}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for post 2 after unlike');
                assert(Array.isArray(data.items), 'Reactions response missing items array after unlike');
                assert(data.items.length === 1, `Should be 1 like on post 2 after unlike, got ${data.items.length}`);
                good(`Reactions for post 2 after unlike received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 1 like)`);

                // User 1 re-likes post 2
                setOverrideUserCookie(createdUserIds[1]);
                info('User 1 re-liking post 2...');
                resp = await fetch(`/reactions/post/${createdUserIds[2]}/${postIds[2]}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                assert(resp.ok, 'User 1 re-like post 2 failed');
                // Verify two likes on post 2 after re-like
                resp = await fetch(`/reactions/${postIds[2]}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for post 2 after re-like');
                assert(Array.isArray(data.items), 'Reactions response missing items array after re-like');
                assert(data.items.length === 2, `Should be 2 likes on post 2 after re-like, got ${data.items.length}`);
                good(`Reactions for post 2 after re-like received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 2 likes)`);

                // 12. Multi-user comment reaction tests
                info('====================================================================');
                info('SECTION 12: MULTI-USER COMMENT REACTION TESTS');
                info('====================================================================');
                // User 0 likes root comment 2
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 liking root comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${rootCommentId2R}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                assert(resp.ok, 'User 0 like root comment 2 failed');
                // User 2 likes root comment 2
                setOverrideUserCookie(createdUserIds[2]);
                info('User 2 liking root comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${rootCommentId2R}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                assert(resp.ok, 'User 2 like root comment 2 failed');
                // Verify two likes on root comment 2
                resp = await fetch(`/reactions/${rootCommentId2R}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for root comment 2');
                assert(Array.isArray(data.items), 'Reactions response missing items array for comment');
                assert(data.items.length === 2, `Should be 2 likes on root comment 2, got ${data.items.length}`);
                good(`Reactions for root comment 2 received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 2 likes)`);

                // User 2 unlikes root comment 2
                setOverrideUserCookie(createdUserIds[2]);
                info('User 2 unliking root comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${rootCommentId2R}`, { method: 'DELETE' });
                assert(resp.ok, 'User 2 unlike root comment 2 failed');
                // Verify one like remaining on root comment 2
                resp = await fetch(`/reactions/${rootCommentId2R}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for root comment 2 after unlike');
                assert(Array.isArray(data.items), 'Reactions response missing items array after unlike for comment');
                assert(data.items.length === 1, `Should be 1 like on root comment 2 after unlike, got ${data.items.length}`);
                good(`Reactions for root comment 2 after unlike received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 1 like)`);

                // User 2 re-likes root comment 2
                setOverrideUserCookie(createdUserIds[2]);
                info('User 2 re-liking root comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${rootCommentId2R}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                assert(resp.ok, 'User 2 re-like root comment 2 failed');
                // Verify two likes on root comment 2 after re-like
                resp = await fetch(`/reactions/${rootCommentId2R}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for root comment 2 after re-like');
                assert(Array.isArray(data.items), 'Reactions response missing items array after re-like for comment');
                assert(data.items.length === 2, `Should be 2 likes on root comment 2 after re-like, got ${data.items.length}`);
                good(`Reactions for root comment 2 after re-like received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 2 likes)`);

                // 13. Multi-user sub-comment reaction tests
                // Note: These reactions will persist and be verified in later sections
                info('====================================================================');
                info('SECTION 13: MULTI-USER SUB-COMMENT REACTION TESTS');
                info('====================================================================');
                // Use sub-comment created by user 2
                const subCommentId2 = subCommentIdsByOwner[createdUserIds[2]];
                // User 0 likes sub-comment 2
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 liking sub-comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${subCommentId2}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                assert(resp.ok, 'User 0 like sub-comment 2 failed');
                // User 1 likes sub-comment 2
                setOverrideUserCookie(createdUserIds[1]);
                info('User 1 liking sub-comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${subCommentId2}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                assert(resp.ok, 'User 1 like sub-comment 2 failed');
                // Verify two likes on sub-comment 2
                resp = await fetch(`/reactions/${subCommentId2}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for sub-comment 2');
                assert(Array.isArray(data.items), 'Reactions response missing items array for sub-comment');
                assert(data.items.length === 2, `Should be 2 likes on sub-comment 2, got ${data.items.length}`);
                good(`Reactions for sub-comment 2 received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 2 likes)`);

                // User 1 unlikes sub-comment 2
                setOverrideUserCookie(createdUserIds[1]);
                info('User 1 unliking sub-comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${subCommentId2}`, { method: 'DELETE' });
                assert(resp.ok, 'User 1 unlike sub-comment 2 failed');
                // Verify one like remaining on sub-comment 2
                resp = await fetch(`/reactions/${subCommentId2}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for sub-comment 2 after unlike');
                assert(Array.isArray(data.items), 'Reactions response missing items array after unlike for sub-comment');
                assert(data.items.length === 1, `Should be 1 like on sub-comment 2 after unlike, got ${data.items.length}`);
                good(`Reactions for sub-comment 2 after unlike received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 1 like)`);

                // User 1 re-likes sub-comment 2
                setOverrideUserCookie(createdUserIds[1]);
                info('User 1 re-liking sub-comment 2...');
                resp = await fetch(`/reactions/comment/${postIds[0]}/${subCommentId2}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ type: 'like' })
                });
                assert(resp.ok, 'User 1 re-like sub-comment 2 failed');
                // Verify two likes on sub-comment 2 after re-like
                resp = await fetch(`/reactions/${subCommentId2}`);
                data = await resp.json();
                assert(resp.ok, 'Failed to get reactions for sub-comment 2 after re-like');
                assert(Array.isArray(data.items), 'Reactions response missing items array after re-like for sub-comment');
                assert(data.items.length === 2, `Should be 2 likes on sub-comment 2 after re-like, got ${data.items.length}`);
                good(`Reactions for sub-comment 2 after re-like received: ${JSON.stringify(data.items)} (Assertions: resp.ok, items array, 2 likes)`);

                // 14. Fetch feed and verify posts order and reaction fields
                info('====================================================================');
                info('SECTION 14: FETCH FEED AND VERIFY POST ORDER AND REACTIONS');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 fetching feed...');
                const feedResp = await fetch('/feed');
                const feedData = await feedResp.json();
                // Log all posts before assertions
                info('Feed post list (before assertions):');
                if (Array.isArray(feedData.items)) {
                    for (const post of feedData.items) {
                        info(`\tID: ${post.id}, content: ${post.content}, created_at: ${post.createdAt}`);
                        // Verify reaction fields exist and are valid
                        assert('reactionType' in post, `Post ${post.id} missing reactionType field`);
                        assert('reactionCounts' in post, `Post ${post.id} missing reactionCounts field`);
                        assert(typeof post.reactionCounts === 'object' && post.reactionCounts !== null, `Post ${post.id} reactionCounts is not an object`);
                        assert('like' in post.reactionCounts, `Post ${post.id} reactionCounts missing 'like' field`);
                        assert(typeof post.reactionCounts.like === 'number', `Post ${post.id} reactionCounts.like is not a number`);
                        // Check post 2 reaction counts (should have 2 likes with user 0 liked)
                        if (post.id === postIds[2]) {
                            assert(post.reactionCounts.like === 2, `Expected like count 2 for post 2, got: ${post.reactionCounts.like}`);
                            assert(post.reactionType === 'like', `Expected reactionType 'like' for post 2 for user 0, got: ${JSON.stringify(post.reactionType)}`);
                        } else {
                            assert(post.reactionCounts.like === 0, `Expected like count 0 for post ${post.id} for user 0, got: ${post.reactionCounts.like}`);
                            assert(post.reactionType === null, `Expected reactionType null for post ${post.id} for user 0, got: ${JSON.stringify(post.reactionType)}`);
                        }
                    }
                } else {
                    info('Feed data.items is not an array: ' + JSON.stringify(feedData));
                }
                assert(feedResp.ok, `Failed to fetch feed: ${JSON.stringify(feedData)}`);
                assert(Array.isArray(feedData.items), 'Feed response missing items array');
                assert(feedData.items.length === 3, `Expected 3 posts in feed, got ${feedData.items.length}`);
                // Verify post order (newest to oldest)
                const expectedOrder = [...postIds].reverse();
                const actualOrder = feedData.items.map(p => p.id);
                assert(JSON.stringify(actualOrder) === JSON.stringify(expectedOrder), `Feed order incorrect. Expected: ${JSON.stringify(expectedOrder)}, got: ${JSON.stringify(actualOrder)}`);
                good('Feed contains all posts in correct (newest-to-oldest) order.');

                // 15. Test post deletion authorization (should fail)
                info('====================================================================');
                info('SECTION 15: TEST POST DELETION AUTHORIZATION (SHOULD FAIL)');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 attempting to delete post created by user 1 (should fail)...');
                let deleteResp = await fetch(`/posts/${createdUserIds[1]}/${postIds[1]}`, { method: 'DELETE' });
                let deleteData = await deleteResp.json();
                assert(deleteResp.status === 403, `Expected HTTP 403 Forbidden, got: ${deleteResp.status}`);
                assert(deleteData.error_code && deleteData.error_code === 'FORBIDDEN', `Expected error_code 'FORBIDDEN', got: ${JSON.stringify(deleteData)}`);
                good('User 0 failed to delete user 1 post as expected (authorization error).');

                // 16. Test post deletion by owner (should succeed)
                info('====================================================================');
                info('SECTION 16: TEST POST DELETION BY OWNER (SHOULD SUCCEED)');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[1]);
                info('User 1 deleting their own post...');
                deleteResp = await fetch(`/posts/${createdUserIds[1]}/${postIds[1]}`, { method: 'DELETE' });
                deleteData = await deleteResp.json();
                assert(deleteResp.ok, `User 1 failed to delete their own post: ${JSON.stringify(deleteData)}`);
                good('User 1 successfully soft-deleted their own post.');

                // 17. Verify soft-deleted post appears in feed with correct fields
                info('====================================================================');
                info('SECTION 17: VERIFY SOFT-DELETED POST IN FEED');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 fetching feed after soft-delete...');
                const feedResp2 = await fetch('/feed');
                const feedData2 = await feedResp2.json();
                // Log all posts before assertions
                info('Feed post list after soft-delete (before assertions):');
                if (Array.isArray(feedData2.items)) {
                    for (const post of feedData2.items) {
                        info(`\tID: ${post.id}, content: ${post.content}, created_at: ${post.createdAt}, deleted: ${post.deleted}, deletedAt: ${post.deletedAt}, deletedBy: ${post.deletedBy}`);
                    }
                } else {
                    info('Feed data.items is not an array: ' + JSON.stringify(feedData2));
                }
                assert(feedResp2.ok, `Failed to fetch feed after delete: ${JSON.stringify(feedData2)}`);
                assert(Array.isArray(feedData2.items), 'Feed response missing items array after delete');
                assert(feedData2.items.length === 3, `Expected 3 posts in feed after delete, got ${feedData2.items.length}`);
                // Verify soft-deleted post fields
                const deletedPost = feedData2.items.find(p => p.id === postIds[1]);
                assert(deletedPost, 'Soft-deleted post not found in feed');
                assert(deletedPost.content === '', 'Soft-deleted post content should be empty string');
                assert(deletedPost.deleted === true, 'Soft-deleted post deleted field should be true');
                assert(typeof deletedPost.deletedAt === 'string' && deletedPost.deletedAt.length > 0, 'Soft-deleted post deletedAt should be a non-empty string');
                assert(typeof deletedPost.deletedBy === 'string' && deletedPost.deletedBy.length > 0, 'Soft-deleted post deletedBy should be a non-empty string');
                good('Soft-deleted post is present in feed with correct fields.');

                // 18. Test root comment deletion and authorization
                info('====================================================================');
                info('SECTION 18: TEST ROOT COMMENT DELETION AND AUTHORIZATION');
                info('====================================================================');
                // Fetch all root comments for the first post
                info('Fetching root comments for first post...');
                let rootCommentsResp = await fetch(`/comments/${postIds[0]}`);
                let rootCommentsData = await rootCommentsResp.json();
                // Log all root comments before assertions
                info('Root comments list (before assertions):');
                if (Array.isArray(rootCommentsData.items)) {
                    for (const comment of rootCommentsData.items) {
                        info(`\tID: ${comment.id}, content: ${comment.content}, created_at: ${comment.createdAt}, deleted: ${comment.deleted}`);
                        assert('reactionType' in comment, `Root comment ${comment.id} missing reactionType field`);
                        assert('reactionCounts' in comment, `Root comment ${comment.id} missing reactionCounts field`);
                        assert(typeof comment.reactionCounts === 'object' && comment.reactionCounts !== null, `Root comment ${comment.id} reactionCounts is not an object`);
                        assert('like' in comment.reactionCounts, `Root comment ${comment.id} reactionCounts missing 'like' field`);
                        assert(typeof comment.reactionCounts.like === 'number', `Root comment ${comment.id} reactionCounts.like is not a number`);
                        // Check root comment 2 reaction counts (should have 2 likes with user 0 liked)
                        if (comment.id === rootCommentId2R) {
                            assert(comment.reactionCounts.like === 2, `Expected like count 2 for root comment 2, got: ${comment.reactionCounts.like}`);
                            assert(comment.reactionType === 'like', `Expected reactionType 'like' for root comment 2 for user 0, got: ${JSON.stringify(comment.reactionType)}`);
                        } else {
                            assert(comment.reactionCounts.like === 0, `Expected like count 0 for root comment ${comment.id} for user 0, got: ${comment.reactionCounts.like}`);
                            assert(comment.reactionType === null, `Expected reactionType null for root comment ${comment.id} for user 0, got: ${JSON.stringify(comment.reactionType)}`);
                        }
                    }
                } else {
                    info('Root comments data.items is not an array: ' + JSON.stringify(rootCommentsData));
                }
                assert(rootCommentsResp.ok, `Failed to fetch root comments: ${JSON.stringify(rootCommentsData)}`);
                assert(Array.isArray(rootCommentsData.items), 'Root comments response missing items array');
                assert(rootCommentsData.items.length === 3, `Expected 3 root comments, got ${rootCommentsData.items.length}`);
                // Verify comment order (newest to oldest)
                const expectedCommentOrder = [...rootCommentIdsByPost[postIds[0]]].reverse();
                const actualCommentOrder = rootCommentsData.items.map(c => c.id);
                assert(JSON.stringify(actualCommentOrder) === JSON.stringify(expectedCommentOrder), `Root comment order incorrect. Expected: ${JSON.stringify(expectedCommentOrder)}, got: ${JSON.stringify(actualCommentOrder)}`);
                good('Root comments are in correct (newest-to-oldest) order.');

                // Test root comment deletion authorization (should fail)
                setOverrideUserCookie(createdUserIds[2]);
                info('User 2 attempting to delete root comment owned by user 0 (should fail)...');
                let delCommentResp = await fetch(`/comments/${postIds[0]}/${firstRootCommentIdForDelete}`, { method: 'DELETE' });
                let delCommentData = await delCommentResp.json();
                assert(delCommentResp.status === 403, `Expected HTTP 403 Forbidden, got: ${delCommentResp.status}`);
                assert(delCommentData.error_code && delCommentData.error_code === 'FORBIDDEN', `Expected error_code 'FORBIDDEN', got: ${JSON.stringify(delCommentData)}`);
                good('User 2 failed to delete user 0 root comment as expected (authorization error).');

                // Test root comment deletion by owner (should succeed)
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 deleting their own root comment...');
                delCommentResp = await fetch(`/comments/${postIds[0]}/${firstRootCommentIdForDelete}`, { method: 'DELETE' });
                delCommentData = await delCommentResp.json();
                assert(delCommentResp.ok, `User 0 failed to delete their own root comment: ${JSON.stringify(delCommentData)}`);
                good('User 0 successfully soft-deleted their own root comment.');

                // Verify soft-deleted root comment fields
                info('Fetching root comments after soft-delete...');
                rootCommentsResp = await fetch(`/comments/${postIds[0]}`);
                rootCommentsData = await rootCommentsResp.json();
                // Log all root comments before assertions
                info('Root comments list after soft-delete (before assertions):');
                if (Array.isArray(rootCommentsData.items)) {
                    for (const comment of rootCommentsData.items) {
                        info(`\tID: ${comment.id}, content: ${comment.content}, created_at: ${comment.createdAt}, deleted: ${comment.deleted}, deletedAt: ${comment.deletedAt}, deletedBy: ${comment.deletedBy}`);
                    }
                } else {
                    info('Root comments data.items is not an array: ' + JSON.stringify(rootCommentsData));
                }
                assert(rootCommentsResp.ok, `Failed to fetch root comments after delete: ${JSON.stringify(rootCommentsData)}`);
                assert(Array.isArray(rootCommentsData.items), 'Root comments response missing items array after delete');
                assert(rootCommentsData.items.length === 3, `Expected 3 root comments after delete, got ${rootCommentsData.items.length}`);
                const deletedComment = rootCommentsData.items.find(c => c.id === firstRootCommentIdForDelete);
                assert(deletedComment, 'Soft-deleted root comment not found');
                assert(deletedComment.content === '', 'Soft-deleted root comment content should be empty string');
                assert(deletedComment.deleted === true, 'Soft-deleted root comment deleted field should be true');
                assert(typeof deletedComment.deletedAt === 'string' && deletedComment.deletedAt.length > 0, 'Soft-deleted root comment deletedAt should be a non-empty string');
                assert(typeof deletedComment.deletedBy === 'string' && deletedComment.deletedBy.length > 0, 'Soft-deleted root comment deletedBy should be a non-empty string');
                good('Soft-deleted root comment is present with correct fields.');

                // 19. Test sub-comment deletion and authorization
                info('====================================================================');
                info('SECTION 19: TEST SUB-COMMENT DELETION AND AUTHORIZATION');
                info('====================================================================');
                // Fetch all sub-comments for the first root comment
                info('Fetching sub-comments for first root comment of first post...');
                let subCommentsResp = await fetch(`/comments/${postIds[0]}/${firstRootCommentIdForDelete}`);
                let subCommentsData = await subCommentsResp.json();
                // Reference sub-comment 2 ID for reaction checks (same as from section 14)
                const subCommentId2ForChecks = subCommentIdsByOwner[createdUserIds[2]];
                // Log all sub-comments before assertions
                info('Sub-comments list (before assertions):');
                if (Array.isArray(subCommentsData.items)) {
                    for (const comment of subCommentsData.items) {
                        info(`\tID: ${comment.id}, content: ${comment.content}, created_at: ${comment.createdAt}, deleted: ${comment.deleted}`);
                        // Verify reaction fields exist and are valid
                        assert('reactionType' in comment, `Sub-comment ${comment.id} missing reactionType field`);
                        assert('reactionCounts' in comment, `Sub-comment ${comment.id} missing reactionCounts field`);
                        assert(typeof comment.reactionCounts === 'object' && comment.reactionCounts !== null, `Sub-comment ${comment.id} reactionCounts is not an object`);
                        // Verify all keys in reactionCounts are valid enum values and all values are integers
                        const validReactionTypes = ['like']; // Add more if your enum supports more
                        for (const [key, value] of Object.entries(comment.reactionCounts)) {
                            assert(validReactionTypes.includes(key), `Sub-comment ${comment.id} reactionCounts has invalid key: ${key}`);
                            assert(Number.isInteger(value), `Sub-comment ${comment.id} reactionCounts[${key}] is not an integer: ${value}`);
                        }
                        // Check sub-comment reactions based on section 14 results
                        // User 0 and User 1 both liked sub-comment 2 (created by User 2)
                        if (comment.id === subCommentId2ForChecks) {
                            assert(comment.reactionCounts.like === 2, `Expected like count 2 for sub-comment 2, got: ${comment.reactionCounts.like}`);
                            assert(comment.reactionType === 'like', `Expected reactionType 'like' for sub-comment 2 for user 0, got: ${JSON.stringify(comment.reactionType)}`);
                        } else {
                            assert(comment.reactionCounts.like === 0, `Expected like count 0 for sub-comment ${comment.id} for user 0, got: ${comment.reactionCounts.like}`);
                            assert(comment.reactionType === null, `Expected reactionType null for sub-comment ${comment.id} for user 0, got: ${JSON.stringify(comment.reactionType)}`);
                        }
                    }
                } else {
                    info('Sub-comments data.items is not an array: ' + JSON.stringify(subCommentsData));
                }
                assert(subCommentsResp.ok, `Failed to fetch sub-comments: ${JSON.stringify(subCommentsData)}`);
                assert(Array.isArray(subCommentsData.items), 'Sub-comments response missing items array');
                assert(subCommentsData.items.length === 3, `Expected 3 sub-comments, got ${subCommentsData.items.length}`);
                good('Sub-comments are present and count is correct with expected reaction state from section 14.');

                // Test sub-comment deletion authorization (should fail)
                const subCommentIdUser0 = subCommentIdsByOwner[createdUserIds[0]];
                setOverrideUserCookie(createdUserIds[2]);
                info('User 2 attempting to delete sub-comment owned by user 0 (should fail)...');
                let delSubCommentResp = await fetch(`/comments/${postIds[0]}/${subCommentIdUser0}`, { method: 'DELETE' });
                let delSubCommentData = await delSubCommentResp.json();
                assert(delSubCommentResp.status === 403, `Expected HTTP 403 Forbidden, got: ${delSubCommentResp.status}`);
                assert(delSubCommentData.error_code && delSubCommentData.error_code === 'FORBIDDEN', `Expected error_code 'FORBIDDEN', got: ${JSON.stringify(delSubCommentData)}`);
                good('User 2 failed to delete user 0 sub-comment as expected (authorization error).');

                // Test sub-comment deletion by owner (should succeed)
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 deleting their own sub-comment...');
                delSubCommentResp = await fetch(`/comments/${postIds[0]}/${subCommentIdUser0}`, { method: 'DELETE' });
                delSubCommentData = await delSubCommentResp.json();
                assert(delSubCommentResp.ok, `User 0 failed to delete their own sub-comment: ${JSON.stringify(delSubCommentData)}`);
                good('User 0 successfully soft-deleted their own sub-comment.');

                // Verify soft-deleted sub-comment fields
                info('Fetching sub-comments after soft-delete...');
                subCommentsResp = await fetch(`/comments/${postIds[0]}/${firstRootCommentIdForDelete}`);
                subCommentsData = await subCommentsResp.json();
                // Log all sub-comments before assertions
                info('Sub-comments list after soft-delete (before assertions):');
                if (Array.isArray(subCommentsData.items)) {
                    for (const comment of subCommentsData.items) {
                        info(`\tID: ${comment.id}, content: ${comment.content}, created_at: ${comment.createdAt}, deleted: ${comment.deleted}, deletedAt: ${comment.deletedAt}, deletedBy: ${comment.deletedBy}`);
                    }
                } else {
                    info('Sub-comments data.items is not an array: ' + JSON.stringify(subCommentsData));
                }
                assert(subCommentsResp.ok, `Failed to fetch sub-comments after delete: ${JSON.stringify(subCommentsData)}`);
                assert(Array.isArray(subCommentsData.items), 'Sub-comments response missing items array after delete');
                assert(subCommentsData.items.length === 3, `Expected 3 sub-comments after delete, got ${subCommentsData.items.length}`);
                const deletedSubComment = subCommentsData.items.find(c => c.id === subCommentIdUser0);
                assert(deletedSubComment, 'Soft-deleted sub-comment not found');
                assert(deletedSubComment.content === '', 'Soft-deleted sub-comment content should be empty string');
                assert(deletedSubComment.deleted === true, 'Soft-deleted sub-comment deleted field should be true');
                assert(typeof deletedSubComment.deletedAt === 'string' && deletedSubComment.deletedAt.length > 0, 'Soft-deleted sub-comment deletedAt should be a non-empty string');
                assert(typeof deletedSubComment.deletedBy === 'string' && deletedSubComment.deletedBy.length > 0, 'Soft-deleted sub-comment deletedBy should be a non-empty string');
                good('Soft-deleted sub-comment is present with correct fields.');

                // 20. Test connection removal
                info('====================================================================');
                info('SECTION 20: TEST CONNECTION REMOVAL');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info('User 0 removing connection with user 2...');
                let removeConnResp = await fetch(`/connections/${createdUserIds[2]}`, {
                    method: 'PATCH',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ status: 'removed' })
                });
                let removeConnData = await removeConnResp.json();
                assert(removeConnResp.ok, `User 0 failed to remove connection with user 2: ${JSON.stringify(removeConnData)}`);
                good('User 0 successfully removed connection with user 2.');

                // 21. Verify connection fields after removal
                info('====================================================================');
                info('SECTION 21: VERIFY CONNECTION FIELDS AFTER REMOVAL');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);
                info('Fetching user 2 after removing connection...');
                let user2AfterRemoveResp = await fetch(`/people/${createdUserIds[2]}`);
                let user2AfterRemoveData = await user2AfterRemoveResp.json();
                assert(user2AfterRemoveResp.ok, `Failed to fetch user 2 after connection removal: ${JSON.stringify(user2AfterRemoveData)}`);
                // Verify connectionStatus and connectionRequestee
                assert(user2AfterRemoveData.connectionStatus === 'removed', `User 2 connectionStatus incorrect after removal: got '${user2AfterRemoveData.connectionStatus}', expected 'removed'`);
                good(`connectionStatus is correct after removal (value: ${user2AfterRemoveData.connectionStatus})`);
                assert(user2AfterRemoveData.connectionRequestee === false, `User 2 connectionRequestee incorrect after removal: got '${user2AfterRemoveData.connectionRequestee}', expected false`);
                good(`connectionRequestee is correct after removal (value: ${user2AfterRemoveData.connectionRequestee})`);

                // 22. Verify feed excludes disconnected user's posts
                info('====================================================================');
                info('SECTION 22: VERIFY FEED EXCLUDES DISCONNECTED USER POSTS');
                info('====================================================================');
                sec_wait = 2;
                info(`Waiting ${sec_wait} seconds before fetching feed after removing connection to allow for eventual consistency...`);
                await new Promise(resolve => setTimeout(resolve, sec_wait * 1000));
                info('User 0 fetching feed after removing connection with user 2...');
                const feedResp3 = await fetch('/feed');
                const feedData3 = await feedResp3.json();
                assert(feedResp3.ok, `Failed to fetch feed after removing connection: ${JSON.stringify(feedData3)}`);
                assert(Array.isArray(feedData3.items), 'Feed response missing items array after removing connection');
                // User 2's post should not be present
                const user2Post = feedData3.items.find(p => p.id === postIds[2]);
                assert(!user2Post, 'Post of disconnected user 2 should not be present in feed after connection removal');
                good('Feed does not contain post of disconnected user 2 after connection removal.');

                // 23. Test redirect endpoints for backward compatibility
                info('====================================================================');
                info('SECTION 23: TEST REDIRECT ENDPOINTS FOR BACKWARD COMPATIBILITY');
                info('====================================================================');
                setOverrideUserCookie(createdUserIds[0]);

                // Test connection redirects by verifying they work (follow redirects automatically)
                info('Testing connection redirect endpoints...');

                // Test GET /connections/active redirect - should return user 0's connections
                info('Testing GET /connections/active redirect...');
                const connActiveResp = await fetch('/connections/active');
                const connActiveData = await connActiveResp.json();
                assert(connActiveResp.ok, `GET /connections/active failed: ${JSON.stringify(connActiveData)}`);
                assert(Array.isArray(connActiveData.items), 'GET /connections/active should return items array');
                // User 0 has 1 active connection (with User 1) - connection with User 2 was removed in Section 21
                assert(connActiveData.items.length === 1, `Expected 1 active connection for user 0, got ${connActiveData.items.length}`);
                assert(connActiveData.items[0].personPreview.id === createdUserIds[1], `Expected connection with user 1, got ${connActiveData.items[0].personPreview.id}`);
                good('GET /connections/active works correctly (redirect followed automatically)');

                // Test GET /people/{person_id}/connections/active redirect - should return user 1's connections
                info('Testing GET /people/{person_id}/connections/active redirect...');
                const peopleConnResp = await fetch(`/people/${createdUserIds[1]}/connections/active`);
                const peopleConnData = await peopleConnResp.json();
                assert(peopleConnResp.ok, `GET /people/${createdUserIds[1]}/connections/active failed: ${JSON.stringify(peopleConnData)}`);
                assert(Array.isArray(peopleConnData.items), 'GET /people/{person_id}/connections/active should return items array');
                // User 1 has 1 active connection (with User 0)
                assert(peopleConnData.items.length === 1, `Expected 1 active connection for user 1, got ${peopleConnData.items.length}`);
                assert(peopleConnData.items[0].personPreview.id === createdUserIds[0], `Expected connection with user 0, got ${peopleConnData.items[0].personPreview.id}`);
                good('GET /people/{person_id}/connections/active works correctly (redirect followed automatically)');

                // Test post redirects by verifying they work
                info('Testing post redirect endpoints...');

                // Test GET /people/{person_id}/posts redirect - should return user 2's posts (user 2 still has 1 undeleted post)
                info('Testing GET /people/{person_id}/posts redirect...');
                const peoplePostsResp = await fetch(`/people/${createdUserIds[2]}/posts`);
                const peoplePostsData = await peoplePostsResp.json();
                assert(peoplePostsResp.ok, `GET /people/${createdUserIds[2]}/posts failed: ${JSON.stringify(peoplePostsData)}`);
                assert(Array.isArray(peoplePostsData.items), 'GET /people/{person_id}/posts should return items array');
                // User 2 has 1 undeleted post (post at postIds[2])
                assert(peoplePostsData.items.length === 1, `Expected 1 post for user 2, got ${peoplePostsData.items.length}`);
                assert(peoplePostsData.items[0].id === postIds[2], `Expected post ${postIds[2]}, got ${peoplePostsData.items[0].id}`);
                good('GET /people/{person_id}/posts works correctly (redirect followed automatically)');

                // Test GET /people/{person_id}/posts/{post_id} redirect - should return specific post
                info('Testing GET /people/{person_id}/posts/{post_id} redirect...');
                const peoplePostByIdResp = await fetch(`/people/${createdUserIds[2]}/posts/${postIds[2]}`);
                const peoplePostByIdData = await peoplePostByIdResp.json();
                assert(peoplePostByIdResp.ok, `GET /people/${createdUserIds[2]}/posts/${postIds[2]} failed: ${JSON.stringify(peoplePostByIdData)}`);
                assert(Array.isArray(peoplePostByIdData.items), 'GET /people/{person_id}/posts/{post_id} should return items array');
                assert(peoplePostByIdData.items.length === 1, 'GET /people/{person_id}/posts/{post_id} should return exactly one post');
                assert(peoplePostByIdData.items[0].id === postIds[2], 'GET /people/{person_id}/posts/{post_id} should return the correct post');
                good('GET /people/{person_id}/posts/{post_id} works correctly (redirect followed automatically)');

                // Test DELETE /people/{person_id}/posts/{post_id} redirect - should delete user 2's post
                info('Testing DELETE /people/{person_id}/posts/{post_id} redirect...');
                setOverrideUserCookie(createdUserIds[2]); // Switch to user 2 to delete their own post
                const peoplePostDeleteResp = await fetch(`/people/${createdUserIds[2]}/posts/${postIds[2]}`, {
                    method: 'DELETE'
                });
                const peoplePostDeleteData = await peoplePostDeleteResp.json();
                assert(peoplePostDeleteResp.ok, `DELETE /people/${createdUserIds[2]}/posts/${postIds[2]} failed: ${JSON.stringify(peoplePostDeleteData)}`);
                // Verify deletion by fetching the post again and checking it's soft-deleted
                const verifyDeleteResp = await fetch(`/posts/${createdUserIds[2]}/${postIds[2]}`);
                const verifyDeleteData = await verifyDeleteResp.json();
                assert(verifyDeleteResp.ok, `Failed to verify post deletion: ${JSON.stringify(verifyDeleteData)}`);
                assert(verifyDeleteData.items[0].deleted === true, 'Post should be soft-deleted after DELETE redirect');
                good('DELETE /people/{person_id}/posts/{post_id} works correctly (redirect followed automatically)');

                // Switch back to user 0 for comment tests
                setOverrideUserCookie(createdUserIds[0]);

                // Test comment redirects by verifying they work
                info('Testing comment redirect endpoints...');

                // Use a non-deleted root comment for redirect tests (use the second root comment)
                const secondRootCommentId = rootCommentIdsByPost[postIds[0]][1];

                // Test GET /posts/{post_id}/comments redirect - should return comments for post
                info('Testing GET /posts/{post_id}/comments redirect...');
                const postCommentsGetResp = await fetch(`/posts/${postIds[0]}/comments`);
                const postCommentsGetData = await postCommentsGetResp.json();
                assert(postCommentsGetResp.ok, `GET /posts/${postIds[0]}/comments failed: ${JSON.stringify(postCommentsGetData)}`);
                assert(Array.isArray(postCommentsGetData.items), 'GET /posts/{post_id}/comments should return items array');
                // Post 0 has 3 root comments total (including 1 soft-deleted)
                assert(postCommentsGetData.items.length === 3, `Expected 3 root comments for post 0, got ${postCommentsGetData.items.length}`);
                good('GET /posts/{post_id}/comments works correctly (redirect followed automatically)');

                // Test GET /posts/{post_id}/comments/{comment_id} redirect - should return sub-comments for the second root comment
                info('Testing GET /posts/{post_id}/comments/{comment_id} redirect...');
                const postSubCommentsGetResp = await fetch(`/posts/${postIds[0]}/comments/${secondRootCommentId}`);
                const postSubCommentsGetData = await postSubCommentsGetResp.json();
                assert(postSubCommentsGetResp.ok, `GET /posts/${postIds[0]}/comments/${secondRootCommentId} failed: ${JSON.stringify(postSubCommentsGetData)}`);
                assert(Array.isArray(postSubCommentsGetData.items), 'GET /posts/{post_id}/comments/{comment_id} should return items array');
                // Second root comment has no sub-comments (only first root comment has sub-comments)
                assert(postSubCommentsGetData.items.length === 0, `Expected 0 sub-comments for second root comment, got ${postSubCommentsGetData.items.length}`);
                good('GET /posts/{post_id}/comments/{comment_id} works correctly (redirect followed automatically)');

                // TODO: remove when the commented-out redirect endpoint is removed
                //
                // Test POST /posts/{post_id}/comments redirect - should create a new comment
                // info('Testing POST /posts/{post_id}/comments redirect...');
                // const postCommentsPostResp = await fetch(`/posts/${postIds[0]}/comments`, {
                //     method: 'POST',
                //     headers: {'Content-Type': 'application/json'},
                //     body: JSON.stringify({ content: 'Test redirect comment created via legacy endpoint' })
                // });
                // const postCommentsPostData = await postCommentsPostResp.json();
                // assert(postCommentsPostResp.ok, `POST /posts/${postIds[0]}/comments failed: ${JSON.stringify(postCommentsPostData)}`);
                // assert(postCommentsPostData.id, 'POST /posts/{post_id}/comments should return created comment with ID');
                // // Verify comment creation by fetching all comments and checking count increased
                // const verifyNewCommentResp = await fetch(`/comments/${postIds[0]}`);
                // const verifyNewCommentData = await verifyNewCommentResp.json();
                // assert(verifyNewCommentResp.ok, `Failed to verify new comment creation: ${JSON.stringify(verifyNewCommentData)}`);
                // assert(verifyNewCommentData.items.length === 4, `Expected 4 root comments after POST, got ${verifyNewCommentData.items.length}`);
                // const newComment = verifyNewCommentData.items.find(c => c.id === postCommentsPostData.id);
                // assert(newComment && newComment.content === 'Test redirect comment created via legacy endpoint', 'New comment should have correct content');
                // good('POST /posts/{post_id}/comments works correctly (redirect followed automatically)');

                // TODO: remove when the commented-out redirect endpoint is removed
                //
                // Test POST /posts/{post_id}/comments/{comment_id} redirect - should create a sub-comment on the second root comment
                // info('Testing POST /posts/{post_id}/comments/{comment_id} redirect...');
                // const postSubCommentsPostResp = await fetch(`/posts/${postIds[0]}/comments/${secondRootCommentId}`, {
                //     method: 'POST',
                //     headers: {'Content-Type': 'application/json'},
                //     body: JSON.stringify({ content: 'Test redirect sub-comment created via legacy endpoint' })
                // });
                // const postSubCommentsPostData = await postSubCommentsPostResp.json();
                // assert(postSubCommentsPostResp.ok, `POST /posts/${postIds[0]}/comments/${secondRootCommentId} failed: ${JSON.stringify(postSubCommentsPostData)}`);
                // assert(postSubCommentsPostData.id, 'POST /posts/{post_id}/comments/{comment_id} should return created sub-comment with ID');
                // // Verify sub-comment creation by fetching sub-comments for the second root comment
                // const verifyNewSubCommentResp = await fetch(`/comments/${postIds[0]}/${secondRootCommentId}`);
                // const verifyNewSubCommentData = await verifyNewSubCommentResp.json();
                // assert(verifyNewSubCommentResp.ok, `Failed to verify new sub-comment creation: ${JSON.stringify(verifyNewSubCommentData)}`);
                // assert(verifyNewSubCommentData.items.length === 1, `Expected 1 sub-comment for second root comment after POST, got ${verifyNewSubCommentData.items.length}`);
                // assert(verifyNewSubCommentData.items[0].content === 'Test redirect sub-comment created via legacy endpoint', 'New sub-comment should have correct content');
                // good('POST /posts/{post_id}/comments/{comment_id} works correctly (redirect followed automatically)');

                // Test PATCH /posts/{post_id}/comments/{comment_id} redirect - should update the second root comment
                info('Testing PATCH /posts/{post_id}/comments/{comment_id} redirect...');
                setOverrideUserCookie(createdUserIds[1]); // Switch to user 1 who owns the second root comment
                const postCommentsPatchResp = await fetch(`/posts/${postIds[0]}/comments/${secondRootCommentId}`, {
                    method: 'PATCH',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ content: 'Updated comment content via PATCH redirect' })
                });
                const postCommentsPatchData = await postCommentsPatchResp.json();
                assert(postCommentsPatchResp.ok, `PATCH /posts/${postIds[0]}/comments/${secondRootCommentId} failed: ${JSON.stringify(postCommentsPatchData)}`);
                assert(postCommentsPatchData.content === 'Updated comment content via PATCH redirect', 'PATCH should update comment content correctly');
                // Verify update by fetching the comment again
                const verifyUpdateResp = await fetch(`/comments/${postIds[0]}`);
                const verifyUpdateData = await verifyUpdateResp.json();
                assert(verifyUpdateResp.ok, `Failed to verify comment update: ${JSON.stringify(verifyUpdateData)}`);
                const updatedComment = verifyUpdateData.items.find(c => c.id === secondRootCommentId);
                assert(updatedComment && updatedComment.content === 'Updated comment content via PATCH redirect', 'Comment should have updated content after PATCH redirect');
                good('PATCH /posts/{post_id}/comments/{comment_id} works correctly (redirect followed automatically)');

                // Test DELETE /posts/{post_id}/comments/{comment_id} redirect - should delete the second root comment
                info('Testing DELETE /posts/{post_id}/comments/{comment_id} redirect...');
                const postCommentsDeleteResp = await fetch(`/posts/${postIds[0]}/comments/${secondRootCommentId}`, {
                    method: 'DELETE'
                });
                const postCommentsDeleteData = await postCommentsDeleteResp.json();
                assert(postCommentsDeleteResp.ok, `DELETE /posts/${postIds[0]}/comments/${secondRootCommentId} failed: ${JSON.stringify(postCommentsDeleteData)}`);
                // Verify deletion by fetching the comment again and checking it's soft-deleted
                const verifyCommentDeleteResp = await fetch(`/comments/${postIds[0]}`);
                const verifyCommentDeleteData = await verifyCommentDeleteResp.json();
                assert(verifyCommentDeleteResp.ok, `Failed to verify comment deletion: ${JSON.stringify(verifyCommentDeleteData)}`);
                const deletedRootComment = verifyCommentDeleteData.items.find(c => c.id === secondRootCommentId);
                assert(deletedRootComment && deletedRootComment.deleted === true, 'Comment should be soft-deleted after DELETE redirect');
                good('DELETE /posts/{post_id}/comments/{comment_id} works correctly (redirect followed automatically)');

                good('All redirect endpoints work correctly with automatic redirect following!');

                const executionTime = ((Date.now() - startTime) / 1000).toFixed(2);
                info('====================================================================');
                good(`INTEGRATION TEST FINISHED SUCCESSFULLY IN ${executionTime} SECONDS.`);
                info('====================================================================');
            } catch (e) {
                fail('Test failed: ' + (e.message || e));
            } finally {
                removeOverrideCookies();
                await removeThreeUsers();
                const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
                good('Cleanup complete.');
                info('Total time: ' + totalTime + ' seconds.');
            }
        }
        document.getElementById('run-integration-tests-btn').addEventListener('click', runAllIntegrationTests);
    </script>
</body>
</html>
