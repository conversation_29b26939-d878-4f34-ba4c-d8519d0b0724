<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Peeps App - Admin Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .dashboard-title {
            font-size: 24px;
            font-weight: 600;
        }
        .user-info {
            display: flex;
            align-items: center;
        }
        .user-name {
            margin-right: 15px;
            font-weight: 500;
        }
        .logout-button {
            background-color: #f44336;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        .logout-button:hover {
            background-color: #d32f2f;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-weight: 500;
        }
        .tab.active {
            border-bottom: 2px solid #0078d4;
            color: #0078d4;
        }
        .tab:hover:not(.active) {
            border-bottom: 2px solid #ddd;
        }
        .tab-content {
            display: none;
            padding: 20px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .tab-content.active {
            display: block;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
            overflow: hidden;
        }
        .monitoring-tabs {
            display: flex;
            margin-bottom: 15px;
        }
        .monitoring-tab {
            padding: 8px 16px;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            margin-right: 5px;
            font-weight: 500;
        }
        .monitoring-tab.active {
            background-color: #0078d4;
            color: white;
            border-color: #0078d4;
        }
        .monitoring-content {
            display: none;
        }
        .monitoring-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="dashboard-title">Peeps App Admin Dashboard</div>
            <div class="user-info">
                <div class="user-name">{{user_name}}</div>
                <a href="/auth/azure-ad/logout">
                    <button class="logout-button">Logout</button>
                </a>
            </div>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="openTab(event, 'overview')">Overview</div>
            <div class="tab" onclick="openTab(event, 'accounts')">Accounts</div>
            <div class="tab" onclick="openTab(event, 'auth-monitoring')">Monitoring</div>
            <div class="tab" onclick="openTab(event, 'integration-tests')">Integration Test</div>
            <div class="tab" onclick="openTab(event, 'api-docs')">API Docs</div>
        </div>

        <div id="overview" class="tab-content active">
            <h2>Dashboard Overview</h2>
            <p>Welcome to the Peeps App Admin Dashboard. Use the tabs above to navigate between different sections.</p>
            <p>This dashboard provides administrative tools and monitoring capabilities for the Peeps App platform.</p>
            <p>Current user: {{user_name}} ({{user_email}})</p>
            <p>Authentication source: {{auth_source}}</p>
        </div>

        <div id="auth-monitoring" class="tab-content">
            <h2>Monitoring</h2>
            <div class="monitoring-tabs">
                <button class="monitoring-tab active" onclick="showMonitoringTab('auth-stats')">Authentication Stats</button>
                <button class="monitoring-tab" onclick="showMonitoringTab('error-stats')">Error Stats</button>
            </div>
            <div id="auth-stats" class="monitoring-content active">
                <iframe src="/dashboard/auth-stats-embed" title="Authentication Statistics"></iframe>
            </div>
            <div id="error-stats" class="monitoring-content">
                <iframe src="/dashboard/error-stats-embed" title="Error Statistics"></iframe>
            </div>
        </div>

        <div id="accounts" class="tab-content">
            <h2>Account Management</h2>
            <iframe src="/dashboard/accounts" title="Account Management" style="width: 100%; height: 800px; border: none;"></iframe>
        </div>

        <div id="integration-tests" class="tab-content">
            <h2>Integration Test</h2>
            <iframe src="/static/templates/integration-tests.html" title="Integration Tests" style="width: 100%; height: 800px; border: none;"></iframe>
        </div>

        <div id="api-docs" class="tab-content">
            <h2>API Documentation</h2>
            <iframe src="/dashboard/api-docs" title="API Documentation" style="width: 100%; height: 800px; border: none;"></iframe>
        </div>
    </div>

    <script>
        function openTab(evt, tabName) {
            // Hide all tab content
            var tabcontent = document.getElementsByClassName("tab-content");
            for (var i = 0; i < tabcontent.length; i++) {
                tabcontent[i].className = tabcontent[i].className.replace(" active", "");
            }

            // Remove active class from all tabs
            var tabs = document.getElementsByClassName("tab");
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].className = tabs[i].className.replace(" active", "");
            }

            // Show the current tab and add active class
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }

        function showMonitoringTab(tabName) {
            // Hide all monitoring content
            var contents = document.getElementsByClassName("monitoring-content");
            for (var i = 0; i < contents.length; i++) {
                contents[i].className = contents[i].className.replace(" active", "");
            }

            // Remove active class from all monitoring tabs
            var tabs = document.getElementsByClassName("monitoring-tab");
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].className = tabs[i].className.replace(" active", "");
            }

            // Show the selected monitoring tab content
            document.getElementById(tabName).className += " active";

            // Find and activate the clicked tab button
            var buttons = document.getElementsByClassName("monitoring-tab");
            for (var i = 0; i < buttons.length; i++) {
                if (buttons[i].textContent.toLowerCase().includes(tabName.replace("-", " "))) {
                    buttons[i].className += " active";
                }
            }
        }
    </script>
</body>
</html>
