<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Statistics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .stats-container {
            max-width: 100%;
            margin: 0 auto;
        }
        .stats-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-header {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        .stat-item {
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .success-rate {
            color: #34a853;
        }
        .failure-rate {
            color: #ea4335;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            font-weight: 600;
            background-color: #f5f5f5;
        }
        tr:hover {
            background-color: #f9f9f9;
        }
        .refresh-button {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            margin-bottom: 20px;
        }
        .refresh-button:hover {
            background-color: #106ebe;
        }
        .timestamp {
            font-size: 12px;
            color: #666;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="stats-container">
        <button class="refresh-button" onclick="location.reload()">Refresh Data</button>
        <div class="timestamp">Generated at: {{generated_at}}</div>

        <div class="stats-card">
            <div class="stats-header">Authentication Summary</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">{{ total_attempts }}</div>
                    <div class="stat-label">Total Attempts</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value success-rate">{{ successful_attempts }}</div>
                    <div class="stat-label">Successful Attempts</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value failure-rate">{{ failed_attempts }}</div>
                    <div class="stat-label">Failed Attempts</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ overall_success_rate }}%</div>
                    <div class="stat-label">Overall Success Rate</div>
                </div>
            </div>
        </div>

        <div class="stats-card">
            <div class="stats-header">Authentication Methods</div>
            <div class="stats-grid">
                {% for method in methods %}
                <div class="stat-item">
                    <div class="stat-value">{{ method.attempts }}</div>
                    <div class="stat-label">{{ method.name }} Attempts</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ method.success_rate }}%</div>
                    <div class="stat-label">{{ method.name }} Success Rate</div>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="stats-card">
            <div class="stats-header">Recent Authentication Events</div>
            <table>
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Method</th>
                        <th>Status</th>
                        <th>Identifier</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    {% for event in recent_events %}
                    <tr>
                        <td>{{ event.timestamp }}</td>
                        <td>{{ event.auth_method }}</td>
                        <td class="{% if event.success %}success-rate{% else %}failure-rate{% endif %}">
                            {% if event.success %}Success{% else %}Failure{% endif %}
                        </td>
                        <td>{{ event.identifier }}</td>
                        <td>{{ event.ip_address }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="stats-card">
            <div class="stats-header">Suspicious IP Addresses</div>
            {% if suspicious_ips %}
            <table>
                <thead>
                    <tr>
                        <th>IP Address</th>
                        <th>Attempts</th>
                        <th>Failures</th>
                        <th>Failure Rate</th>
                    </tr>
                </thead>
                <tbody>
                    {% for ip in suspicious_ips %}
                    <tr>
                        <td>{{ ip.ip_address }}</td>
                        <td>{{ ip.attempts }}</td>
                        <td>{{ ip.failures }}</td>
                        <td>{{ ip.failure_rate }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p>No suspicious IP addresses detected.</p>
            {% endif %}
        </div>
    </div>
</body>
</html>
