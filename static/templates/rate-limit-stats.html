<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rate Limiting Statistics</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .timestamp {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        .stats-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-item {
            flex: 1;
            min-width: 150px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
            color: #3498db;
        }
        .stat-label {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        .success-rate {
            color: #2ecc71;
        }
        .failure-rate {
            color: #e74c3c;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .section {
            margin-bottom: 30px;
        }
        .button {
            display: inline-block;
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .button:hover {
            background-color: #2980b9;
        }
        .button-danger {
            background-color: #e74c3c;
        }
        .button-danger:hover {
            background-color: #c0392b;
        }
        .refresh-button {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rate Limiting Statistics</h1>
            <div>
                <button class="button refresh-button" onclick="location.reload()">Refresh</button>
                <button class="button button-danger" onclick="resetStats()">Reset Statistics</button>
            </div>
        </div>

        <div class="timestamp">
            Generated at: {{ generated_at }}
        </div>

        <div class="section">
            <h2>Summary</h2>
            <div class="stats-container">
                <div class="stat-item">
                    <div class="stat-value">{{ total_requests }}</div>
                    <div class="stat-label">Total Requests</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value failure-rate">{{ total_blocked_requests }}</div>
                    <div class="stat-label">Blocked Requests</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ overall_block_rate_percent }}%</div>
                    <div class="stat-label">Block Rate</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Rate Limiters</h2>
            <table>
                <thead>
                    <tr>
                        <th>Rate Limiter</th>
                        <th>Requests</th>
                        <th>Blocked</th>
                        <th>Block Rate</th>
                        <th>Active Blocks</th>
                        <th>Window</th>
                        <th>Max Requests</th>
                        <th>Block Duration</th>
                    </tr>
                </thead>
                <tbody>
                    {% for limiter in rate_limiters %}
                    <tr>
                        <td>{{ limiter.name }}</td>
                        <td>{{ limiter.requests }}</td>
                        <td>{{ limiter.blocked }}</td>
                        <td>{{ limiter.block_rate }}%</td>
                        <td>{{ limiter.active_blocks }}</td>
                        <td>{{ limiter.window_seconds }}s</td>
                        <td>{{ limiter.max_requests }}</td>
                        <td>{{ limiter.block_duration_minutes }}m</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function resetStats() {
            if (confirm('Are you sure you want to reset all rate limiting statistics? This will not affect active blocks.')) {
                fetch('{{ reset_url }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Statistics reset successfully');
                        location.reload();
                    } else {
                        alert('Error resetting statistics: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Error resetting statistics: ' + error);
                });
            }
        }
    </script>
</body>
</html>
