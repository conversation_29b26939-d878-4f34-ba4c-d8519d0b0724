@description('The name of the Azure AD application')
param appName string

@description('The Azure AD tenant ID')
param tenantId string = subscription().tenantId

@description('The redirect URIs for the application')
param redirectUris array

@description('The logout URL for the application')
param logoutUrl string

@description('The environment name (dev, stage, api)')
param environmentName string

// Note: Azure AD resources cannot be directly deployed using Bicep
// Instead, we'll use the Azure CLI in the Makefile to create these resources
// This is a placeholder to document the intended configuration

// The following resources would be created using Azure CLI:
// 1. Azure AD application registration with:
//    - Display name: appName
//    - Sign-in audience: AzureADMyOrg (internal employees only)
//    - Redirect URIs: redirectUris
//    - Logout URL: logoutUrl
//    - ID token issuance enabled
//    - API access token version: 2
//
// 2. Service principal for the application

// Output placeholders - actual values will be set by Azure CLI
output applicationId string = 'placeholder-app-id-${appName}'
output applicationObjectId string = 'placeholder-obj-id-${appName}'
output servicePrincipalId string = 'placeholder-sp-id-${appName}'
