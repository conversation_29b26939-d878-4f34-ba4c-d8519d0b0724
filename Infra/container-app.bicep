@description('The name of the Azure Container App')
param containerAppName string

@description('The Azure region for the container app')
param location string = resourceGroup().location

@description('The name of the Azure Container Registry')
param acrName string

@description('The managed identity ID to use for the container app')
param managedIdentityId string

@description('The managed identity client ID to use for the container app')
param managedIdentityClientId string

@description('The name of the Azure Key Vault')
param keyVaultName string

@description('The environment name (dev, test, prod)')
param environmentName string = 'dev'

@description('Whether to upload mock data on a service start')
param enableMockDataUpload string = 'false'

@description('Log level for application logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
param logLevel string = 'INFO'

@description('The container image to deploy')
param containerImage string = 'mcr.microsoft.com/azuredocs/containerapps-helloworld:latest'

@description('The number of CPU cores for the container app')
param containerCpuCores string = '0.5'

@description('The amount of memory for the container app')
param containerMemory string = '1.0Gi'

@description('The minimum number of replicas for the container app')
param minReplicas int = 1

@description('The maximum number of replicas for the container app')
param maxReplicas int = 3

@description('Whether to enable custom domain')
param enableCustomDomain bool = false

@description('The custom domain name')
param customDomainName string = ''

@description('The certificate name for the custom domain')
param containerCertificateName string = ''

// Get existing ACR
resource acr 'Microsoft.ContainerRegistry/registries@2023-07-01' existing = {
  name: acrName
}

// Get existing Key Vault
resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: keyVaultName
}

// Create Container App Environment
resource containerAppEnvironment 'Microsoft.App/managedEnvironments@2023-05-01' = {
  name: 'peepsapp-capp-${environmentName}'
  location: location
  properties: {
    appLogsConfiguration: {
      destination: 'log-analytics'
      logAnalyticsConfiguration: {
        customerId: logAnalyticsWorkspace.properties.customerId
        sharedKey: logAnalyticsWorkspace.listKeys().primarySharedKey
      }
    }
  }
}

// Create Log Analytics Workspace
resource logAnalyticsWorkspace 'Microsoft.OperationalInsights/workspaces@2022-10-01' = {
  name: 'peepsapp-logs-${environmentName}'
  location: location
  properties: {
    sku: {
      name: 'PerGB2018'
    }
    retentionInDays: 30
    features: {
      enableLogAccessUsingOnlyResourcePermissions: true
    }
    workspaceCapping: {
      dailyQuotaGb: 1
    }
  }
}

// Create Container App
resource containerApp 'Microsoft.App/containerApps@2023-05-01' = {
  name: containerAppName
  location: location
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${managedIdentityId}': {}
    }
  }
  properties: {
    managedEnvironmentId: containerAppEnvironment.id
    configuration: {
      activeRevisionsMode: 'Single'
      ingress: {
        external: true
        targetPort: 8000
        allowInsecure: false
        traffic: [
          {
            latestRevision: true
            weight: 100
          }
        ]
        // Add custom domain configuration if enabled
        customDomains: enableCustomDomain ? [
          {
            name: customDomainName
            bindingType: 'SniEnabled'
            certificateId: resourceId('Microsoft.App/managedEnvironments/managedCertificates', containerAppEnvironment.name, containerCertificateName)
          }
        ] : []
      }
      registries: [
        {
          server: acr.properties.loginServer
          identity: managedIdentityId
        }
      ]
      secrets: []
    }
    template: {
      containers: [
        {
          name: 'peepsapi'
          image: containerImage
          resources: {
            cpu: json(containerCpuCores)
            memory: containerMemory
          }
          env: [
            {
              name: 'ENVIRONMENT'
              value: environmentName
            }
            {
              name: 'ENABLE_DATA_LOADING'
              value: enableMockDataUpload
            }
            {
              name: 'AZURE_KEY_VAULT_URL'
              value: keyVault.properties.vaultUri
            }
            {
              name: 'AZURE_CLIENT_ID'
              value: managedIdentityClientId
            }
            {
              name: 'LOG_LEVEL'
              value: logLevel
            }
          ]
        }
      ]
      scale: {
        minReplicas: minReplicas
        maxReplicas: maxReplicas
      }
    }
  }
}

output containerAppFqdn string = containerApp.properties.configuration.ingress.fqdn
