# Infrastructure Setup

This folder contains the infrastructure setup for the PeepsAPI application.

## Overview

The infrastructure setup includes:

- Azure Container Apps (ACA) for hosting the application
- Azure Container Registry (ACR) for storing container images
- Azure Key Vault (AKV) for storing secrets
- Cosmos DB collections for data storage
- Azure OpenAI for AI capabilities
- Managed Identity for secure access between services

## Files

- `main.bicep`: Main Bicep template that orchestrates all resources
- `container-app.bicep`: Bicep template for Azure Container Apps
- `container-registry.bicep`: Bicep template for Azure Container Registry
- `key-vault.bicep`: Bicep template for Azure Key Vault
- `managed-identity.bicep`: Bicep template for Managed Identity
- `all-collections.bicep`: Bicep template for creating Cosmos DB collections
- `openai.bicep`: Bicep template for Azure OpenAI
- `main-parameters-dev.json`: Parameters for the development environment

- `grant_kv_access.sh`: Script to grant access to Azure Key Vault

## Usage

### Deploying Infrastructure

To deploy the infrastructure using Bicep:

```bash
# Deploy to development environment
make deploy-infra ENV=dev

# Deploy to production environment
make deploy-infra ENV=api

# Deploy to a custom environment
make deploy-infra ENV=stage

# Deploy to a specific resource group
make deploy-infra ENV=dev RESOURCE_GROUP=my-custom-resource-group
```

This will:
1. Check if the resource group exists and create it if needed
2. Deploy all Azure resources defined in the Bicep templates
3. Configure the resources for the specified environment

### Deploying the Application

After deploying the infrastructure, you can deploy the application to Azure Container Apps:

```bash
# Deploy to development environment
make deploy-service ENV=dev

# Deploy to production environment
make deploy-service ENV=prod

# Deploy to test environment
make deploy-service ENV=test

# Deploy to a custom environment
make deploy-service ENV=stage

# Set log level to DEBUG
LOG_LEVEL=DEBUG make deploy-service ENV=dev

# Deploy to a specific resource group
make deploy-service ENV=dev RESOURCE_GROUP=my-custom-resource-group
```

This will:
1. Build a Docker image for the application
2. Push the image to Azure Container Registry
3. Update the Azure Container App with the new image

### Environment Configuration

Environment-specific configuration is stored in files named `{environment}.env` in the project root. For example:

- `dev.env` - Development environment configuration
- `test.env` - Test environment configuration
- `prod.env` - Production environment configuration

You can also create environment-specific Bicep parameters files named `main-parameters-{environment}.json` in the Infra folder. For example:

- `main-parameters-dev.json` - Development environment parameters
- `main-parameters-prod.json` - Production environment parameters

If an environment-specific parameters file is not found, the deployment will fail with an error message.

## Environment Variables

The following environment variables are required for the application to connect to Azure services:

- `AZURE_KEY_VAULT_URL`: The URL of the Azure Key Vault (e.g., https://peepsapp-vault-dev.vault.azure.net/)
- `COSMOS_ENDPOINT`: The endpoint URL for the Cosmos DB account
- `COSMOS_KEY`: The primary key for the Cosmos DB account
- `COSMOS_DB`: The name of the Cosmos DB database

These variables are automatically set in the Azure Container App environment during deployment. For local development, they can be set in the `.env` or `local.env` file.

## Azure Container Apps Configuration

The Azure Container App is configured with:

- HTTPS ingress enabled
- Managed identity for secure access to other Azure services
- Autoscaling based on HTTP traffic
- Connection to Azure Container Registry
- Access to Azure Key Vault for configuration

## Azure OpenAI Configuration

Azure OpenAI is deployed by default with the infrastructure. Note that:

1. Not all Azure subscriptions have access to OpenAI resources
2. OpenAI resources may have regional availability limitations

The deployment will create:
- Azure OpenAI resource with the default (GPT-4o model deployment)
- OpenAI-related secrets in Azure Key Vault

By default, the GPT-4o model will be deployed with the following configuration:
```json
{
    "name": "gpt-4o"
    "model": {
      "name": "gpt-4o"
      "version": "2024-08-06"
      "format": "OpenAI"
    }
    "scaleSettings": {
      "scaleType": "Standard"
    }
  }
```

If you need to deploy different models or customize the deployment, you can add a `modelDeployments` parameter to your environment's parameter file.

To skip creating Azure OpenAI entirely, add the following to your environment parameters file:

```json
"deployOpenAi": { "value": false }
```

When `deployOpenAi` is `false`, the Makefile skips all OpenAI-related API calls and no OpenAI resources are created.

### Automatic Key Vault Configuration

When you run `make deploy-infra ENV=<environment>`, the following happens:

1. All Azure resources are deployed (Container Registry, Container App, Key Vault, Cosmos DB)
2. The script automatically extracts important information from the deployed resources
3. The following secrets are automatically set in Azure Key Vault:
   - `AZURE-KEY-VAULT-URL`: The URL of the Azure Key Vault
   - `COSMOS-ENDPOINT`: The endpoint URL for the Cosmos DB account
   - `COSMOS-KEY`: The primary key for the Cosmos DB account
   - `COSMOS-DB`: The name of the Cosmos DB database
   - `OPENAI-API-KEY`: The API key for Azure OpenAI (only if OpenAI is deployed)
   - `OPENAI-ENDPOINT`: The endpoint URL for Azure OpenAI (only if OpenAI is deployed)
   - `OPENAI-API-VERSION`: The API version for Azure OpenAI (only if OpenAI is deployed)
   - `OPENAI-MODEL-NAME`: The model name to use (only if OpenAI is deployed)
   - `OPENAI-DEPLOYMENT`: The deployment name to use (only if OpenAI is deployed)
   - `WEBAUTHN-RP-ID`: The domain name of the Container App
   - `WEBAUTHN-RP-NAME`: The name of the application (e.g., "Peeps App (dev)")
   - `WEBAUTHN-RP-ORIGIN`: The origin URL of the Container App (e.g., "https://peepsapi-dev.azurecontainerapps.io")
4. The Container App's managed identity is granted access to read secrets from Key Vault

## Adding New Collections

To add new collections:

1. Update the `main-parameters-{environment}.json` file with the new collection details in the `collections` array
2. Run `make deploy-infra ENV={environment}` to deploy the changes

## Troubleshooting

### Authentication Issues

If you encounter issues with Azure CLI authentication, make sure you're logged in:

```bash
az login
```

### Resource Deployment Failures

If resource deployment fails, check the error message in the Azure CLI output. Common issues include:

- Resource name conflicts (names must be globally unique)
- Insufficient permissions
- Resource quotas or limits

### Container App Deployment Issues

If the container app deployment fails, check:

- Docker build errors
- Container registry access
- Container app configuration

You can view the container app logs in the Azure Portal or using the Azure CLI:

```bash
az containerapp logs show --name peepsapi-dev --resource-group peepsapp-dev-rg
```
