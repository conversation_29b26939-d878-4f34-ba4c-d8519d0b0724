@description('The name of the Azure OpenAI resource')
param openAiName string

@description('The Azure region for the OpenAI resource')
param location string = resourceGroup().location

@description('The SKU name for the OpenAI resource')
param skuName string = 'S0'

@description('The model deployments to create')
param modelDeployments array

// Using the latest stable API version for Azure OpenAI
resource openAi 'Microsoft.CognitiveServices/accounts@2024-10-01' = {
  name: openAiName
  location: location
  kind: 'OpenAI'
  sku: {
    name: skuName
  }
  properties: {
    customSubDomainName: openAiName
    publicNetworkAccess: 'Enabled'
  }
}

@batchSize(1)
resource modelDeployment 'Microsoft.CognitiveServices/accounts/deployments@2024-10-01' = [for deployment in modelDeployments: {
  parent: openAi
  name: deployment.name
  sku: {
    name: 'Standard'
    capacity: deployment.capacity
  }
  properties: {
    model: deployment.model
  }
}]

output openAiEndpoint string = openAi.properties.endpoint
output openAiName string = openAi.name
