{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"environmentName": {"value": "api"}, "enableMockDataUpload": {"value": "false"}, "logLevel": {"value": "INFO"}, "location": {"value": "west<PERSON>"}, "keyVaultName": {"value": "peepsapp-vault-api"}, "deployOpenAi": {"value": true}, "deployAzureAdSso": {"value": true}, "azureAdAppName": {"value": "Peeps App SSO API"}, "azureAdRedirectUris": {"value": ["https://api.peepsapp.ai/auth/azure-ad/callback"]}, "azureAdLogoutUrl": {"value": "https://api.peepsapp.ai/auth/logout"}, "enableCustomDomain": {"value": true}, "customDomainName": {"value": "api.peepsapp.ai"}, "containerCertificateName": {"value": "api.peepsapp.ai-peepsapp-************"}, "picturesStorageAccountName": {"value": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "picturesOriginalsContainerName": {"value": "pictures-originals"}, "picturesThumbnailsContainerName": {"value": "pictures-thumbnails"}, "modelDeployments": {"value": [{"name": "gpt-4o", "model": {"name": "gpt-4o", "version": "2024-05-13", "format": "OpenAI"}, "skuName": "Standard", "capacity": 1}]}, "collections": {"value": [{"databaseName": "peepsapp-cosmos-db-api", "name": "communities", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-api", "name": "people", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-api", "name": "conversations", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-api", "name": "events", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-api", "name": "messages", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-api", "name": "passkey_credentials", "partitionKeyPath": "/person_id", "uniqueKeys": [{"paths": ["/credential_id"]}]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "registration_challenges", "partitionKeyPath": "/person_id", "defaultTtl": 300}, {"databaseName": "peepsapp-cosmos-db-api", "name": "authentication_challenges", "partitionKeyPath": "/person_id", "defaultTtl": 300}, {"databaseName": "peepsapp-cosmos-db-api", "name": "invite_tokens", "partitionKeyPath": "/id", "uniqueKeys": [{"paths": ["/token"]}, {"paths": ["/email"]}]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "session_tokens", "partitionKeyPath": "/person_id", "defaultTtl": 2592000}, {"databaseName": "peepsapp-cosmos-db-api", "name": "audit_logs", "partitionKeyPath": "/id", "defaultTtl": 7776000}, {"databaseName": "peepsapp-cosmos-db-api", "name": "locks", "partitionKeyPath": "/type", "defaultTtl": 30, "excludedIndexPaths": [{"path": "/\"type\"/?"}]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "connections", "partitionKeyPath": "/owner_person_id", "excludedIndexPaths": [], "compositeIndexes": [[{"path": "/status", "order": "ascending"}, {"path": "/requester_person_id", "order": "ascending"}], [{"path": "/status", "order": "ascending"}, {"path": "/requestee_person_id", "order": "ascending"}]], "fullTextPolicy": {"defaultLanguage": "en-US", "fullTextPaths": [{"path": "/person_preview/name", "language": "en-US"}, {"path": "/person_preview/last_name", "language": "en-US"}, {"path": "/person_preview/current_role", "language": "en-US"}, {"path": "/person_preview/current_company", "language": "en-US"}, {"path": "/person_preview/location", "language": "en-US"}]}, "fullTextIndexes": [{"path": "/person_preview/name"}, {"path": "/person_preview/last_name"}, {"path": "/person_preview/current_role"}, {"path": "/person_preview/current_company"}, {"path": "/person_preview/location"}]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "posts", "partitionKeyPath": "/author_person_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "comments", "partitionKeyPath": "/target_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "person_reactions", "partitionKeyPath": "/author_person_id", "uniqueKeys": [{"paths": ["/target_id"]}], "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "reactions", "partitionKeyPath": "/target_id", "uniqueKeys": [{"paths": ["/author_person_id"]}], "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "active_jobs", "partitionKeyPath": "/action"}, {"databaseName": "peepsapp-cosmos-db-api", "name": "feeds", "partitionKeyPath": "/person_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "pictures", "partitionKeyPath": "/parent_id", "compositeIndexes": [[{"path": "/parent_type", "order": "ascending"}, {"path": "/parent_id", "order": "ascending"}]]}, {"databaseName": "peepsapp-cosmos-db-api", "name": "notes", "partitionKeyPath": "/author_id", "defaultTtl": -1, "indexingPolicy": {"indexingMode": "consistent"}, "includedPaths": [{"path": "/object_id/?"}, {"path": "/created_at/?", "indexes": [{"dataType": "Number", "precision": -1, "kind": "Range", "order": "Descending"}]}, {"path": "/note_group_id/?"}], "excludedIndexPaths": [{"path": "/content/*"}]}]}}}