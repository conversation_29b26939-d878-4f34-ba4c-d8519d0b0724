@description('The environment name (dev, test, prod)')
param environmentName string = 'dev'

@description('Whether to upload mock data on a service start')
param enableMockDataUpload string = 'false'

@description('Log level for application logging (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
param logLevel string = 'INFO'

@description('The Azure region for all resources')
param location string = resourceGroup().location

@description('The name of the Azure Container Registry')
param acrName string = 'peepsappcr${environmentName}'

@description('The name of the Azure Container App')
param containerAppName string = 'peepsapp-aca-${environmentName}'

@description('The certificate name for the custom domain')
param containerCertificateName string = ''

@description('The name of the Azure Key Vault')
param keyVaultName string = 'peepsapp-vault-${environmentName}'

@description('The name of the Azure Cosmos DB account')
param cosmosDbAccountName string = 'peepsappcosmos${environmentName}'

@description('The name of the Cosmos DB database')
param databaseName string = 'peepsapp-cosmos-db-${environmentName}'

@description('The name of the Storage account for pictures')
param picturesStorageAccountName string = 'peepsappblob${environmentName}'

@description('The container for original pictures')
param picturesOriginalsContainerName string = 'pictures-originals'

@description('The container for picture thumbnails')
param picturesThumbnailsContainerName string = 'pictures-thumbnails'

@description('The name of the Azure OpenAI resource')
param openAiName string = 'peepsapp-openai-${environmentName}'

@description('Whether to deploy Azure OpenAI')
param deployOpenAi bool = true

@description('The list of all collections to deploy')
param collections array = []

@description('The OpenAI model deployments to create')
@metadata({
  note: 'If not specified, the default values from openai.bicep will be used. Default is gpt-4o with version 2024-08-06.'
})
param modelDeployments array = []

// Note: Azure AD resources are managed separately via Azure CLI in the Makefile
// These parameters are kept for documentation purposes but not used in the Bicep deployment
@description('Whether to deploy Azure AD SSO')
param deployAzureAdSso bool = true

@description('The name of the Azure AD application')
param azureAdAppName string = 'Peeps App SSO ${environmentName}'

@description('The redirect URIs for the Azure AD application')
param azureAdRedirectUris array = []

@description('The logout URL for the Azure AD application')
param azureAdLogoutUrl string = ''

@description('Whether to enable custom domain')
param enableCustomDomain bool = false

@description('The custom domain name')
param customDomainName string = ''

// Deploy Azure Container Registry
module acr 'container-registry.bicep' = {
  name: 'acrDeployment'
  params: {
    acrName: acrName
    location: location
  }
}

// Deploy Azure Key Vault
module keyVault 'key-vault.bicep' = {
  name: 'keyVaultDeployment'
  params: {
    keyVaultName: keyVaultName
    location: location
  }
}

// Deploy Managed Identity
module managedIdentity 'managed-identity.bicep' = {
  name: 'managedIdentityDeployment'
  params: {
    managedIdentityName: 'peepsapp-mid-${environmentName}'
    location: location
  }
}

// Deploy Blob Storage for pictures
module picturesStorage 'azure-blob.bicep' = {
  name: 'picturesStorageDeployment'
  params: {
    storageAccountName: picturesStorageAccountName
    location: location
    originalsContainerName: picturesOriginalsContainerName
    thumbnailsContainerName: picturesThumbnailsContainerName
  }
}

// Deploy Cosmos DB Collections
module cosmosDb 'all-collections.bicep' = {
  name: 'cosmosDbDeployment'
  params: {
    cosmosDbAccountName: cosmosDbAccountName
    collections: collections
  }
}

// Deploy Azure OpenAI with models
module openAi 'openai.bicep' = if (deployOpenAi) {
  name: 'openAiDeployment'
  params: {
    openAiName: openAiName
    location: location
    modelDeployments: modelDeployments
    skuName: 'S0'
  }
}

// Deploy Container App
module containerApp 'container-app.bicep' = {
  name: 'containerAppDeployment'
  params: {
    containerAppName: containerAppName
    location: location
    acrName: acrName
    managedIdentityId: managedIdentity.outputs.managedIdentityId
    managedIdentityClientId: managedIdentity.outputs.managedIdentityClientId
    keyVaultName: keyVaultName
    environmentName: environmentName
    enableMockDataUpload: enableMockDataUpload
    logLevel: logLevel
    enableCustomDomain: enableCustomDomain
    customDomainName: customDomainName
    containerCertificateName: containerCertificateName
  }
  dependsOn: [
    acr
    keyVault
  ]
}

// Outputs
output acrLoginServer string = acr.outputs.acrLoginServer
output containerAppFqdn string = containerApp.outputs.containerAppFqdn
// Note: The actual Azure AD application ID is set by Azure CLI in the Makefile
output azureAdApplicationId string = deployAzureAdSso ? 'managed-by-azure-cli' : ''
output managedIdentityId string = managedIdentity.outputs.managedIdentityId
output managedIdentityPrincipalId string = managedIdentity.outputs.managedIdentityPrincipalId
output keyVaultUri string = keyVault.outputs.keyVaultUri
output keyVaultName string = keyVault.outputs.keyVaultName
output picturesStorageConnectionString string = picturesStorage.outputs.connectionString
output picturesStorageAccountName string = picturesStorage.outputs.storageAccountName
output cosmosDbEndpoint string = cosmosDb.outputs.cosmosDbEndpoint
output cosmosDbName string = databaseName
output openAiEndpoint string = openAi.outputs.openAiEndpoint
output openAiName string = openAi.outputs.openAiName
