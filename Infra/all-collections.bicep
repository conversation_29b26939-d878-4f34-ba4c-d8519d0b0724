@description('The name of the Azure Cosmos DB account')
param cosmosDbAccountName string = 'my-cosmos-account'

@description('The list of all collections to deploy')
param collections array = [
  {
    databaseName: 'myDatabase'
    name: 'myCollection'
    partitionKeyPath: '/myPartitionKey'
    uniqueKeys: []
    defaultTtl: -1
    excludedIndexPaths: []
    compositeIndexes: []
  }
]

resource cosmosDbAccount 'Microsoft.DocumentDB/databaseAccounts@2025-05-01-preview' = {
  name: cosmosDbAccountName
  location: resourceGroup().location
  properties: {
    databaseAccountOfferType: 'Standard'
    consistencyPolicy: {
      defaultConsistencyLevel: 'Session'
    }
    locations: [
      {
        locationName: resourceGroup().location
        failoverPriority: 0
        isZoneRedundant: false
      }
    ]
    capabilities: [
      {
        name: 'EnableServerless'
      }
    ]
  }
}

var databaseNames = union(map(collections, item => item.databaseName), [])

resource databaseResources 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases@2025-05-01-preview' = [
  for db in databaseNames: {
    parent: cosmosDbAccount
    name: db
    properties: {
      resource: {
        id: db
      }
    }
  }
]

// Create containers in a module to avoid parent reference issues
module containerModule 'container.bicep' = [
  for (col, i) in collections: {
    name: 'container-${col.name}-${i}'
    params: {
      cosmosDbAccountName: cosmosDbAccountName
      databaseName: col.databaseName
      containerName: col.name
      partitionKeyPath: col.partitionKeyPath
      defaultTtl: contains(col, 'defaultTtl') ? col.defaultTtl ?? -1 : -1
      uniqueKeys: contains(col, 'uniqueKeys') ? col.uniqueKeys ?? [] : []
      excludedIndexPaths: contains(col, 'excludedIndexPaths') ? col.excludedIndexPaths ?? [] : []
      compositeIndexes: contains(col, 'compositeIndexes') ? col.compositeIndexes ?? [] : []
      fullTextPolicy: contains(col, 'fullTextPolicy') ? col.fullTextPolicy ?? {} : {}
      fullTextIndexes: contains(col, 'fullTextIndexes') ? col.fullTextIndexes ?? [] : []
    }
    dependsOn: [
      databaseResources
    ]
  }
]

// Outputs
output cosmosDbEndpoint string = 'https://${cosmosDbAccount.name}.documents.azure.com:443/'
