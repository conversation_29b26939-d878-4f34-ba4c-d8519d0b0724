{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"environmentName": {"value": "dev"}, "enableMockDataUpload": {"value": "false"}, "logLevel": {"value": "INFO"}, "location": {"value": "west<PERSON>"}, "keyVaultName": {"value": "peepsapp-vault-dev"}, "deployOpenAi": {"value": true}, "deployAzureAdSso": {"value": true}, "azureAdAppName": {"value": "Peeps App SSO Dev"}, "azureAdRedirectUris": {"value": ["https://dev.peepsapp.ai/auth/azure-ad/callback"]}, "azureAdLogoutUrl": {"value": "https://dev.peepsapp.ai/auth/logout"}, "enableCustomDomain": {"value": true}, "customDomainName": {"value": "dev.peepsapp.ai"}, "containerCertificateName": {"value": "dev.peepsapp.ai-peepsapp-************"}, "picturesStorageAccountName": {"value": "peepsappblobdev"}, "picturesOriginalsContainerName": {"value": "pictures-originals"}, "picturesThumbnailsContainerName": {"value": "pictures-thumbnails"}, "modelDeployments": {"value": [{"name": "gpt-4o", "model": {"name": "gpt-4o", "version": "2024-05-13", "format": "OpenAI"}, "skuName": "Standard", "capacity": 1}]}, "collections": {"value": [{"databaseName": "peepsapp-cosmos-db-dev", "name": "communities", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "people", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "conversations", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "events", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "messages", "partitionKeyPath": "/id"}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "passkey_credentials", "partitionKeyPath": "/person_id", "uniqueKeys": [{"paths": ["/credential_id"]}]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "registration_challenges", "partitionKeyPath": "/person_id", "defaultTtl": 300}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "authentication_challenges", "partitionKeyPath": "/person_id", "defaultTtl": 300}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "invite_tokens", "partitionKeyPath": "/id", "uniqueKeys": [{"paths": ["/token"]}, {"paths": ["/email"]}]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "session_tokens", "partitionKeyPath": "/person_id", "defaultTtl": 2592000}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "audit_logs", "partitionKeyPath": "/id", "defaultTtl": 7776000}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "locks", "partitionKeyPath": "/type", "defaultTtl": 30, "excludedIndexPaths": [{"path": "/\"type\"/?"}]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "connections", "partitionKeyPath": "/owner_person_id", "excludedIndexPaths": [], "compositeIndexes": [[{"path": "/status", "order": "ascending"}, {"path": "/requester_person_id", "order": "ascending"}], [{"path": "/status", "order": "ascending"}, {"path": "/requestee_person_id", "order": "ascending"}]], "fullTextPolicy": {"defaultLanguage": "en-US", "fullTextPaths": [{"path": "/person_preview/name", "language": "en-US"}, {"path": "/person_preview/last_name", "language": "en-US"}, {"path": "/person_preview/current_role", "language": "en-US"}, {"path": "/person_preview/current_company", "language": "en-US"}, {"path": "/person_preview/location", "language": "en-US"}]}, "fullTextIndexes": [{"path": "/person_preview/name"}, {"path": "/person_preview/last_name"}, {"path": "/person_preview/current_role"}, {"path": "/person_preview/current_company"}, {"path": "/person_preview/location"}]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "posts", "partitionKeyPath": "/author_person_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "comments", "partitionKeyPath": "/target_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "person_reactions", "partitionKeyPath": "/author_person_id", "uniqueKeys": [{"paths": ["/target_id"]}], "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "reactions", "partitionKeyPath": "/target_id", "uniqueKeys": [{"paths": ["/author_person_id"]}], "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "active_jobs", "partitionKeyPath": "/action"}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "feeds", "partitionKeyPath": "/person_id", "compositeIndexes": [[{"path": "/created_at", "order": "descending"}, {"path": "/id", "order": "descending"}]]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "pictures", "partitionKeyPath": "/parent_id", "compositeIndexes": [[{"path": "/parent_type", "order": "ascending"}, {"path": "/parent_id", "order": "ascending"}]]}, {"databaseName": "peepsapp-cosmos-db-dev", "name": "notes", "partitionKeyPath": "/author_id", "defaultTtl": -1, "indexingPolicy": {"indexingMode": "consistent"}, "includedPaths": [{"path": "/object_id/?"}, {"path": "/created_at/?", "indexes": [{"dataType": "Number", "precision": -1, "kind": "Range", "order": "Descending"}]}, {"path": "/note_group_id/?"}], "excludedIndexPaths": [{"path": "/content/*"}]}]}}}