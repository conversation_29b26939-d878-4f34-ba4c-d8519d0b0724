#!/bin/bash

set -e

VAULT_NAME=$1

if [ -z "$VAULT_NAME" ]; then
  echo "❌ Usage: ./grant_kv_access.sh <vault-name>"
  exit 1
fi

echo "🔍 Fetching your object ID..."
OBJECT_ID=$(az ad signed-in-user show --query id -o tsv)

if [ -z "$OBJECT_ID" ]; then
  echo "❌ Could not resolve your object ID."
  exit 1
fi

echo "✅ Your object ID: $OBJECT_ID"

VAULT_ID=$(az keyvault show --name "$VAULT_NAME" --query id -o tsv)

echo "🔍 Checking current role assignments..."
HAS_CONTRIBUTOR=$(az role assignment list \
  --assignee "$OBJECT_ID" \
  --scope "$VAULT_ID" \
  --query "[?roleDefinitionName=='Key Vault Secrets Officer']" -o tsv)

if [ -n "$HAS_CONTRIBUTOR" ]; then
  echo "✅ You already have Key Vault Secrets Officer access to '$VAULT_NAME'."
else
  echo "⚙️ Granting 'Key Vault Secrets Officer' on $VAULT_NAME..."
  az role assignment create \
    --assignee "$OBJECT_ID" \
    --role "Key Vault Secrets Officer" \
    --scope "$VAULT_ID"

  echo "✅ Access granted. Note: RBAC changes may take a few minutes to propagate."
fi
