# Error Handling System Documentation

## Overview

The PeepsAPI application implements a comprehensive error handling system designed to:

1. Provide consistent error responses across all endpoints
2. Capture detailed error information for debugging
3. Present user-friendly error messages to clients
4. Protect sensitive information in production environments

This document explains how the error handling system works and how to use it effectively.

## Architecture

The error handling system consists of several components:

### 1. Custom Exception Classes

All custom exceptions inherit from `PeepsAPIException`, which extends FastAPI's `HTTPException`.
These exceptions include standardized fields:

- `status_code`: HTTP status code
- `error_code`: Application-specific error code (e.g., "AUTH_ERROR")
- `error_type`: Type of error (e.g., "authentication_error")
- `message`: Human-readable error message
- `details`: Additional error details (optional)

Common exception types include:

- `AuthenticationError`: For authentication failures
- `AuthorizationError`: For permission issues
- `ValidationError`: For input validation failures
- `ResourceNotFoundError`: When requested resources don't exist
- `RateLimitError`: When rate limits are exceeded
- `ServerError`: For internal server errors

### 2. Global Exception Handlers

The application registers global exception handlers in `middleware/error_handlers.py`:

- `peepsapi_exception_handler`: Handles all `PeepsAPIException` instances
- `validation_exception_handler`: Handles validation errors
- `http_exception_handler`: Handles standard FastAPI `HTTPException`
- `general_exception_handler`: Catches all other exceptions

These handlers ensure that all errors return a consistent JSON response format.

### 3. Error Response Model

All error responses use the `ErrorResponse` model:

```python
class ErrorResponse(BaseResponse):
    success: bool = False
    error_code: str
    error_type: str
    message: str
    details: Optional[Dict[str, Any]] = None
```

### 4. Error Handling Decorator

The `@handle_exceptions` decorator simplifies error handling in route handlers:

```python
@router.get("/example")
@handle_exceptions(error_code_prefix="EXAMPLE")
def example_route():
    # Your code here
```

This decorator catches exceptions and converts them to appropriate HTTP responses.

The decorator supports both synchronous and asynchronous functions:

```python
# For synchronous functions
@router.get("/sync-example")
@handle_exceptions(error_code_prefix="SYNC")
def sync_example():
    # Synchronous code here
    return {"result": "success"}

# For asynchronous functions
@router.get("/async-example")
@handle_exceptions(error_code_prefix="ASYNC")
async def async_example():
    # Asynchronous code here
    return {"result": "success"}
```

The decorator automatically detects whether the function is a coroutine function (defined with `async def`) and handles it appropriately.

### 5. Authentication Helper

The `authenticate_request` function simplifies authentication in route handlers:

```python
@router.get("/protected")
@handle_exceptions(error_code_prefix="AUTH")
async def protected_route(
    request: Request,
    session_token: Optional[str] = Cookie(None),
    azure_ad_token: Optional[str] = Cookie(None, alias=AZURE_AD_COOKIE_NAME),
):
    # Use the centralized authentication function
    is_authenticated = await auth_service.authenticate_request(
        request=request,
        session_token=session_token,
        azure_ad_token=azure_ad_token,
    )

    # Handle unauthenticated users
    if not is_authenticated:
        # Redirect to login or return error
        ...

    # Continue with authenticated request
    ...
```

This function:
1. Checks for JWT and Azure AD tokens
2. Validates the tokens
3. Sets the request state with authentication information
4. Returns a boolean indicating whether authentication was successful

## Using the Error Handling System

### Adding Error Handling to Routes

To add error handling to a route:

1. Import the decorator:
   ```python
   from peepsapi.utils.decorators import handle_exceptions
   ```

2. Apply the decorator to your route function:
   ```python
   @router.get("/resource")
   @handle_exceptions(error_code_prefix="RESOURCE")
   def get_resource():
       # Your code here
   ```

The `error_code_prefix` parameter is used to create unique error codes for each route.

### Raising Custom Exceptions

When an error occurs, raise an appropriate exception:

```python
from peepsapi.utils.error_handling import ValidationError, ResourceNotFoundError

# For validation errors
if not is_valid(data):
    raise ValidationError(
        message="Invalid data format",
        error_code="INVALID_FORMAT",
        details={"field": "name", "issue": "too_short"}
    )

# For not found errors
if not resource:
    raise ResourceNotFoundError(
        message="Resource not found",
        error_code="RESOURCE_NOT_FOUND",
        details={"resource_id": resource_id}
    )
```

### Creating Custom Exception Types

To create a new exception type:

```python
from peepsapi.utils.error_handling import PeepsAPIException
from fastapi import status

class CustomError(PeepsAPIException):
    def __init__(
        self,
        message: str = "Custom error occurred",
        error_code: str = "CUSTOM_ERROR",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            error_code=error_code,
            error_type="custom_error",
            message=message,
            details=details,
        )
```

## Client-Side Error Handling

Clients should handle error responses as follows:

1. Check if the response status code indicates an error (4xx or 5xx)
2. Parse the JSON response body
3. Extract the `error_code`, `error_type`, `message`, and `details` fields
4. Display appropriate error messages to users
5. Take action based on specific error codes if needed

Example JavaScript:

```javascript
async function makeApiRequest(endpoint, method, body) {
    try {
        const options = {
            method: method,
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include'
        };

        if (method !== 'GET' && body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(`${API_BASE_URL}${endpoint}`, options);

        if (!response.ok) {
            const errorData = await response.json();

            // Handle the standardized error response
            console.error(`Error ${errorData.error_code}: ${errorData.message}`);

            // You can handle specific error types differently
            if (errorData.error_type === 'authentication_error') {
                // Redirect to login page
                window.location.href = '/dashboard/login';
            }

            throw new Error(errorData.message);
        }

        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}
```

## Error Monitoring

The application includes error monitoring capabilities:

1. All errors are logged with contextual information
2. Authentication-related errors are tracked in the auth monitoring system
3. Error statistics are available in the dashboard

## Best Practices

1. **Use Specific Exceptions**: Choose the most specific exception type for each error
2. **Include Helpful Messages**: Error messages should help users understand what went wrong
3. **Add Relevant Details**: Include details that help diagnose the issue
4. **Protect Sensitive Data**: Never include sensitive data in error responses
5. **Log Extensively**: Log additional details that aren't included in the response
6. **Handle Client-Side**: Ensure client-side code properly handles and displays errors
