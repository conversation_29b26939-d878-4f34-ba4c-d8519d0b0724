# Rate Limiting Security Enhancement

This document outlines the current state of rate limiting in the PeepsAPI application and proposes enhancements to improve security.

## Current Implementation

### Rate Limiter Class

The application uses a `RateLimiter` class in `peepsapi/auth/services/rate_limiter.py` with the following features:

- Tracks requests by IP address and user ID
- Configurable time windows and request limits
- Blocks IPs and users for a configurable duration (15 minutes by default)
- In-memory storage (resets on application restart)

### Existing Rate Limiters

| Rate Limiter                | Window      | Max Requests | Purpose                |
|-----------------------------|-------------|--------------|------------------------|
| `auth_rate_limiter`         | 60 seconds  | 5 requests   | General auth endpoints |
| `registration_rate_limiter` | 300 seconds | 10 requests  | Registration endpoints |
| `login_rate_limiter`        | 60 seconds  | 5 requests   | Login endpoints        |

### Current Coverage

The following endpoints currently have rate limiting applied:

- **Login endpoints**:
  - `/auth/login/challenge` - Uses `login_rate_limiter`
  - `/auth/login/verify` - Uses `login_rate_limiter`

- **Registration endpoints**:
  - `/auth/invite` - Uses `registration_rate_limiter`
  - `/auth/register/challenge` - Uses `registration_rate_limiter`
  - `/auth/register/verify` - Uses `registration_rate_limiter`

- **Recovery endpoints**:
  - `/auth/recovery/initiate` - Uses `registration_rate_limiter`

## Missing Rate Limiting

The following endpoints currently lack rate limiting:

### Azure AD Endpoints
- `/auth/azure-ad/login` - No rate limiting
- `/auth/azure-ad/callback` - No rate limiting
- `/auth/azure-ad/logout` - No rate limiting
- `/auth/azure-ad/me` - No rate limiting

### Device Management Endpoints
- `/auth/devices` - No rate limiting
- `/auth/devices/{device_id}/rename` - No rate limiting
- `/auth/devices/{device_id}` (DELETE) - No rate limiting

### Session Endpoints
- `/auth/logout` - No rate limiting
- `/auth/refresh-token` - No rate limiting

### Recovery Endpoints
- `/auth/recovery/register/challenge` - Using `registration_rate_limiter` but should use a dedicated recovery rate limiter
- `/auth/recovery/register/verify` - No rate limiting

## Proposed Enhancements

### 1. Additional Rate Limiters

Create more specialized rate limiters for different types of endpoints:

```python
# Add these to peepsapi/auth/services/rate_limiter.py
recovery_rate_limiter = RateLimiter(window_seconds=300, max_requests=5)  # Stricter than registration
azure_ad_rate_limiter = RateLimiter(window_seconds=60, max_requests=10)  # Azure AD auth
device_rate_limiter = RateLimiter(window_seconds=60, max_requests=20)    # Device management
session_rate_limiter = RateLimiter(window_seconds=60, max_requests=10)   # Session management
```

### 2. More Granular Rate Limiting

Enhance the `RateLimiter` class with:

1. **User-Agent Based Limiting**:
   - Track requests by user agent to identify and block automated tools
   - Implement stricter limits for suspicious user agents

2. **Progressive Rate Limiting**:
   - Implement increasing block durations for repeated violations:
     - First violation: 15 minutes (current)
     - Second violation: 1 hour
     - Third violation: 24 hours

3. **Endpoint-Specific Tracking**:
   - Track rate limits per endpoint to prevent distributed attacks
   - Allow different limits for different endpoints using the same limiter

### 3. Improved Rate Limit Responses

1. **Standardized Rate Limit Responses**:
   - Create a custom `RateLimitExceededResponse` with:
     - Retry-After header
     - Detailed message about why the request was rate limited
     - Remaining time until the rate limit is reset

2. **Rate Limit Headers**:
   - Add standard rate limit headers to all responses:
     - `X-RateLimit-Limit`: Maximum requests allowed in the window
     - `X-RateLimit-Remaining`: Remaining requests in the current window
     - `X-RateLimit-Reset`: Time when the rate limit window resets

### 4. Monitoring and Alerting

1. **Enhanced Logging**:
   - Log all rate limit events with details like IP, endpoint, user ID
   - Track patterns of rate limit violations

2. **Dashboard Metrics**:
   - Add rate limiting metrics to the monitoring dashboard
   - Show trends in rate limit violations
   - Alert on unusual patterns of rate limit violations

3. **Integration with Security Monitoring**:
   - Send rate limit violations to a security monitoring system
   - Correlate rate limit violations with other security events

## Implementation Priority

1. Apply rate limiting to all missing endpoints
2. Create additional specialized rate limiters
3. Enhance the rate limiter with more granular controls
4. Improve rate limit responses
5. Add monitoring and alerting

## Future Considerations

1. **Persistent Storage**:
   - Move from in-memory storage to a persistent store (Redis, database)
   - This prevents rate limit resets on application restart

2. **Distributed Rate Limiting**:
   - Implement distributed rate limiting for multi-instance deployments
   - Ensure consistent rate limiting across all instances

3. **IP Reputation System**:
   - Maintain a reputation system for IPs based on their behavior
   - Apply stricter limits to IPs with poor reputation

4. **Geographic Rate Limiting**:
   - Apply different rate limits based on geographic location
   - Block requests from high-risk regions
