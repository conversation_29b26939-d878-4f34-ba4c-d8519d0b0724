# Case Conversion Standards

This document outlines the case conversion standards and implementation used in the PeepsAPI project.

## Overview

The PeepsAPI project follows these standards:

1. **Internal Python Code**: Uses PEP8 snake_case for readability and consistency
2. **API Responses and Requests**: Exposes camelCase for frontend compatibility (JavaScript, iOS, Android, etc.)

This approach allows each platform to use its preferred case convention while maintaining consistency across the application.

## Implementation

The case conversion is handled by two components:

1. **BaseModelWithExtra class** in `peepsapi/models/base.py`:
   - Uses Pydantic's built-in `alias_generator` to convert between snake_case and camelCase
   - Primarily handles the conversion for API responses

2. **CaseConversionMiddleware** in `peepsapi/middleware/case_conversion.py`:
   - Automatically converts all incoming JSON request bodies from camelCase to snake_case
   - Ensures consistent case conversion even for complex nested objects
   - Added to the FastAPI application in `peepsapi/main.py`

### How It Works

#### Incoming Requests

When a client sends a request with camelCase field names:

```json
{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "emailAddress": "<EMAIL>"
}
```

The middleware automatically converts it to snake_case before it reaches your route handlers:

```json
{
  "first_name": "<PERSON>",
  "last_name": "Doe",
  "email_address": "<EMAIL>"
}
```

This allows your Python code to work with snake_case field names, following PEP8 conventions.

#### Outgoing Responses

When your route handler returns a response:

```python
return {
    "first_name": "John",
    "last_name": "Doe",
    "email_address": "<EMAIL>"
}
```

Pydantic's `alias_generator` automatically converts it to camelCase before sending it to the client:

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "emailAddress": "<EMAIL>"
}
```

This allows clients to work with camelCase field names, following their language conventions.

## Implementation Details

The case conversion functionality is centralized in `peepsapi/utils/case_conversion.py`:

```python
def to_camel_case(snake_str: str) -> str:
    """Convert a snake_case string to camelCase."""
    if not snake_str:
        return snake_str
    components = snake_str.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

def to_snake_case(camel_str: str) -> str:
    """Convert a camelCase string to snake_case."""
    if not camel_str:
        return camel_str
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
```

The `BaseModelWithExtra` class in `peepsapi/models/base.py` uses the `to_camel_case` function:

```python
from peepsapi.utils.case_conversion import to_camel_case

class BaseModelWithExtra(BaseModel):
    model_config = {
        "extra": "allow",
        "alias_generator": to_camel_case,  # Convert snake_case to camelCase for API
        "populate_by_name": True,  # Allow populating by field name or alias
    }
```

This implementation leverages Pydantic's built-in `alias_generator` to handle the case conversion automatically.

The middleware is added to the FastAPI application in `peepsapi/main.py`:

```python
# Add case conversion middleware to convert camelCase to snake_case in requests
app.add_middleware(CaseConversionMiddleware)
```

## Usage Examples

### Defining Models

When defining models, use snake_case for field names:

```python
class Person(BaseModelWithExtra):
    id: str
    first_name: str
    last_name: str
    email_address: str
```

### Accessing Model Fields in Python Code

In Python code, access fields using snake_case:

```python
person = Person(id="123", first_name="John", last_name="Doe", email_address="<EMAIL>")
print(person.first_name)  # Output: John
print(person.email_address)  # Output: <EMAIL>
```

### API Response

When the model is serialized to JSON for an API response, the keys will be in camelCase:

```json
{
  "id": "123",
  "firstName": "John",
  "lastName": "Doe",
  "emailAddress": "<EMAIL>"
}
```

### API Request

When receiving a request with camelCase keys, they will be automatically converted to snake_case:

```python
# Request JSON:
# {
#   "firstName": "Jane",
#   "lastName": "Smith",
#   "emailAddress": "<EMAIL>"
# }

@app.post("/people")
def create_person(person_data: PersonCreate):
    # person_data.first_name == "Jane"
    # person_data.last_name == "Smith"
    # person_data.email_address == "<EMAIL>"
    ...
```

## JavaScript Usage

### Accessing API Response Data in JavaScript

When working with API responses in JavaScript, always use camelCase to access properties:

```javascript
// CORRECT: Use camelCase when accessing properties from API responses
const token = data.inviteToken;
const url = data.inviteUrl;
const expiresAt = data.expiresAt;

// INCORRECT: This will return undefined
const token = data.invite_token;
```

### Sending API Requests from JavaScript

When sending data to the API from JavaScript, use snake_case for field names:

```javascript
// CORRECT: Use snake_case for request body fields
const requestBody = {
    invite_token: token,
    device_name: deviceName
};

// INCORRECT: This will not be properly mapped to the backend model
const requestBody = {
    inviteToken: token,
    deviceName: deviceName
};
```

## Common Issues

### Undefined Values in JavaScript

If you're getting `undefined` when accessing a property from an API response in JavaScript, check if you're using the correct case:

```javascript
// If this returns undefined:
console.log(data.invite_token);

// Try using camelCase instead:
console.log(data.inviteToken);
```

### API Validation Errors

If you're getting validation errors from the API, check if you're using the correct case in your request body:

```javascript
// If this causes validation errors:
const requestBody = {
    inviteToken: token,
    deviceName: deviceName
};

// Try using snake_case instead:
const requestBody = {
    invite_token: token,
    device_name: deviceName
};
```

## Benefits

This approach provides several benefits:

1. **Consistent Standards**: Python code follows PEP8 (snake_case), while API clients use their preferred convention (camelCase)
2. **Automatic Conversion**: No manual conversion needed in your route handlers
3. **Client Flexibility**: Clients can use camelCase without worrying about the backend implementation
4. **Maintainability**: Easier to maintain code that follows language-specific conventions

## Limitations

1. The middleware only processes JSON request bodies
2. It doesn't handle special cases like acronyms (e.g., "HTTPRequest" -> "http_request")
3. It doesn't handle arrays at the root level of the request body

## Best Practices

1. Always use snake_case in Python code
2. Always use camelCase when accessing API response data in JavaScript
3. Always use snake_case when sending data to the API from JavaScript
4. Add comments in JavaScript code to clarify the case convention
5. Let the automatic conversion handle camelCase for API responses and requests
6. If you need to manually convert between cases, use the utility functions
7. When updating existing models, make sure to update any code that accesses the fields
8. When adding new models, follow the snake_case convention from the start
