# Logging System Documentation

This guide explains the logging functionality in the PeepsAPI project, including configuration options, usage patterns, and troubleshooting tips.

## Table of Contents

1. [Local Development Logging](#local-development-logging)
   - [Available Commands](#available-commands)
   - [Logging Configuration](#logging-configuration)
   - [Log Format](#log-format)
   - [Log Level Filtering](#log-level-filtering)
2. [Server Logging](#server-logging)
   - [Azure Application Insights](#azure-application-insights)
   - [Log Retention](#log-retention)
3. [Contextual Logging](#contextual-logging)
   - [Using the ContextualLogger](#using-the-contextuallogger)
   - [Available Context Fields](#available-context-fields)
4. [Graphic Logging Standards](#graphic-based-logging-standards)
   - [Emoji Prefix Table](#emoji-prefix-table)
   - [Usage Guidelines](#usage-guidelines)
   - [Examples](#examples)
5. [Logging Best Practices](#logging-best-practices)
   - [Using Proper Log Levels](#using-proper-log-levels)
   - [Adding Context Information](#adding-context-information)
   - [Avoiding Print Statements](#avoiding-print-statements)
   - [Error Logging](#error-logging)
6. [Troubleshooting](#troubleshooting)
   - [Authentication Issues](#troubleshooting-authentication-issues)
   - [Common Logging Problems](#common-logging-problems)
7. [Related Fixes](#related-fixes)
   - [Error Handling Decorator Fix](#error-handling-decorator-fix)

## Local Development Logging

### Available Commands

The following commands are available for running the application with enhanced logging:

```bash
# Start the application with standard logging over HTTPS
# Logging defaults: LOG_LEVEL=INFO, LOG_FORMAT=Standard, AZURE_LOG_LEVEL=WARNING
make start

# Start the application with configurable logging over HTTPS
# Logging defaults: LOG_LEVEL=DEBUG, LOG_FORMAT=Detailed, AZURE_LOG_LEVEL=WARNING
make debug

# Start with specific LOG_LEVEL (DEBUG, INFO, WARNING, ERROR, CRITICAL)
make debug DEBUG

# Start with specific LOG_LEVEL and LOG_FORMAT
make debug ERROR Detailed

# Start with specific LOG_FORMAT only (Standard or Detailed)
make debug Standard

# Start with specific LOG_LEVEL, LOG_FORMAT, and AZURE_LOG_LEVEL
make debug ERROR detailed INFO
```

### Logging Configuration

The enhanced logging is configured in the `simple-log-config.json` file. This configuration:

1. Sets the specified log level for all loggers (peepsapi modules, uvicorn, Azure, and the root logger)
2. Uses a detailed formatter that includes:
   - Timestamp
   - Log level
   - Module name
   - Line number (in detailed format)
   - Message

### Log Format

The standard log format includes the following information:

```
TIMESTAMP | LEVEL | MODULE | MESSAGE
```

For example:
```
2023-06-01 12:34:56 | DEBUG    | peepsapi.auth | User authenticated
```

The detailed log format includes line numbers:

```
TIMESTAMP | LEVEL | MODULE:LINE | MESSAGE
```

For example:
```
2023-06-01 12:34:56 | DEBUG    | peepsapi.auth:123 | User authenticated
```

### Log Level Filtering

When you set a specific log level (e.g., `make debug ERROR detailed INFO`), all loggers in the application will be configured to that level. This means:

1. For `ERROR` level, only ERROR and CRITICAL logs will be displayed
2. For `WARNING` level, only WARNING, ERROR, and CRITICAL logs will be displayed
3. For `INFO` level, only INFO, WARNING, ERROR, and CRITICAL logs will be displayed
4. For `DEBUG` level, all logs will be displayed

This filtering applies to all loggers in the application, including:
- All peepsapi modules
- Uvicorn server logs
- Azure SDK logs (controlled by AZURE_LOG_LEVEL)
- Root logger

For example, running `make debug ERROR detailed INFO` will:
- Only show ERROR and CRITICAL logs from all sources
- Use the detailed format that includes line numbers
- Set Azure SDK logs to INFO level
- Filter out all DEBUG, INFO, and WARNING logs from the application

### Customizing Logging

#### Switching Between Standard and Detailed Logging

The logging configuration supports two different formats:

1. **Standard Format**: Shows timestamp, log level, module name, and message
2. **Detailed Format**: Shows timestamp, log level, module name, line number, and message

#### Configuring Log Format

You can configure the log format in two ways:

1. **Using the `debug` command with parameters**:
   ```bash
   # For standard format
   make debug Standard

   # For detailed format
   make debug Detailed

   # Combine with log level
   make debug ERROR Detailed
   ```

2. **Manually editing the configuration file**:
   1. Open the `simple-log-config.json` file
   2. Find the "loggers" section
   3. For each logger (peepsapi, peepsapi.auth, etc.), change the "handlers" array:
      - For standard format: `"handlers": ["console"]`
      - For detailed format: `"handlers": ["detailed_console"]`

Example for standard format:
```json
"peepsapi": {
    "handlers": ["console"],
    "level": "DEBUG",
    "propagate": false
}
```

Example for detailed format:
```json
"peepsapi": {
    "handlers": ["detailed_console"],
    "level": "DEBUG",
    "propagate": false
}
```

### Other Customizations

You can also:

1. Adjust the log levels for specific modules (DEBUG, INFO, WARNING, ERROR, CRITICAL)
2. Modify the formatters to include more or less information
3. Add new loggers for specific modules

### Environment Variables

You can also control logging through environment variables:

```bash
# Set the log level for the entire application (HTTPS mode)
LOG_LEVEL=DEBUG make start

# Enable audit logging to the database (HTTPS debug mode)
ENABLE_AUDIT_LOGGING=true make debug

# Combine multiple environment variables with specific log level and format
ENABLE_AUDIT_LOGGING=true make debug ERROR Detailed
```

Note: When using both environment variables and command-line arguments, the command-line arguments will take precedence for configuring the log level and format in the logging configuration file.

## Server Logging

The Docker image now includes a basic logging configuration. When the container
starts, Uvicorn loads `simple-log-config.json`, which sets the default log level
to `INFO` for all application loggers. You can override this level by providing
the `LOG_LEVEL` environment variable when deploying the container or by setting
the `logLevel` parameter in the Bicep templates:

```bash
az containerapp update \
  --name <app> \
  --resource-group <rg> \
  --set-env-vars LOG_LEVEL=DEBUG

# Or using the Makefile wrapper
LOG_LEVEL=DEBUG make deploy-service ENV=dev
```

This allows `logger.info` and `logger.debug` statements to appear in the Azure
Container App logs.

## Contextual Logging

The PeepsAPI application uses a `ContextualLogger` class that automatically includes relevant context information in log entries.

### Using the ContextualLogger

To use the contextual logger:

```python
from peepsapi.utils.logging import get_logger

# Create a logger for your module
logger = get_logger(__name__)

# Basic logging
logger.info("This is an informational message")

# Logging with request context
logger.info("User authenticated", request=request)

# Logging with person ID
logger.info("Profile updated", person_id=person.id)

# Logging with additional context
logger.error(
    "Payment processing failed",
    request=request,
    person_id=person.id,
    extra={"payment_id": payment.id, "amount": payment.amount}
)
```

### Available Context Fields

The contextual logger automatically includes the following information when available:

- **Request path**: The URL path of the request
- **Request method**: The HTTP method (GET, POST, etc.)
- **Client IP**: The IP address of the client
- **User agent**: The client's user agent string
- **Person ID**: The ID of the authenticated person
- **Authentication source**: The source of authentication (jwt or azure_ad)
- **For Azure AD auth**: User email, name, and any override person ID

## Graphic-Based Logging Standards

To improve log readability and make it easier to scan logs for specific types of information, we use a standardized set of emoji prefixes for all log messages.

### Emoji Prefix Table

| Emoji | Meaning / When to Use                   |
|-------|------------------------------------------|
| ✅    | Success / operation completed            |
| ❌    | Error / failure                          |
| ⚠️    | Warning / degraded behavior              |
| 🛠️    | Setup / configuration steps              |
| 🔧    | Debug logs / detailed internals          |
| 🎯    | Starting a specific task / step          |
| 📦    | Package or dependency loading            |
| 🚀    | App or server start                      |
| 🧪    | Validation / test / checks               |
| 📝    | General info / notes                     |
| 🔐    | Authentication / passkey / security logs |
| ⏳    | Waiting / in-progress status             |
| 🧠    | AI features / GPT / reasoning logs       |
| 🧹    | Cleanup / teardown                       |
| 📤    | Sending data / request outbound          |
| 📥    | Receiving data / response inbound        |

### Usage Guidelines

1. **Always include an emoji prefix** at the beginning of each log message
2. **Choose the most appropriate emoji** based on the message content and context
3. **Be consistent** with emoji usage across similar log messages
4. **Avoid duplicating emojis** if one already exists in the message
5. **Use only one emoji** per log message (the most relevant one)
6. **For structured logs** (JSON format), add a `tag` or `emoji` field instead

### Examples

```python
# Success logs
logger.info("✅ User successfully authenticated")
logger.info("✅ Payment processed successfully")

# Error logs
logger.error("❌ Failed to connect to database")
logger.error("❌ Payment processing failed: Invalid card")

# Warning logs
logger.warning("⚠️ Rate limit threshold reached")
logger.warning("⚠️ Database connection pool running low")

# Debug logs
logger.debug("🔧 Processing request parameters")
logger.debug("🔧 Query execution plan: SELECT * FROM users")

# Starting tasks
logger.info("🎯 Starting data migration process")
logger.info("🎯 Processing user input: 'Hello world'")

# Authentication logs
logger.info("🔐 New passkey registered for user")
logger.info("🔐 Token refreshed for session")

# AI-related logs
logger.info("🧠 Parsing user intent with GPT")
logger.debug("🧠 Raw GPT response: {...}")

# Data transfer logs
logger.info("📤 Sending request to payment gateway")
logger.info("📥 Received response from external API")
```

## Logging Best Practices

### Using Proper Log Levels

Choose the appropriate log level based on the importance and purpose of the message:

- **DEBUG**: Detailed information, typically useful only for diagnosing problems
  ```python
  logger.debug(f"🔧 Processing request with parameters: {params}")
  ```

- **INFO**: Confirmation that things are working as expected
  ```python
  logger.info(f"✅ User {user_id} successfully authenticated")
  ```

- **WARNING**: An indication that something unexpected happened, or may happen in the near future
  ```python
  logger.warning(f"⚠️ Rate limit threshold reached for user {user_id}")
  ```

- **ERROR**: Due to a more serious problem, the software has not been able to perform some function
  ```python
  logger.error(f"❌ Failed to process payment: {str(error)}")
  ```

- **CRITICAL**: A serious error, indicating that the program itself may be unable to continue running
  ```python
  logger.critical(f"❌ Database connection failed: {str(error)}")
  ```

### Adding Context Information

Always include relevant context information in your log messages:

```python
# Good: Includes context information and emoji
logger.info(
    f"✅ Connection status updated",
    extra={
        "connection_id": connection_id,
        "old_status": old_status,
        "new_status": new_status,
    }
)

# Bad: Missing context and emoji
logger.info("Connection status updated")
```

When logging errors, include details about the error:

```python
try:
    # Some operation
    result = process_payment(payment_id)
except Exception as e:
    logger.error(
        f"❌ Payment processing failed: {str(e)}",
        extra={
            "payment_id": payment_id,
            "error_type": type(e).__name__,
        }
    )
```

### Avoiding Print Statements

Always use the logger instead of print statements:

```python
# Good: Uses logger with emoji
logger.debug(f"🔐 Token generated: {token[:5]}...")

# Bad: Uses print without emoji
print(f"Token generated: {token[:5]}...")
```

Benefits of using the logger over print statements:
1. Respects log level configuration (only shows logs at or above the configured level)
2. Includes contextual information (timestamps, module names, etc.)
3. Can be filtered and redirected to different outputs
4. Consistent formatting across the application

### Error Logging

When logging exceptions, follow these best practices:

1. **Log at the appropriate level** (usually ERROR or CRITICAL)
2. **Include the exception message** using `str(e)`
3. **Add context about where the error occurred**
4. **Include relevant IDs and parameters** (but be careful with sensitive data)
5. **Consider including the exception type** using `type(e).__name__`
6. **For critical errors, include the traceback** using `traceback.format_exc()`

Example:

```python
try:
    # Some operation that might fail
    result = process_data(data_id)
except Exception as e:
    logger.error(
        f"❌ Failed to process data: {str(e)}",
        extra={
            "data_id": data_id,
            "error_type": type(e).__name__,
            "traceback": traceback.format_exc() if is_critical else None,
        }
    )
```

## Troubleshooting

### Troubleshooting Authentication Issues

When debugging authentication issues, pay attention to:

1. Look for log messages from the `peepsapi.auth` module
2. Check for any error messages related to token validation
3. Look for messages containing "JWT token valid" or "Azure AD token valid"
4. Check the request state in middleware logs

### Common Logging Problems

- **Missing logs**: Ensure your log level is appropriate (e.g., DEBUG for detailed logs)
- **Too many logs**: Use a higher log level (e.g., ERROR) to reduce verbosity
- **Azure SDK logs**: These are set to WARNING by default but follow the ERROR/CRITICAL setting
- **"Attempt to overwrite 'name' in LogRecord" error**: Avoid using 'name' as a key in the `extra` dictionary when logging, as it conflicts with a built-in LogRecord attribute. Use a more specific key like 'user_name', 'search_name', etc. instead.
- **"Attempt to overwrite 'module' in LogRecord" error**: Avoid using 'module' as a key in the `extra` dictionary when logging, as it conflicts with a built-in LogRecord attribute. Use a more specific key like 'func_module', 'source_module', etc. instead.

## Related Fixes

### Error Handling Decorator Fix

The `handle_exceptions` decorator in `peepsapi/utils/decorators.py` has been updated to support both synchronous and asynchronous functions. This fixes errors like:

```
Error in get_person: object dict can't be used in 'await' expression
```

The decorator now automatically detects whether a function is a coroutine function (defined with `async def`) and handles it appropriately. This allows you to use the decorator with both types of functions without errors.

### LogRecord Reserved Attributes Fix

The `handle_exceptions` decorator in `peepsapi/utils/decorators.py` has been updated to avoid conflicts with Python's built-in LogRecord attributes. Specifically:

1. Changed `module` to `func_module` in the `extra` dictionary passed to logger calls
2. Updated the corresponding field in the ServerError details dictionary

This fixes errors like:

```
KeyError: "Attempt to overwrite 'module' in LogRecord"
```

Python's LogRecord class has several reserved attribute names that should not be used in the `extra` dictionary:

- `name`
- `level`
- `pathname`
- `lineno`
- `msg`
- `args`
- `exc_info`
- `func`
- `sinfo`
- `module`

When logging with the `extra` parameter, always use unique keys that don't conflict with these built-in attributes.
