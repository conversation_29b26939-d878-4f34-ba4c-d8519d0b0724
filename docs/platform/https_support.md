# HTTPS Support for PeepsAPI

This document explains how to use HTTPS with PeepsAPI for both local development and production environments.

You can run the local development server with either:
- `localhost` (default)
- `local.peepsapp.ai` (recommended for WebAuthn/passkey authentication)

## Important Note for WebAuthn/FIDO2

WebAuthn requires HTTPS except on localhost. However, browsers will reject WebAuthn operations on sites with TLS certificate errors. To solve this, we recommend using `mkcert` to generate locally-trusted certificates.

## Local Development

### Quick Start

First, install mkcert (required for WebAuthn):

```bash
# macOS
brew install mkcert nss

# Ubuntu/Debian
sudo apt install libnss3-tools mkcert

# Windows
choco install mkcert
```

Then generate certificates:

```bash
make generate-certs
```

Finally, start the server:

```bash
make start
```

This will:
1. Use the certificates in the `certs` directory
2. Start the server with HTTPS on port 8443
3. Access the site at `https://local.peepsapp.ai:8443`

### Using local.peepsapp.ai Domain (Recommended)

For WebAuthn/passkey authentication, it's recommended to use a proper domain name instead of localhost. The `make build` command automatically adds the required hosts file entry for `local.peepsapp.ai` on Unix based systems. If you need to add it manually:

1. **Add an entry to your hosts file**:

   ```bash
   # On macOS/Linux
   sudo sh -c 'echo "127.0.0.1 local.peepsapp.ai" >> /etc/hosts'

   # On Windows (PowerShell as Administrator)
   Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "127.0.0.1 local.peepsapp.ai" -Force
   ```

2. **Generate certificates with the domain**:

   ```bash
   make generate-certs
   ```

   This will create certificates that include `local.peepsapp.ai` in the Subject Alternative Name (SAN) field.

3. **Update your environment variables**:

   Add these to your `local.env` file:

   ```
   # HTTPS Configuration
   ENABLE_HTTPS=True
   HTTPS_CERT_FILE=certs/cert.pem
   HTTPS_KEY_FILE=certs/key.pem
   HTTPS_PORT=8443

   # WebAuthn settings
   WEBAUTHN_RP_ID=local.peepsapp.ai
   WEBAUTHN_RP_NAME=Peeps App (Local)
   WEBAUTHN_RP_ORIGIN=https://local.peepsapp.ai:8443
   ```

4. **Start the server**:

   ```bash
   make start
   ```

5. **Access the application**:

   Open your browser and navigate to:
   ```
   https://local.peepsapp.ai:8443
   ```

### Generate Certificates

[mkcert](https://github.com/FiloSottile/mkcert) is a simple tool for making locally-trusted development certificates. It automatically creates and installs a local CA in the system trust store, and generates certificates that browsers will trust.

**Note: mkcert is required for WebAuthn/FIDO2 authentication to work properly in local development.**

1. Install mkcert:

```bash
# macOS with Homebrew
brew install mkcert
brew install nss  # for Firefox support

# Ubuntu/Debian
sudo apt install libnss3-tools
sudo apt install mkcert
# or download from https://github.com/FiloSottile/mkcert/releases

# Windows with Chocolatey
choco install mkcert
```

2. Generate certificates:

```bash
make generate-certs
```

This will:
- Install a local CA if needed
- Generate certificates for `local.peepsapp.ai` and `localhost`
- Place them in the `certs` directory
- **Only generate certificates without starting the server**

> **Important:** WebAuthn/FIDO2 requires properly trusted certificates. Self-signed certificates will not work with WebAuthn.

### Configuration

You can configure HTTPS by adding the following to your `local.env` file:

```
# HTTPS Configuration
ENABLE_HTTPS=True
HTTPS_CERT_FILE=certs/cert.pem
HTTPS_KEY_FILE=certs/key.pem
HTTPS_PORT=8443
```

With these settings, the server will automatically use HTTPS when started with `make start`.

Add these settings to your `local.env` file to enable HTTPS:

### Command Line Options

The Makefile provides the following commands for HTTPS-related operations:

```bash
# Generate certificates
make generate-certs

# Start the server with HTTPS
make start

# Start with configurable logging
make debug [LOG_LEVEL] [LOG_FORMAT]
```

For more information about logging options, see the [Logging Documentation](../LOGGING.md).

### Browser Security

When using mkcert, the certificates are automatically trusted by your browser, so you won't see any security warnings. This is because mkcert installs a local Certificate Authority (CA) that your system trusts.

If you're using Firefox, you may need to install the NSS tools to make Firefox trust the certificates:

```bash
# macOS
brew install nss

# Ubuntu/Debian
sudo apt install libnss3-tools
```

Then run `mkcert -install` again to install the CA in Firefox's trust store.

## Production Environment

For production, we recommend using Azure App Service with managed certificates:

### Azure App Service Setup

1. **Create an App Service with HTTPS**

   Azure App Service provides built-in HTTPS support with managed certificates.

   ```bash
   # Example Azure CLI command
   az webapp create --resource-group myResourceGroup --plan myAppServicePlan --name myAppName --runtime "PYTHON|3.12"
   ```

2. **Configure HTTPS Settings**

   Ensure HTTPS-only traffic is enabled:

   ```bash
   az webapp update --resource-group myResourceGroup --name myAppName --https-only true
   ```

3. **Add Custom Domain and Certificate (Optional)**

   If you're using a custom domain, you can add it and configure a managed certificate:

   ```bash
   # Add custom domain
   az webapp config hostname add --webapp-name myAppName --resource-group myResourceGroup --hostname example.com

   # Create managed certificate
   az webapp config ssl create --resource-group myResourceGroup --name myAppName --hostname example.com
   ```

## WebAuthn Considerations

When using WebAuthn/FIDO2 for authentication, HTTPS is required except for localhost. Update your WebAuthn configuration:

```python
# For local development with local.peepsapp.ai
if os.getenv("DEBUG_MODE", "False").lower() == "true":
    rp_id = "local.peepsapp.ai"
    origin = "https://local.peepsapp.ai:8443"
else:
    # For production
    rp_id = "peeps.app"  # Your domain
    origin = "https://peeps.app"  # Your production URL
```

## Troubleshooting

- **Certificate Issues**: Regenerate certificates with `make generate-certs`
- **Port Conflicts**: Change the HTTPS port in your environment file
- **CORS Issues**: Ensure your CORS configuration includes the HTTPS origin
- **Domain Not Resolving**: Check your hosts file entry for `local.peepsapp.ai`
- **WebAuthn Errors**: Ensure the RP ID matches the domain you're using
