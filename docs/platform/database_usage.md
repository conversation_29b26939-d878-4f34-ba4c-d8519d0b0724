# Database Usage and Dependency Injection

This document provides guidelines for working with the database in the PeepsAPI application, focusing on the `CosmosContainer` class and dependency injection patterns.

## Table of Contents

- [Overview](#overview)
- [CosmosContainer Class](#cosmoscontainer-class)
- [Dependency Injection Patterns](#dependency-injection-patterns)
- [Model Serialization](#model-serialization)
- [Best Practices](#best-practices)
- [Common Pitfalls](#common-pitfalls)
- [Examples](#examples)

## Overview

The PeepsAPI application uses Azure Cosmos DB for data storage. To simplify database operations, we've implemented a `CosmosContainer` class that provides:

1. Lazy initialization of database connections
2. Convenience methods for common operations
3. Automatic serialization of Pydantic models
4. Integration with FastAPI's dependency injection system

## CosmosContainer Class

The `CosmosContainer` class (defined in `peepsapi/services/cosmos_db.py`) provides a wrapper around Azure Cosmos DB's container client. It offers several methods for working with data:

### Basic Methods

- `read_item(item, partition_key)`: Read an item from the container
- `create_item(body)`: Create an item in the container
- `replace_item(item, body)`: Replace an item in the container
- `upsert_item(body)`: Upsert an item in the container
- `delete_item(item, partition_key)`: Delete an item from the container
- `query_items(query, parameters, enable_cross_partition_query)`: Query items from the container
- `read_all_items()`: Read all items from the container

### Enhanced Methods for Pydantic Models

- `create_model(model)`: Create an item from a Pydantic model
- `replace_model(model)`: Replace an item with a Pydantic model
- `upsert_model(model)`: Upsert an item from a Pydantic model

These enhanced methods automatically handle serialization of Pydantic models using the `model_dump_json_safe()` method.

## Dependency Injection Patterns

### IMPORTANT: The Correct Way to Use CosmosContainer with FastAPI

When using `CosmosContainer` with FastAPI's dependency injection system, there are two patterns to be aware of:

#### Pattern 1: Direct Container Client Injection (Limited Functionality)

```python
# Define the container
invite_tokens_container = CosmosContainer("invite_tokens")

# Use it in a route
@router.post("/endpoint")
async def endpoint(container=Depends(invite_tokens_container)):
    # This gives you the raw ContainerProxy object
    # You can only use basic methods like create_item, replace_item, etc.
    # You CANNOT use enhanced methods like create_model, replace_model, etc.
    container.create_item(body={"id": "123", "data": "value"})
```

With this pattern, you get the raw `ContainerProxy` object, which doesn't have the enhanced methods for Pydantic models.

#### Pattern 2: Container Instance Injection (Recommended)

```python
# Define the container
invite_tokens_container = CosmosContainer("invite_tokens")

# Define a getter function
def get_invite_tokens_container():
    return invite_tokens_container

# Use it in a route
@router.post("/endpoint")
async def endpoint(container=Depends(get_invite_tokens_container)):
    # This gives you the CosmosContainer instance
    # You can use all methods, including enhanced methods for Pydantic models
    model = MyModel(id="123", data="value")
    container.create_model(model)
```

With this pattern, you get the `CosmosContainer` instance, which has all the enhanced methods for Pydantic models.

### Shared Container Instances

For commonly used containers, we define shared instances and getter functions in `peepsapi/auth/utils.py`:

```python
# Shared Cosmos DB container dependencies
invite_tokens_container = CosmosContainer("invite_tokens")
registration_challenges_container = CosmosContainer("registration_challenges")
# ...

# Functions to get container instances for dependency injection
def get_invite_tokens_container():
    return invite_tokens_container

def get_registration_challenges_container():
    return registration_challenges_container
# ...
```

## Model Serialization

The `CosmosContainer` class uses the `model_dump_json_safe()` method to serialize Pydantic models. This method is defined in the `BaseModelWithExtra` class and handles:

- Converting datetime objects to ISO 8601 strings
- Converting snake_case field names to camelCase for API compatibility
- Handling nested models and lists

## Best Practices

1. **Always use the getter function pattern for dependency injection**:
   ```python
   container=Depends(get_invite_tokens_container)
   ```

2. **Use the enhanced methods for Pydantic models**:
   ```python
   container.create_model(model)  # Instead of container.create_item(body=model.model_dump_json_safe())
   ```

3. **Define shared container instances in a central location**:
   ```python
   # In a shared module
   my_container = CosmosContainer("my_container")

   def get_my_container():
       return my_container
   ```

4. **Use the `model_dump_json_safe()` method for manual serialization**:
   ```python
   data = model.model_dump_json_safe()
   ```

## Common Pitfalls

1. **Using the container directly with Depends**:
   ```python
   # WRONG - This gives you the ContainerProxy, not the CosmosContainer
   container=Depends(invite_tokens_container)

   # RIGHT - This gives you the CosmosContainer
   container=Depends(get_invite_tokens_container)
   ```

2. **Calling methods that don't exist on ContainerProxy**:
   ```python
   # WRONG - ContainerProxy doesn't have create_model
   container.create_model(model)

   # RIGHT - Use create_item with manual serialization if you have ContainerProxy
   container.create_item(body=model.model_dump_json_safe())
   ```

3. **Not initializing the container**:
   ```python
   # WRONG - This doesn't initialize the container
   container = CosmosContainer("my_container")()

   # RIGHT - This initializes the container
   container = CosmosContainer("my_container")
   container_client = container()
   ```

## Examples

### Example 1: Creating a New Item

```python
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.models.my_model import MyModel

# Define the container and getter function
my_container = CosmosContainer("my_container")

def get_my_container():
    return my_container

# Use it in a route
@router.post("/items")
async def create_item(
    item_data: MyModel,
    container=Depends(get_my_container)
):
    # Create the item using the enhanced method
    result = container.create_model(item_data)
    return result
```

### Example 2: Querying Items

```python
@router.get("/items")
async def get_items(
    name: str = None,
    container=Depends(get_my_container)
):
    if name:
        # Query items by name
        query = "SELECT * FROM c WHERE c.name = @name"
        params = [{"name": "@name", "value": name}]
        items = list(container.query_items(
            query=query,
            parameters=params,
            enable_cross_partition_query=True
        ))
    else:
        # Get all items
        items = list(container.read_all_items())

    return items
```

### Example 3: Updating an Item

```python
@router.put("/items/{item_id}")
async def update_item(
    item_id: str,
    item_data: MyModel,
    container=Depends(get_my_container)
):
    # Check if the item exists
    try:
        container.read_item(item=item_id, partition_key=item_id)
    except Exception:
        raise HTTPException(status_code=404, detail="Item not found")

    # Update the item using the enhanced method
    result = container.replace_model(model=item_data)
    return result
```
