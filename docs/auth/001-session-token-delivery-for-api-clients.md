# ADR-001: Session Token Delivery for API Clients

**Status:** Accepted

**Date:** 2025-05-15

**Context:**

Following a successful authentication (e.g., via passkey login), clients require a session token to make subsequent authenticated requests to the API. The backend was already generating a session token (JWT) and setting it as an `HttpOnly` cookie, which is suitable for web browser clients. However, API clients, such as the Android mobile application, typically expect to receive the session token directly in the response body of the authentication verification endpoint (e.g., `/auth/login/verify`).

A bug was identified where the `/auth/login/verify` endpoint, despite successful passkey verification and session token generation (including setting the cookie), was not including the session token string in the JSON response body. This led to deserialization errors on the Android client, which was designed to expect a `session_token` field in the `LoginVerifyResponse`.

This ADR clarifies the standard mechanism for providing session tokens to different types of clients.

**Decision:**

1.  **Session Token Generation:** Upon successful authentication, the backend will continue to generate a session token (JWT).
2.  **Cookie for Web Clients:** For all successful authentications, the session token will be set as an `HttpOnly`, `Secure`, `samesite=strict` cookie. This primarily serves web browser clients and enhances security by preventing direct JavaScript access to the token.
3.  **Token in JSON Body for API Clients:** For authentication verification endpoints (e.g., `/auth/login/verify`, `/auth/recovery/register/verify` if applicable, `/auth/register/verify`), the session token string **will also be included directly in the JSON response body**.
    - The Pydantic response models for these endpoints (e.g., `AuthenticationVerifyResponse`) will include a mandatory `session_token: str` field.
    - API clients (like mobile applications) are expected to extract this token from the response body, store it securely, and send it in the `Authorization: Bearer <token>` header for subsequent authenticated API calls.
4.  **Error Handling:** If session token generation fails after a successful credential verification, this should be treated as a server-side error. The API should not return a 2xx success response that is missing an expected session token if the client contract implies it's mandatory. An appropriate error response (e.g., HTTP 500) should be returned, or the Pydantic model validation (due to the mandatory `session_token` field) will naturally cause a server error if the token is `None`.

**Consequences:**

- **Positive:**

  - **Clear API Contract for Mobile/API Clients:** Mobile and other API clients have a clear and standard way to receive session tokens (via JSON body), facilitating explicit token management.
  - **Consistency with Common Practices:** This aligns with common patterns for authenticating mobile apps and SPAs against APIs.
  - **Bug Resolution:** Fixes the previously identified bug where the Android client could not deserialize the login verification response due to a missing `session_token`.
  - **Supports Multiple Client Types:** The backend can effectively support both web browser clients (via cookies) and API clients (via token in body) with a unified session token generation mechanism.
  - **Improved Debuggability:** When issues arise, it's clear whether the token was provided in the response body.

- **Negative:**
  - **Slightly Larger Response Body:** Including the token in the JSON body slightly increases the size of the authentication response, but this is generally negligible for JWTs.
  - **Backend Responsibility:** The backend must ensure the token is correctly included in the response model. Pydantic's model validation for mandatory fields helps enforce this.
