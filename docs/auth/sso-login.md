# Azure AD SSO Authentication

This document describes the Azure AD Single Sign-On (SSO) implementation for internal employees in the Peeps App.

## Overview

The Azure AD SSO integration allows internal employees to authenticate using their Microsoft accounts, providing a seamless and secure authentication experience without requiring separate credentials.

## Features

- **Single Sign-On**: Internal employees can sign in using their Microsoft credentials
- **Cookie-Based Sessions**: Authentication state is maintained via secure cookies, not stored in the database
- **Integration with Existing Auth**: Works alongside the existing passkey authentication system
- **Audit Logging**: All authentication attempts are logged for security monitoring
- **Infrastructure as Code**: Azure AD app registration and configuration is managed through Bicep templates

## Architecture

### Components

1. **Azure AD Application**:
   - Registered in Azure Active Directory
   - Configured for internal organization access only
   - Deployed via Bicep templates

2. **Authentication Flow**:
   - OAuth 2.0 authorization code flow
   - JWT token validation
   - Session management via secure cookies

3. **Integration Points**:
   - Authentication middleware
   - Login page
   - Admin dashboard

## Implementation Details

### Azure AD Configuration

The Azure AD application is configured with the following settings:

- **Sign-in audience**: AzureADMyOrg (internal employees only)
- **Redirect URIs**: Environment-specific callback URLs
- **Token configuration**: ID tokens and access tokens
- **Application secrets**: Stored in Azure Key Vault

### Authentication Flow

1. User clicks "Sign in with Microsoft" on the login page
2. User is redirected to Microsoft login page
3. After successful authentication, Microsoft redirects back to the callback URL
4. The application validates the token and creates a session
5. User is redirected to the dashboard

### Session Management

- Sessions are maintained via HTTP-only, secure cookies
- Session tokens are JWT tokens with an 8-hour expiration
- No session data is stored in the database

### Public Endpoints

The following endpoints are accessible without authentication:

```
/auth/validate-invite
/auth/register/challenge
/auth/register/verify
/auth/login/challenge
/auth/login/verify
/auth/recovery/initiate
/auth/recovery/register/challenge
/auth/recovery/register/verify
/auth/login-page
/auth/azure-ad/login
/auth/azure-ad/callback
/dashboard/login
```

## Deployment

### Infrastructure Deployment

The Azure AD application is deployed using the `make deploy-infra` command:

```bash
make deploy-infra ENV=dev
```

This command:
1. Creates/updates the Azure AD application registration
2. Configures redirect URIs and other settings
3. Stores client ID, tenant ID, and client secret in Key Vault
4. Updates container app configuration

### Environment-Specific Configuration

Each environment (dev, stage, api) has its own Azure AD application configuration in the corresponding parameter files:

- `Infra/main-parameters-dev.json`
- `Infra/main-parameters-stage.json`
- `Infra/main-parameters-api.json`

## Usage

### For Users

1. Navigate to the application login page
2. Click "Sign in with Microsoft (Internal)"
3. Enter Microsoft credentials when prompted
4. After successful authentication, you'll be redirected to the dashboard

### For Developers

#### Adding Azure AD Authentication to a New Endpoint

1. Ensure the endpoint is not in the public paths list if it requires authentication
2. The authentication middleware will automatically check for both JWT and Azure AD authentication
3. Access user information from `request.state`:
   - `request.state.person_id`: The user's ID
   - `request.state.auth_source`: The authentication source ("azure_ad" or "jwt")
   - `request.state.user_email`: The user's email (Azure AD only)
   - `request.state.user_name`: The user's name (Azure AD only)

#### Testing Azure AD Authentication

For local testing:
1. Deploy the infrastructure to dev environment
2. Update your local.env file with the Azure AD configuration
3. Start the application
4. Navigate to the login page and test the Azure AD login flow

## Security Considerations

- Azure AD SSO is restricted to internal employees only
- All authentication attempts are logged for security monitoring
- Session tokens are stored in HTTP-only, secure cookies
- Token validation includes audience, issuer, and expiration checks
- Failed authentication attempts are tracked for suspicious activity detection

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**:
   - Ensure the redirect URI in the Azure AD application matches the callback URL in the application
   - Check for protocol (http vs https) and trailing slash differences

2. **Token Validation Errors**:
   - Check that the client ID, tenant ID, and client secret are correctly configured
   - Verify that the Azure AD application is properly registered

3. **Session Cookie Issues**:
   - Ensure cookies are enabled in the browser
   - Check for secure cookie issues when testing on non-HTTPS environments

### Logging

Authentication events are logged to:
- Console logs
- Audit logs in Cosmos DB
- Authentication monitoring dashboard

## Future Enhancements

- Role-based access control for internal employees
- Integration with Microsoft Graph API for additional user information
- Support for multi-tenant authentication
- Enhanced security features like conditional access policies
