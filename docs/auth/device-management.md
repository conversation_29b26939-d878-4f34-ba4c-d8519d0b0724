# Device Management

This document describes the device management functionality implemented in the Peeps API application.

## Overview

The device management feature allows users to view and manage the passkey credentials (devices) associated with their account. This includes viewing device details and renaming devices.

Devices and authentication tokens are tightly integrated in the system:
- Each device (passkey) can have multiple active authentication sessions (tokens)
- Device status (active/inactive) affects authentication capabilities

## User Interface

The device management UI is accessible from the Accounts page in the dashboard. It provides the following functionality:

1. **View Registered Devices**: Lists all passkeys/devices registered to the selected person
2. **Device Details**: Shows detailed information about a selected device
3. **Device Management Actions**:
   - Rename Device: Change the friendly name of a device

## Implementation Details

### Frontend Components

The device management UI consists of the following components:

1. **Device List**: A table showing all registered devices with basic information
2. **Device Details Panel**: A panel showing detailed information about a selected device

### API Endpoints

The device management functionality relies on the following API endpoints:

1. `GET /auth/devices` - List all passkeys for the current person
2. `PUT /auth/devices/{id}/rename` - Rename a passkey

### Data Models

#### Device Model (PasskeyCredential)

Each device (passkey credential) includes the following information:

- `id`: Unique identifier for the passkey record
- `credential_id`: WebAuthn credential ID
- `public_key`: WebAuthn public key
- `person_id`: ID of the person who owns the device
- `device_name`: User-friendly name for the device
- `device_type`: Type of device (browser, mobile, etc.)
- `device_os`: Operating system of the device
- `device_browser`: Browser used for registration
- `created_at`: When the device was registered
- `last_used_at`: When the device was last used for authentication
- `is_active`: Whether the device is currently active

#### Token Model (SessionToken)

Each authentication session token includes:

- `id`: Unique identifier for the token
- `person_id`: ID of the person who owns the token
- `token`: The JWT token value
- `device_info`: Information about the device, including `credential_id` reference
- `created_at`: When the token was created
- `expires_at`: When the token expires
- `last_used_at`: When the token was last used
- `is_active`: Whether the token is currently active

## Usage

### Viewing Devices

1. Navigate to the Accounts page in the dashboard
2. Select a person using the Peep Identity section
3. Click on "Device Management" in the sidebar
4. The list of registered devices will be displayed

### Managing Devices

#### Viewing Device Details

1. Click the "View" button next to a device in the list
2. The device details will be displayed in the panel below the list, including:
   - Device information (name, type, OS, browser)
   - Registration and last used dates
   - Active status

#### Renaming a Device

1. Select a device from the list
2. Click the "Rename" button in the device details panel
3. Enter a new name in the dialog
4. Click "Rename" to confirm

<!-- Device token revocation and deletion functionality has been removed -->

## Security Considerations

- Device management requires authentication and proper authorization
- Sensitive device operations (rename) require confirmation
- The system maintains a relationship between devices and tokens for security tracking

## Future Enhancements

The following enhancements could be added in the future:

1. **Device Status Toggle**: Enable/disable devices
2. **Authentication History**: Show a more detailed history of device usage
3. **Security Notifications**: Visual indicators for potential security issues
4. **Device Filtering**: Filter devices by type, status, or last used date
5. **Bulk Actions**: Perform actions on multiple devices at once

### Enhanced Device Information
- Store more detailed information about each device
- Display this information in the device management UI

### Security Alerts
- Implement alerts for suspicious device activity
- Tie these alerts to the device management UI

### Device Health Indicators
- Add visual indicators in the UI to show the security status of each device

## Troubleshooting

### Common Issues

1. **No devices shown**: Ensure a person is selected in the Peep Identity section
2. **Device list not refreshing**: Click the "Refresh Device List" button

### Error Messages

- "Please select a device first": You need to select a device before performing actions
- "Error fetching devices": There was an error retrieving the device list from the API
