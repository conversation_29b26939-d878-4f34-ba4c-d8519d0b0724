# Token Security in PeepsAPI

This document provides an overview of the token security features in PeepsAPI, including token generation, validation, revocation, and best practices.

## Table of Contents

- [Overview](#overview)
- [Token Architecture](#token-architecture)
- [Device-Token Relationship](#device-token-relationship)
- [Token Lifecycle](#token-lifecycle)
- [Token Revocation](#token-revocation)
- [Security Features](#security-features)
- [Best Practices](#best-practices)
- [API Endpoints](#api-endpoints)
- [Monitoring and Alerting](#monitoring-and-alerting)

## Overview

PeepsAPI uses JWT (JSON Web Tokens) for authentication and session management. Tokens are used to authenticate users across the application and provide a secure, stateless authentication mechanism.

Key features:
- JWT-based session tokens
- Token revocation capabilities
- Token usage tracking
- Secure cookie storage
- Device fingerprinting

## Token Architecture

### Components

1. **Token Service**: Central service for token management (`token_service.py`)
2. **Auth Service**: Authentication service that uses the token service (`auth_service.py`)
3. **Session Routes**: API endpoints for session management (`session.py`)
4. **Token Routes**: API endpoints for token revocation (`token.py`)
5. **Database**: Cosmos DB container for storing session tokens (`session_tokens`)

### Token Structure

JWT tokens contain the following claims:
- `sub`: Subject (person ID)
- `iat`: Issued at timestamp
- `exp`: Expiration timestamp
- `jti`: JWT ID (used for revocation)
- `device_fingerprint`: Device fingerprint for additional security

### Database Schema

Session tokens are stored in the `session_tokens` container with the following schema:

```json
{
  "id": "string",  // JWT ID (jti claim)
  "person_id": "string",  // Person ID (sub claim)
  "token": "string",  // JWT token
  "credential_id": "string",  // Reference to the PasskeyCredential
  "device_info": {
    "name": "string",
    "type": "string",
    "os": "string",
    "browser": "string",
    "ip": "string",
    "user_agent": "string",
    "last_ip": "string",  // Previous IP address
    "last_user_agent": "string",  // Previous user agent
    "credential_id": "string"  // Reference to the PasskeyCredential (for consistency)
  },
  "created_at": "string",  // ISO timestamp
  "expires_at": "string",  // ISO timestamp
  "last_used_at": "string",  // ISO timestamp
  "is_active": boolean  // Whether the token is active
}
```

## Device-Token Relationship

In the PeepsAPI authentication system, there are two main entities involved in device-based authentication:

1. **PasskeyCredential**: Represents a WebAuthn credential (passkey) registered on a user's device
2. **SessionToken**: Represents an active session for a user

These entities are linked through the `credential_id` field, which provides a direct reference from a SessionToken to the PasskeyCredential that was used to authenticate.

### Model Relationships

#### PasskeyCredential

- `id`: Unique identifier for the credential record
- `credential_id`: WebAuthn credential ID (base64url encoded)
- `device_info`: Contains device metadata, including a reference back to the credential_id

#### SessionToken

- `id`: Unique identifier for the session
- `credential_id`: Direct reference to the PasskeyCredential's credential_id
- `device_info`: Contains device metadata, including a reference to the credential_id

#### DeviceInfo

- `name`: User-friendly name for the device
- `type`: Type of device (e.g., "mobile", "desktop")
- `os`: Operating system of the device
- `browser`: Browser used
- `ip`: IP address
- `credential_id`: Reference to the PasskeyCredential

### Relationship Diagram

```
┌─────────────────┐       ┌─────────────────┐
│ PasskeyCredential│       │   SessionToken  │
├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │
│ credential_id   │◄──────┤ credential_id   │
│ device_info     │       │ device_info     │
└─────────────────┘       └─────────────────┘
```

### Security Benefits

The consistent device-token relationship provides several security benefits:

1. **Token Revocation**: Allows for precise revocation of all tokens associated with a specific device
2. **Device Management**: Enables users to see and manage all active sessions for each of their devices
3. **Suspicious Activity Detection**: Facilitates detection of unusual patterns by linking sessions to specific devices

## Token Lifecycle

### Creation

1. User authenticates via passkey or Azure AD
2. Token service creates a JWT token with appropriate claims
3. Token is stored in the database with device information
4. Token is set as a secure cookie in the response

### Validation

1. Token is extracted from the cookie
2. JWT signature and expiration are validated
3. Token is checked against the database to ensure it's active
4. Token usage is updated in the database

### Refresh

1. User requests a token refresh
2. Old token is revoked
3. New token is created and set as a cookie
4. Old token entry is marked as inactive in the database

### Revocation

1. User logs out or requests token revocation
2. Token is marked as inactive in the database
3. Cookie is cleared from the client

## Token Revocation

Token revocation functionality has been removed from the public API.

## Security Features

### Token Usage Tracking

The system tracks token usage, including:
- Last used timestamp
- IP address changes
- User agent changes

This information can be used to detect suspicious activity and potential token theft.

### Device Fingerprinting

Each token includes a device fingerprint based on:
- Operating system
- Browser
- Device type
- IP address
- User agent

This helps detect token reuse across different devices.

### Secure Cookie Storage

Tokens are stored in cookies with the following security attributes:
- `httponly`: Prevents JavaScript access
- `secure`: Requires HTTPS
- `samesite=strict`: Protects against CSRF attacks

### Database TTL

Session tokens in the database have a Time-To-Live (TTL) setting that automatically removes expired tokens.

## Best Practices

### For Developers

1. **Always use the token service**: Never create or validate tokens directly.
2. **Check token revocation**: Always validate tokens with `check_revocation=True`.
3. **Update token usage**: Call `update_token_usage` when using a token.
4. **Secure cookies**: Always set cookies with appropriate security attributes.
5. **Handle token errors**: Properly handle token validation errors.
6. **Maintain device-token relationship**: Always set the `credential_id` field in SessionToken when creating a new token.
7. **Use credential_id for queries**: Use the `credential_id` field for queries rather than looking inside device_info.
8. **Keep device_info consistent**: Ensure device_info is consistent between PasskeyCredential and SessionToken.

### For Administrators

1. **Monitor token usage**: Watch for suspicious token activity.
2. **Revoke tokens when needed**: Use the admin endpoints to revoke tokens.
3. **Set appropriate TTL**: Configure the database TTL based on security requirements.
4. **Rotate JWT secret**: Periodically rotate the JWT secret key.

## API Endpoints

### Session Management

- `POST /auth/logout`: Log out and revoke the current token
- `POST /auth/refresh-token`: Refresh the current token

## Monitoring and Alerting

The token security system includes monitoring and alerting for:

1. **Failed token validations**: Multiple failed validations may indicate an attack
2. **Token revocations**: Unusual patterns of token revocation
3. **IP address changes**: Sudden changes in IP address for a token
4. **User agent changes**: Changes in user agent for a token
5. **High volume of token creation**: Unusual number of tokens being created

Alerts are logged and can be configured to notify administrators via email or other channels.

## Future Improvements

While the current implementation provides a solid foundation for token security, there are several enhancements that could be added in future iterations:

### 1. Suspicious Activity Detection

The current system tracks token usage patterns but doesn't actively detect suspicious activities. Future improvements could include:

- **Pattern recognition algorithms**: Implement machine learning models to detect unusual access patterns
- **Geolocation analysis**: Flag tokens being used from unusual or impossible geographic locations
- **Time-based analysis**: Detect tokens being used at unusual times for a specific user
- **Velocity checks**: Identify impossible travel scenarios (e.g., token used in New York and Tokyo within minutes)

### 2. Enhanced Alerting System

The current system logs security events, but a more comprehensive alerting system could be implemented:

- **Real-time alerts**: Send immediate notifications for high-risk security events
- **Configurable alert thresholds**: Allow administrators to set custom thresholds for different alert types
- **Alert aggregation**: Group related alerts to prevent alert fatigue
- **Escalation paths**: Define escalation procedures for different types of security incidents
- **Integration with external systems**: Connect with SIEM (Security Information and Event Management) systems

### 3. Token Usage Dashboard

A dedicated dashboard for token usage would provide valuable insights:

- **Usage statistics**: Visualize token creation, validation, and revocation patterns
- **User activity timelines**: Show token usage over time for specific users
- **Security incident tracking**: Track and manage security incidents related to tokens
- **Device fingerprint analysis**: Identify common devices and flag unusual ones
- **Audit logs**: Provide detailed audit logs for compliance and security investigations

### 4. Session Management UI

Enhance the user interface for managing authentication sessions:

- **Sessions tab**: Add a dedicated tab in the device details panel to show all active sessions for a device
- **Individual session revocation**: Allow revoking individual sessions rather than all sessions at once
- **Session details**: Show detailed information about each session, including creation time, last activity, and IP address

### 5. Enhanced Token Information

Improve the information stored and displayed for each authentication session:

- **Detailed session metadata**: Store more comprehensive information about each session, such as IP address, location, and last activity
- **Rich session history**: Maintain a history of session activities and changes
- **Visual session timeline**: Display this information in an intuitive UI within the device management interface

### 6. Security Alerts Integration

Integrate security alerts directly into the user interface:

- **Suspicious activity notifications**: Implement alerts for unusual token activity (e.g., tokens being used from unusual locations)
- **Device-specific alerts**: Tie these alerts to the device management UI for easy investigation
- **Alert response actions**: Allow administrators to take immediate action from the alert interface

### 7. Unified Authentication Dashboard

Create a comprehensive view of authentication across the system:

- **Holistic authentication view**: Develop a dashboard that shows all devices and sessions in one interface
- **Relationship visualization**: Include visual indicators of the relationship between devices and tokens
- **Security status overview**: Provide at-a-glance security status for all authentication components

### 8. Token Lifecycle Management

Implement more sophisticated token lifecycle controls:

- **Flexible expiration policies**: Allow users to set different expiration policies for different devices
- **Automatic token refresh**: Implement mechanisms to refresh tokens without disrupting the user experience
- **Scheduled token rotation**: Enable scheduled rotation of tokens for enhanced security

### 9. Device Health Indicators

Add visual indicators of device security status:

- **Security health score**: Develop a scoring system for device security health
- **Visual indicators**: Add UI elements that show the security status of each device
- **Usage pattern visualization**: Include information about token usage patterns to help identify anomalies

### 10. Detailed Sessions API Endpoint

Enhance the session management capabilities with more detailed API endpoints:

- **Session details endpoint**: Create an API endpoint to fetch detailed session information for a device
- **Client-side integration**: Update the fetchDeviceSessions function to use this endpoint
- **Rich session display**: Show detailed session information including creation time, last activity, IP address, etc.

### 11. Individual Session Revocation

Provide more granular control over session management:

- **Single session revocation endpoint**: Implement an API endpoint to revoke individual sessions
- **UI controls**: Add UI elements to allow revoking specific sessions
- **Real-time updates**: Update the Sessions tab to reflect changes after session revocation

### 12. Session Activity Timeline

Implement comprehensive session activity tracking:

- **Activity logging**: Track session activities such as login, token refresh, and IP changes
- **Visual timeline**: Display a timeline of session activities in the UI
- **Geolocation integration**: Include information about location, IP changes, and other relevant data

These improvements would further enhance the security posture of the system and provide administrators with better tools for monitoring and responding to security incidents.
