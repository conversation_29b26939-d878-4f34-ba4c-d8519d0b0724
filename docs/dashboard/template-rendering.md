# Template Rendering System

This document describes the template rendering system used in the Peeps API application.

## Overview

The Peeps API uses Jinja2 as its template engine for rendering HTML templates. This allows for dynamic content generation with features like loops, conditionals, and variable substitution.

## Template Location

Templates are stored in the `static/templates` directory. Each template is an HTML file that can include Jinja2 syntax for dynamic content.

## Usage

To render a template, use the `render_template` function from `peepsapi.utils.templates`:

```python
from peepsapi.utils.templates import render_template

# Create context for template rendering
context = {
    "variable_name": variable_value,
    "list_variable": [item1, item2, item3],
    "dict_variable": {"key1": "value1", "key2": "value2"}
}

# Render the template
template_content = render_template("template_name.html", context)
```

## Template Syntax

Templates use Jinja2 syntax for dynamic content:

### Variable Substitution

```html
<p>Hello, {{ name }}!</p>
```

### Loops

```html
<ul>
    {% for item in items %}
    <li>{{ item.name }}: {{ item.value }}</li>
    {% endfor %}
</ul>
```

### Conditionals

```html
{% if user_authenticated %}
<p>Welcome back, {{ user_name }}!</p>
{% else %}
<p>Please log in to continue.</p>
{% endif %}
```

### Filters

```html
<p>{{ text | upper }}</p>
<p>{{ timestamp | date }}</p>
```

## Available Templates

The application includes the following templates:

- `dashboard.html` - Main dashboard layout
- `login.html` - Login page
- `accounts.html` - Account management page
- `auth-stats.html` - Authentication statistics
- `error-stats.html` - Error statistics
- `rate-limit-stats.html` - Rate limiting statistics

## Error Handling

If a template cannot be found or an error occurs during rendering, the `render_template` function will return `None`. The calling code should handle this case appropriately, typically by returning a fallback HTML response.

## Best Practices

1. **Keep templates simple**: Templates should focus on presentation, not business logic.
2. **Use consistent naming**: Use descriptive names for templates and variables.
3. **Provide fallbacks**: Always handle the case where template rendering fails.
4. **Sanitize input**: Jinja2 automatically escapes variables, but be careful with `safe` filter.
5. **Document context**: Document the expected context variables for each template.

## Troubleshooting

If templates are not rendering correctly, check the following:

1. Ensure the template file exists in the `static/templates` directory.
2. Verify that all required context variables are provided.
3. Check for syntax errors in the template.
4. Look for errors in the application logs.
