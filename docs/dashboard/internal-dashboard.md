# Internal Admin Dashboard

This document describes the internal admin dashboard implementation for the Peeps App.

## Overview

The internal admin dashboard provides a centralized interface for administrative functions, monitoring, and management of the Peeps App platform. It's designed for internal employees who authenticate via Azure AD SSO.

## Features

- **Authentication Monitoring**: Real-time statistics on authentication attempts, success rates, and suspicious activities
- **System Status**: Overview of system health and performance (placeholder for future implementation)
- **User-Friendly Interface**: Clean, modern UI with tabbed navigation
- **Responsive Design**: Works on desktop and mobile devices
- **Secure Access**: Restricted to authenticated internal employees

## Architecture

### Components

1. **Dashboard Framework**:
   - Main dashboard page with tabbed navigation
   - Modular content areas for different features
   - Responsive layout using CSS

2. **Authentication Monitoring**:
   - Real-time statistics from audit logs
   - Visualization of authentication patterns
   - Suspicious activity detection

3. **Template System**:
   - HTML templates stored in `static/templates/`
   - Template rendering with dynamic content
   - Separation of HTML from Python code

## Implementation Details

### Dashboard Structure

The dashboard is organized into tabs:

1. **Overview**: General information and summary
2. **Auth Monitoring**: Authentication statistics and monitoring
3. **System Status**: System health and performance (placeholder)

### Authentication Monitoring

The authentication monitoring tab provides:

- **Summary Statistics**: Total attempts, success/failure counts, overall success rate
- **Method Breakdown**: Statistics by authentication method (Azure AD, JWT)
- **Recent Events**: List of recent authentication attempts with details
- **Suspicious IPs**: Detection of potentially malicious IP addresses

### Template System

The dashboard uses a template system to separate HTML from Python code:

- **Template Files**: HTML templates stored in `static/templates/`
- **Template Rendering**: Dynamic content injected into templates
- **Context Variables**: Data passed from Python to templates

### Endpoints

The dashboard is accessible via the following endpoints:

- `/dashboard`: Main dashboard page
- `/dashboard/auth-stats`: Authentication statistics API endpoint
- `/dashboard/auth-stats-embed`: Embeddable HTML view of authentication statistics

## Usage

### Accessing the Dashboard

1. Navigate to the application login page
2. Sign in with Microsoft (internal employees only)
3. After successful authentication, you'll be redirected to the dashboard

### Using the Dashboard

1. **Navigation**: Use the tabs at the top to navigate between different sections
2. **Auth Monitoring**: View authentication statistics and detect suspicious activities
3. **Data Refresh**: Use the refresh button in each section to update the data

### Authentication Monitoring

The authentication monitoring tab provides:

- **Real-time Statistics**: View authentication attempts, success rates, and failures
- **Method Breakdown**: Compare different authentication methods
- **Suspicious Activity**: Identify potential security threats
- **Recent Events**: Track recent authentication attempts

## Development

### Adding New Dashboard Features

To add a new feature to the dashboard:

1. **Create a Template**: Add a new HTML template in `static/templates/`
2. **Add a Route**: Create a new route in `peepsapi/dashboard/routes/`
3. **Update the Dashboard**: Add a new tab in `dashboard.html`

### Template Structure

Templates follow this structure:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Title</title>
    <style>
        /* CSS styles */
    </style>
</head>
<body>
    <div class="container">
        <!-- Content with {{placeholder}} variables -->
    </div>
    <script>
        // JavaScript code
    </script>
</body>
</html>
```

### Template Rendering

Templates are rendered using the `render_template` function:

```python
from peepsapi.utils.templates import render_template

# Create context for template rendering
context = {
    "variable_name": variable_value
}

# Render the template
template_content = render_template("template_name.html", context)
```

## Security Considerations

- Dashboard access is restricted to authenticated internal employees
- Authentication is required for all dashboard endpoints
- Sensitive information is appropriately sanitized before display
- All dashboard activities are logged for audit purposes

## Performance Considerations

- Authentication statistics are cached to reduce database queries
- Templates are rendered server-side for faster loading
- Embedded content uses iframes to isolate rendering

## Future Enhancements

- **User Management**: Interface for managing user accounts and permissions
- **System Monitoring**: Real-time monitoring of system performance and health
- **Audit Log Viewer**: Interface for browsing and searching audit logs
- **Configuration Management**: Interface for managing application configuration
- **Analytics Dashboard**: Advanced analytics and reporting features
