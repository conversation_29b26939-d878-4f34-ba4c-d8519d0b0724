# Passkey Authentication

This document provides an overview of the passkey authentication feature in PeepsAPI, including how to use it, how it works, and how to test it.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [API Endpoints](#api-endpoints)
- [Usage Flow](#usage-flow)
- [Testing](#testing)
- [FIDO2 Implementation Details](#fido2-implementation-details)
- [Security Considerations](#security-considerations)
- [Future Improvements](#future-improvements)
- [Troubleshooting](#troubleshooting)

## Overview

Passkey authentication provides a passwordless authentication mechanism using the WebAuthn standard. It allows users to authenticate using biometrics (fingerprint, face recognition) or security keys, providing a more secure and user-friendly alternative to traditional passwords.

Key features:
- Registration via invite tokens
- Multi-device support
- Account recovery mechanisms
- Comprehensive security features

## Architecture

The passkey authentication system consists of several components:

### Database Collections

- `passkey_credentials`: Stores user credentials (partition key: credential ID)
- `registration_challenges`: Stores registration challenges (partition key: person ID)
- `authentication_challenges`: Stores authentication challenges (partition key: person ID)
- `invite_tokens`: Stores invite tokens (partition key: token ID)
- `session_tokens`: Stores active sessions (partition key: person ID)

### Core Components

- `challenge_service.py`: FIDO2 challenge generation and verification
- `auth_service.py`: Authentication flow management
- `token_service.py`: JWT token management
- `recovery.py`: Account recovery mechanisms
- `security.py`: Security features (audit logging, brute force protection)
- `router.py`: API endpoints

## API Endpoints

### Registration

- `POST /auth/invite`: Create an invite token
- `POST /auth/register/challenge`: Generate registration challenge
- `POST /auth/register/verify`: Verify registration response

### Authentication

- `POST /auth/login/challenge`: Generate authentication challenge
- `POST /auth/login/verify`: Verify authentication response
- `POST /auth/logout`: Log out current user
- `POST /auth/refresh-token`: Refresh the session token

### Device Management

- `GET /auth/devices`: List registered devices
- `POST /auth/devices/register`: Register a new device (not implemented yet)
- `POST /auth/devices/verify`: Verify new device registration (not implemented yet)
- `DELETE /auth/devices/{credential_id}`: Remove a device
- `PUT /auth/devices/{device_id}/rename`: Rename a device

### Recovery

- `POST /auth/recover/initiate`: Initiate account recovery
- `POST /auth/recover/challenge`: Create registration challenge for recovery
- `POST /auth/recover/verify`: Verify registration for recovery

## Usage Flow

### Registration Flow

1. Admin creates an invite token for a new user
2. User receives the invite token
3. User's browser generates a registration challenge (token validation happens automatically)
4. User completes the challenge (e.g., using fingerprint)
5. User's browser sends the response to the server
6. Server verifies the response and creates a user account

### Authentication Flow

1. User enters their email
2. Server generates an authentication challenge
3. User's browser presents the challenge to the user
4. User completes the challenge (e.g., using fingerprint)
5. User's browser sends the response to the server
6. Server verifies the response and issues a session token

### Device Management Flow

1. User authenticates
2. User can view and manage their registered devices
3. User can rename devices using the device rename endpoint
4. User can remove devices (except the last active device)

Note: The flow for adding new devices is not yet implemented but will follow a similar pattern to the registration flow.

### Recovery Flow

1. User initiates recovery with their email or phone number
2. Server sends a recovery link with a token to the user's email or phone
3. User clicks the recovery link, which contains the recovery token
4. The recovery token is used to create a registration challenge
5. User completes the challenge (e.g., using fingerprint)
6. User's browser sends the response to the server
7. Server verifies the response and updates the user's credentials

## Testing

### Environment Setup

Before testing, ensure your environment is properly configured:

1. Set the `ENVIRONMENT` variable to "development" or "dev" in your `.env` file
2. Verify that the WebAuthn configuration in your `.env` file matches your test environment:
   ```
   WEBAUTHN_RP_ID=local.peepsapp.ai
   WEBAUTHN_RP_NAME=Peeps App (Local)
   WEBAUTHN_RP_ORIGIN=https://local.peepsapp.ai:8443
   ```

### Test UI

A test UI is provided for manual testing of the passkey authentication flow. To use it:

1. Start the API server: `make start-https`
2. Open https://local.peepsapp.ai:8443/dashboard/accounts in your browser

The test UI provides a simple interface for testing all aspects of the passkey authentication system, including:
- Testing recovery flow
- Testing login flow
- Viewing device information

### Accounts Page

The Accounts page at `dashboard/` allows you to:
1. Test the recovery flow (initiate recovery, create challenge, register passkey)
2. Test the login flow (get authentication challenge, authenticate with passkey)
3. View device information detected by the browser

## FIDO2 Implementation Details

The passkey authentication system uses the FIDO2 WebAuthn standard for secure, passwordless authentication. Here's how our implementation works:

### Architecture

Our FIDO2 implementation follows a layered approach:

1. **API Endpoints**: Handle HTTP requests and responses
2. **Auth Service**: Provides a unified interface for credential verification
3. **Challenge Service**: Handles the core FIDO2 operations
4. **Database**: Stores credentials, challenges, and session tokens

The `auth_service.verify_webauthn_credential` method serves as a unified entry point for both registration and authentication verification, automatically detecting the type of challenge and handling it appropriately.

### Registration Process

1. The server generates a challenge using `server.register_begin()` from the FIDO2 library
2. The client responds with attestation data containing the newly created credential
3. The server verifies the response using `server.register_complete()` with:
   - The stored challenge state
   - The client data (decoded from client_data_json)
   - The attestation object (decoded from attestation_object)
4. Upon successful verification, the server stores:
   - Credential ID (base64url encoded)
   - Public key (base64 encoded)
   - Sign count (for anti-replay protection)
   - AAGUID (Authenticator Attestation GUID)
   - Device information

### Authentication Process

1. The server generates a challenge using `server.authenticate_begin()`
2. The client responds with assertion data proving possession of the private key
3. The server verifies the response using `server.authenticate_complete()` with:
   - The stored challenge state
   - The client data
   - The authenticator data
   - The signature
4. Upon successful verification, the server:
   - Updates the credential's sign count
   - Issues a session token
   - Updates the last used timestamp

### Credential Storage

Each passkey credential includes:
- Credential ID: Unique identifier for the credential
- Public key: Used to verify signatures
- Sign count: Prevents replay attacks
- AAGUID: Identifies the authenticator model
- Device information: Name, type, OS, browser, etc.

### Implementation Notes

- We use the `fido2` Python library for WebAuthn operations
- All API payloads are automatically translated to snake_case via middleware
- The `websafe_decode` utility from the FIDO2 library is used for decoding base64url-encoded data
- Challenge state is stored in the database and retrieved during verification
- The `auth_service.verify_webauthn_credential` method serves as a unified entry point for both registration and authentication verification
- Origin verification is handled by a custom function that can be configured for different environments

## Security Considerations

The passkey authentication system includes several security features:

- **Audit Logging**: All security-related events are logged
- **Brute Force Protection**: Limits the number of failed authentication attempts
- **Rate Limiting**: Prevents abuse of API endpoints
- **Session Management**: Secure session tokens with proper expiration
- **Device Fingerprinting**: Tracks device information for suspicious activity detection
- **Sign Count Verification**: Prevents replay attacks by tracking credential usage

### Implementation Notes

- **Algorithm Type Workaround**: For registration, we use a simplified approach that doesn't rely on the FIDO2 library's internal classes when encountering algorithm type errors. This is a workaround for compatibility with certain authenticators, but it means we're not performing full attestation verification and may use dummy values for the public key, AAGUID, and sign count. In a high-security environment, this should be replaced with a more robust solution.

- **Permissive Origin Verification**: For development, we use a permissive origin verification function. In production, this should be replaced with a more secure function that checks against the configured RP origin and ID.

## Future Improvements

The following enhancements could be implemented to further improve the passkey authentication system:

### Session Management UI
- Add a "Sessions" tab in the device details panel to show all active sessions for a device
- Allow revoking individual sessions rather than all sessions at once
- Provide detailed session information including creation time, last activity, and IP address

### Enhanced Token Information
- Store more detailed information about each session, such as IP address, location, and last activity
- Maintain a history of session activities and changes
- Display this information in an intuitive UI within the device management interface

### Security Alerts
- Implement alerts for suspicious token activity (e.g., tokens being used from unusual locations)
- Tie these alerts to the device management UI for easy investigation
- Allow administrators to take immediate action from the alert interface

### Unified Authentication Dashboard
- Create a comprehensive dashboard that shows all devices and sessions in one interface
- Include visual indicators of the relationship between devices and tokens
- Provide at-a-glance security status for all authentication components

### Token Lifecycle Management
- Implement automatic token expiration and refresh mechanisms
- Allow users to set different expiration policies for different devices
- Enable scheduled rotation of tokens for enhanced security

### Device Health Indicators
- Develop a scoring system for device security health
- Add UI elements that show the security status of each device
- Include information about token usage patterns to help identify anomalies

## Troubleshooting

### Common Issues

#### WebAuthn Not Supported

If you see an error about WebAuthn not being supported, ensure you're using a modern browser that supports the WebAuthn standard (Chrome, Firefox, Safari, Edge).

#### Registration/Authentication Fails

- Ensure you're using a secure context (HTTPS or localhost)
- Check that your device has biometric capabilities or a security key
- Verify that the invite token is valid and not expired
- Check that the cookie settings match your environment (secure=false for development)
- Verify that the WEBAUTHN_RP_ID matches your domain
- Check that the challenge hasn't expired (default timeout is 5 minutes)

#### FIDO2 Verification Errors

If you encounter errors during FIDO2 verification:
- Check the server logs for specific error messages from the FIDO2 library
- Ensure the client is sending the correct format for client_data_json and attestation_object/authenticator_data
- Verify that the challenge state is being properly stored and retrieved
- Check that the sign count is being properly tracked to prevent replay attacks

For algorithm type errors (e.g., "a bytes-like object is required, not 'ES256'"):
- Our implementation includes a workaround for cases where the algorithm type is a string instead of bytes
- This is handled by using a simplified approach that doesn't rely on the FIDO2 library's internal classes
- We extract the credential ID from the request and use it to create a credential
- For the public key, we try to extract it from the attestation statement or use a dummy value
- We use a default AAGUID and sign count

#### Origin Verification Issues

If you encounter errors related to origin verification:
- For development, we use a permissive origin verification function that always returns true
- In production, you should use a more secure function that checks against the configured RP origin and ID
- If you're seeing "verify_origin missing required positional argument" errors, check that the verify_origin function accepts variable arguments (*args)

#### Session Token Not Set

If authentication succeeds but no session token is set:
- In development environments, ensure the ENVIRONMENT variable is set to "development" or "dev"
- Check that the browser accepts cookies from your domain
- Verify that the authentication challenge container is using the correct partition key
- Check the server logs for any errors during authentication

#### Device Management Issues

- You cannot remove the last active device
- Ensure you're authenticated before attempting to manage devices

#### Recovery Issues

- Recovery tokens expire after 24 hours
- The recovery flow uses a token-based approach (no recovery codes)
- The recovery token is validated during the registration challenge creation

### Getting Help

If you encounter issues not covered here, please:
1. Check the server logs for error messages
2. Verify your browser console for client-side errors
3. Ensure your environment variables are correctly set
