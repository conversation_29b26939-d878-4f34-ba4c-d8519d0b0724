# Data Loaders Documentation

## Mock Data Loader

The mock data loader (`scripts/mockdata_loader.py`) generates mock data that matches the API's data model. It's designed to create consistent, related data for testing and development purposes.

### Features

- **Data Generation**: Creates mock data for all major entities:
  - People
  - Connections
  - Communities
  - Events

- **Incremental Updates**: Supports updating existing data:
  - Preserves existing records
  - Updates only changed fields
  - Maintains relationships

- **Validation**: Includes comprehensive data validation:
  - Format checking
  - Relationship validation
  - Data type verification
  - Required field validation

### Usage

```bash
# Generate all mock data for a given ENV
python scripts/mockdata_loader.py --env {test,dev,stage}
```

### Logging

- Configurable logging levels
- Detailed progress information
- Error reporting
- Operation summaries

### Data Validation

- Schema validation
- Relationship checking
- Data type verification
- Required field validation

### Error Handling

- Detailed error messages
- Progress tracking

## Contributing

When adding new features to the data loaders:

1. Follow the existing code style
2. Update documentation
3. Include error handling
4. Add logging statements

## License

Know Your People - 2025. All rights reserved. This software is proprietary and confidential. Unauthorized use, reproduction, or distribution is strictly prohibited.
