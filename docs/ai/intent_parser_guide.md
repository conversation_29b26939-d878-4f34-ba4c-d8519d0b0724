# Intent Parser Guide for Frontend Developers

## Overview

The Intent Parser is a core component of the Peeps API that processes natural language user input and extracts structured information about the user's intended action. This guide explains how the Intent Parser works, what parameters it accepts, what outputs it produces, and how to integrate with it from the frontend.

## How It Works

The Intent Parser uses OpenAI's language models to analyze user input and extract:

1. **Intent**: The type of action the user wants to perform
2. **Parameters**: Specific data needed for the action
3. **Context Needed**: Any missing information required to complete the action
4. **Data Needed**: Types of data that should be retrieved from the database

This structured information is then passed to the Action Dispatcher, which routes the request to the appropriate handler.

## Available Intents

The system currently supports three primary intents:

### 1. `search_network`

Used for searching people, communities, or other entities in the network.

**Parameters:**

- `query` (required): The search term
- `filters` (optional): Additional filters to narrow the search

**Data Needed:**

- Can include: `people`, `communities`, `posts`, `comments`, `events`, etc.
- If not specified, defaults to `["people", "communities"]`

**Example Input:**

```
Search for people who know about machine learning
```

**Example Output:**

```json
{
  "intent": "search_network",
  "parameters": { "query": "machine learning" },
  "context_needed": [],
  "data_needed": ["people"]
}
```

### 2. `send_message`

Used for sending messages to individuals or communities.

**Parameters:**

- `recipient` (required): The name of the person or community
- `content` (required): The message content

**Data Needed:**

- For person recipients: `["people"]`
- For community recipients: `["communities"]`

**Example Input:**

```
Send a message to Ilya saying I found an iOS developer
```

**Example Output:**

```json
{
  "intent": "send_message",
  "parameters": { "recipient": "Ilya", "content": "I found an iOS developer" },
  "context_needed": [],
  "data_needed": ["people"]
}
```

#### Important Note

It won't actually send the message, it will prepare sending it. Once user approves it, it will be sent. Actual sending is handled at Frontend using CRUD requests.

### 3. `chat`

Used for general conversation and questions.

**Parameters:**

- `content` (required): The chat message or question

**Data Needed:**

- Varies based on the content of the message
- Can include any combination of data types
- May be empty for general questions

**Example Input:**

```
What are the upcoming events in the AI community?
```

**Example Output:**

```json
{
  "intent": "chat",
  "parameters": {
    "content": "What are the upcoming events in the AI community?"
  },
  "context_needed": [],
  "data_needed": ["events", "communities"]
}
```

## Intent Response Structure

Each intent handler returns a standardized response structure:

```json
{
  "success": true,
  "message": "Human-readable status message",
  "response": {
    "intent": "intent_type",
    "content": {
      // Intent-specific content structure
    }
  },
  "id": "action_id"
}
```

### Intent-Specific Response Structures

#### `search_network` Response

```json
{
  "intent": "search_network",
  "content": {
    "results": {
      "people": [
        {
          "id": "person_id",
          "name": "Person Name",
          "description": "Short description of why this person is relevant"
        }
      ],
      "communities": [
        {
          "id": "community_id",
          "name": "Community Name",
          "description": "Short description of why this community is relevant"
        }
      ]
    },
    "query": "original search query",
    "filters": "any applied filters",
    "data_types_searched": ["people", "communities"]
  }
}
```

#### `send_message` Response

```json
{
  "intent": "send_message",
  "content": {
    "recipient": {
      "id": "recipient_id",
      "name": "Recipient Name",
      "type": "person" or "community"
    },
    "text": "Message content"
  }
}
```

#### `chat` Response

```json
{
  "intent": "chat",
  "content": {
    "ai_response": "AI-generated response text",
    "references": ["Any references used in generating the response"],
    "soft_inferences": ["Any inferences made by the AI"],
    "suggested_explorations": ["Suggested follow-up topics"],
    "context_used": {} // Context data used to generate the response
  }
}
```

## Error Handling

If the Intent Parser encounters an error, it returns a standardized error response:

```json
{
  "success": false,
  "message": "Error message describing what went wrong",
  "response": null,
  "id": "action_id"
}
```

Common error scenarios include:

- Missing required parameters
- Entity not found (e.g., recipient for a message)
- OpenAI service unavailable

### API Endpoints

#### Process User Input

```
POST /ai/process
```

**Request Body:**

```json
{
  "user_input": "User's natural language input"
}
```

- `user_input`: The natural language input from the user

**Response:**
The structured response as described in the "Intent Response Structure" section.

### Entity Validation

The system automatically validates entities (like recipients) mentioned in the user input. For each entity, it returns:

- `entity_id`: The ID of the matched entity
- `entity_type`: The type of entity (person, community)
- `name`: The full name of the entity
- `confidence`: A score from 0.0 to 1.0 indicating match confidence
- `original_query`: The original text that was matched
- `additional_info`: Extra information about the entity

### What to keep in mind

1. **Handle Missing Context**: Check for `context_needed` in the response and prompt the user for any missing information
2. **User Editing**: Allow users to edit the extracted parameters before execution for `send_message` intent and similar.
