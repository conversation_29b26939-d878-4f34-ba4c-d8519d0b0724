# Picture Management

This document explains how profile pictures and other images are handled within the Peeps API.

## Picture Model

Pictures are stored in the `pictures` Cosmos DB container. Each record is represented by the `Picture` model and contains metadata about the stored blob:

- `id` – unique identifier for each uploaded picture
- `parent_type` – the associated model type (`person`, `post`, or `comment`)
- `parent_id` – ID of the owning object
- `blob_storage_path` – path within the Azure Blob container
- `thumbnail_paths` – dictionary of thumbnail blob paths (populated in Phase 6)
- `size_bytes`, `width`, `height`, `format`, `original-filename` , `content-type` – basic file information
- `gps`, `exif`, `is_animated`, `colors` , `mode` – more advanced file information
- `version` – monotonically increasing version per parent object
- `deleted` – soft delete flag

## Upload Lifecycle

1. **Validation** – `PictureService.upload_picture` verifies the file type and size using the constants in `peepsapi.crud.utils.constants`.
2. **Metadata Extraction** – Pillow is used to gather image dimensions and format information.
3. **Blob Upload** – the raw bytes are stored in the `pictures-originals` blob container.
4. **Record Creation** – a new `Picture` entry is inserted with metadata and version number. Any previous record for the same parent is soft deleted.

Thumbnail support will be added in Phase&nbsp;6. Thumbnail sizes and additional limits will be configurable in the same constants module.

## Retrieval and Fallback

`PictureService.get_picture` returns the latest non-deleted picture for a parent. If none exists, the service downloads a default image whose path is defined in `DEFAULT_PICTURE_PATH`. When the default blob is missing, the service returns empty bytes and logs a warning. API callers can rely on this behavior to show a placeholder image when users have not uploaded a profile picture.

The default images themselves live in the same blob container under a `defaults/` prefix. They can be replaced without changing code as long as the filenames remain the same.

## Testing the Fallback

Unit tests mock the blob service to ensure the service correctly downloads the default image when no user picture is found. Additional tests verify that an empty byte string is returned if the default blob is unavailable.
