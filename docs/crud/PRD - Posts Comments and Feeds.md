# Product Requirements Document (PRD)

**Title:**   Peeps App API Support for Posts, Comments, Timeline, and Feeds  
**Author:**  <PERSON><PERSON>  
**Date:**    June 2, 2025  
**Version:** 0.2 (MVP)

## 1. **Objective**
To develop a set of API endpoints and associated logic to enable production and consumption of user-generated content with peeps app in MVP scope.

## 2. **Background**
Creation of user-generated content (posts, comments) and consumption of user-generated content as a chronological sorted list (feed) are key patterns of the product use. These features drive platform engagement, and help users achive their personal and professional goals by sharing and recieving information, ie discovering opportunities, colleting opinions, etc.

## 3. **Scope**
- **In Scope:**
  - Public posts creation, edit, react (like), and (soft) deletion
  - Public post comments creation, edit, react (like) and (soft) deletion
  - User's timeline and feed
- **Out of Scope:**
  - Posting and commenting outside of user's timeline (e.g. community posts)
  - Non-public posting and commenting (e.g. visible for core-network only)
  - Posts re-posting
  - Non-image media types

## 4. **Requirements**

### 4.1 Functional Requirements

#### 4.1.1 Posts
- **FR1:** Users must be able to create a new post with Markdown text and media attachments (images).
- **FR2:** Users must be able to edit their own existing post.
- **FR3:** Users must be able to delete their own existing post.
- **FR4:** Users must be able to like existing post.

#### 4.1.2 Comments
- **FR5:** Users must be able to create a new post comment with Markdown text.
- **FR6:** Users must be able to create a new post comment directly under the post or under an existing comment.
- **FR7:** Users must be able to edit their own existing post comment.
- **FR8:** Users must be able to delete their own existing post comment.
- **FR9:** Users must be able to like existing post comment.

#### 4.1.3 Content Discovery
- **FR10:** Users must be able to get a chronological list of posts of a given user (timeline).
- **FR11:** Users must be able to get a chronological list of post authored by them an their active network (feed).
- **FR12:** Users must be able to get an individual post by its ID.
- **FR13:** Users must be able to get a chronological list of comments for a given post and level: root or child comments of a given comment.
- **FR13:** Users must be able to get lists of content in a reasonably sized batches (pages).
- **FR14:** Users must be able to get post and comments author previews.
- **FR15:** Deleted posts must be still visible without the content (text or media) to preserve on-going discussions.

### 4.2 Non-Functional Requirements
- **NFR2:** User input is limited to 10K characters and one (1) image.
- **NFR2:** As user's text supports markdown, thus would be converted to HTML on render: original input must be sanitized to avoid unexpected render artifacts.
- **NFR3:** Content page retrieval must be fast and use reasonable amount of Request Units (complexity of n, where n is a page size).
- **NFR4:** Content page retrieval cost (speed and RUs) must be constant regardless of the page number.

## 5. **Implementation details**

### 5.1 Endpoints

- `POST /posts/` create a post
- `PATCH /posts/<ID>` update the post
- `DELETE /posts/<ID>` soft-delete the post
- `GET /posts/` get posts of the authenticated user
- `GET/posts/<ID>` get the post of the authenticated user
- `GET /people/<author ID>/posts/` get posts of the given user
- `GET /people/<author ID>/posts/<ID>` get the post of the given user
- `POST /people/<author ID>/posts/<ID>/likes/` like the post by authenticated user
- `DELETE /people/<author ID>/posts/<ID>/likes/` unlike the post by authenticated user
- `GET /people/<author ID>/posts/<ID>/likes/` get a list of people who liked the post
- `GET /feed` get authenticated user feed

### 5.2 Pagination

- Page size is limited to POSTS_LIST_PAGE_SIZE (default to 10)
- Pages are retrieved sequentially, via `nextPage` tokens
- `nextPage` is a base64-encoded string containing information required for a next page retrieval: `created_at` and `id` of the last item of the page
- Paginated data logical co-location:
  - Data batched in pages is always stored in a single partition
  - For user post timeline: `posts` container partitioned by `author_person_id`
  - For comment list: `comments` container partitioned by `post_id`
  - For feed: `feeds` container partitioned by `person_id` (see section 5.5)

### 5.3 Posts

Posts are saved in `posts` container.

Post creation/update data model:
```
  content  str      <-- Mandatory field
  media    [Media]  <-- Attached images
  tags     [str]    <-- Reserved for future use
```

Post data model / container structure:
```
  <-- All fields from post creation model -->
  id                UUID
  author_person_id  UUID                    <-- Partition key
  comments_count    int
  reactions_count   int                     <-- Likes
  created_at        UTCDateTime
  updated_at        UTCDateTime | null
  deleted_at        UTCDateTime | null
  deleted_by        UUID | null             <-- author_person_id or AD user ID
  delete_reason     "by author"
                    | "by moderator"        <-- if done by AD user
                    | null
  visibility        "public" | "community"  <-- Always "public" for MVP
                    | "core" | "tagged"
  
  <-- Fields reserved for future use below -->
  parent_post_id    UUID | null
  community_id      UUID | null
  is_pinned         bool                    <-- Always "false" for MVP
  comment_previews  [CommentPreview]
```

### 5.4 Comments

Comments are saved in `comments` container.

Comment creation/update data model:
```
  content  str
```

Comment data model / container structure:
```
  <-- All fields from comment creation model -->
  id                  UUID
  target_id           UUID                <-- partition key, post ID or (in future) event ID
  target_type         "post" | "event"    <-- parent resource type, always "post" for MVP
  author_person_id    UUID                <-- Partition key
  comments_count      int
  reactions_count     int                 <-- Likes
  parent_comment_id   UUID | null
  created_at          UTCDateTime
  updated_at          UTCDateTime | null
  deleted_at          UTCDateTime | null
  deleted_by          UUID | null         <-- author_person_id or AD user ID
  delete_reason       "by author"
                      | "by moderator"    <-- if done by AD user
                      | null
```

### 5.5 Feed

#### 5.5.1 Overview

Feed pages are not retrieved from `posts` container directly, but from a dedicated `feeds` container. Each record represents a metadata of a `posts` container item + feed's owner (`person_id`). This design choice was made due to:
- Logical data co-location requirement for paginated data
- For predictable cost/performance of data retrieval

Feed data model / container structure:
```
  id                UUID         <-- Post's ID, partition unique
  person_id         UUID         <-- Partition key, feed's owner
  author_person_id  UUID         <-- Post's author
  created_at        UTCDateTime  <-- Post's creation timestamp
```

`feeds` container is populated ahead of time at the moment when a given post or a group of posts must be included. There are four events leading to feeds modifications:
1. New post creation
2. Exising post removal
3. Active connection creation
4. Active connection removal

#### 5.5.2 Future Development

1. Feeds will be capped, ie max 500 items per person
2. Feeds of not-active users could have a smaller cap and/or could be dynamically generated/extended upon login

Upon each event, a background job is created to update the feed (see section 5.6) outside of the scope of the API call which triggered the update.

### 5.6 Background Jobs

#### 5.6.1 Overview

Since feed updates depend on varible factors, a size of active network, and number of posts, these updates may take more than negligable time. Thus, feed updates must be separated from API calls that trigger them.

Features of background jobs:
- Jobs are executed separatelly from their trigger events
- A given job could be reserved by any worker (scalability)
- A given job is reserved by a single worker at a time (race conditions prevention)
- Failed jobs are preserved and retrtied

A new job is created by saving a record to `active_jobs` container.

Job data model:
```
  id              UUID                                     <-- Autogenerated, partition unique
  action          "add_post_to_feed"
                  | "remove_post_from_feed"
                  | "remove_each_others_posts_from_feeds"
                  | "add_each_others_posts_to_feeds"
  reserved_until  UTCDateTime                              <-- If set to a future timestamp, the job is reserved
  params                                                   <-- Format depends on action
  etag            str                                      <-- Used to avoid race condition upon reservation
                                                               (optimistic concurency control)
```

A worker is responsible for reserving and executing jobs.

#### 5.6.2 Lifecycle

1. Once the job is created, it's `reserved_until` is set to `now`, meaning the job is not reserved, the record is saved to `active_jobs` container
2. A worker is triggered to check for a new job immidiatelly (otherwise done automatically every 30 seconds)
3. A job is reserved by updating it's `reserved_until` to `now + 30 seconds`, job execution starts
4. Every job has an `action` and `params`, they are used to execute the job
5. Upon successful execution, the job is removed from the `active_jobs` container
6. Unsuccessful jobs would be retried automatically

#### 5.6.3 Future Development

1. A simple job system could be used for not feed update tasks, e.g. for updating connections person preview on profile updates
2. A cap could be imposed on the number of retries and/or a waiting time between retries could gradually increase
3. A dedicated job queue system could be used instead of a simple Cosmos DB container setup
