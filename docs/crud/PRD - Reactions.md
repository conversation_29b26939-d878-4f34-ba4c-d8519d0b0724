# Product Requirements Document (PRD)

**Title:**   Peeps App API Support for Reactions System
**Author:**  <PERSON><PERSON>
**Date:**    June 30, 2025
**Version:** 0.2 (MVP)

## 1. **Objective**
To develop a reaction system that enables users to express sentiment and engagement with posts and comments through standardized reaction types (currently "like") within the peeps app MVP scope.

## 2. **Background**
Reaction functionality is a key engagement pattern for social platforms, allowing users to quickly express sentiment without writing comments. This feature drives user engagement.

## 3. **Scope**
- **In Scope:**
  - Like reactions on posts and comments
  - Reaction creation, removal, and listing
  - Real-time reaction count updates
  - User reaction state context
  - Paginated reaction browsing

## 4. **Requirements**

### 4.1 Functional Requirements

#### 4.1.1 Reactions
- **FR1:** Users must be able to add a "like" reaction to posts and comments.
- **FR2:** Users must be able to remove their own reactions.
- **FR3:** Users must be able to update reaction type. System must prevent duplicate reactions from the same user on the same content.
- **FR4:** Reaction counts must be updated in real-time when reactions are added/removed.

#### 4.1.2 Reaction Discovery
- **FR5:** System must show user's current reaction state on content items.
- **FR6:** Users must be able to get a list of reactions for a given post or comment.
- **FR7:** Users must be able to get lists of reactions in reasonably sized batches (pages).
- **FR8:** Reaction counts must be displayed with posts and comments.

## 5. **Implementation details**

### 5.1 Endpoints

- `POST /reactions/{target_type}/{target_parent_id}/{target_id}` create or update a reaction
- `DELETE /reactions/{target_type}/{target_parent_id}/{target_id}` remove a reaction
- `GET /reactions/{target_id}` get reactions for a target (post or comment)

### 5.2 Pagination

- Page size is limited to REACTIONS_LIST_PAGE_SIZE (default to 10)
- Pages are retrieved sequentially, via `nextPage` tokens
- `nextPage` is a base64-encoded string containing information required for a next page retrieval: `created_at` and `id` of the last item of the page
- Paginated data logical co-location:
  - Data batched in pages is always stored in a single partition
  - For reaction lists: `reactions` container partitioned by `target_id`

### 5.3 Reactions

Each reactions is saved in two containers for optimized access patterns.

Reaction creation data model:
```
  type  "like"  <-- Only "like" supported in MVP
```

Reaction data model / container structure:
```
  <-- All fields from reaction creation model -->
  id                  UUID                    <-- Deterministic ID based on user + target
  target_id           UUID                    <-- Partition key for reactions container
  author_person_id    UUID                    <-- Partition key for person_reactions container
  created_at          UTCDateTime
  updated_at          UTCDateTime | null
  deleted             bool                    <-- Soft deletion support
  deleted_at          UTCDateTime | null
  deleted_by          UUID | null             <-- author_person_id or AD user ID
```

### 5.4 Reaction Counts

Reaction counts are embedded directly in posts and comments for fast display.

Reaction counts data model:
```
  reaction_counts     object
    like              int                     <-- Count of like reactions
  reaction_type       "like" | null           <-- Current user's reaction (populated per request)
```

### 5.5 Dual Storage Strategy

#### 5.5.1 Overview

Reactions are stored in two containers for optimized access patterns:
- `reactions` container: partitioned by `target_id` for content-based queries
- `person_reactions` container: partitioned by `author_person_id` for user-based queries

This design choice was made due to:
- Logical data co-location requirement for paginated data
- Different query patterns (content-centric vs user-centric)
- Predictable cost/performance of data retrieval

#### 5.5.2 Deterministic ID Generation

Reaction IDs are generated deterministically using:
```
id = uuid5(REACTION_NAMESPACE, sorted([user_id, target_id]))
```

Benefits:
- Prevents duplicate reactions
- Enables efficient lookups

### 5.6 Upsert Logic

Reaction operations use upsert logic to handle various scenarios:

1. **New Reaction**: Create in both containers, increment counter in target content
2. **Existing Active**: Update type if different (future use), increment/decrement counters as needed
3. **Existing Soft-Deleted**: Restore reaction, increment counter in target content
4. **Delete Operation**: Soft-delete reaction, decrement counter in target content

### 5.7 Counter Management

Reaction counts are maintained in the target content (posts/comments) for fast display:
- Incremented when reactions are created or restored
- Decremented when reactions are soft-deleted
- Always reflects only non-deleted reactions
- Updated atomically with reaction operations
