# Notes Feature Testing

This document outlines internal QA steps for validating the Notes feature before external rollout.

## Manual End-to-End Validation

1. **Create** a note about yourself and verify it is returned from the `/notes` endpoint.
2. **Create** a note about a different person and ensure it does not appear for other authors.
3. **Update** and **delete** notes, confirming that deleted notes are omitted from query results.
4. Create multiple notes for the same person to verify ordering and ownership.
5. Test large and complex markdown inputs to ensure they render correctly and are sanitized.

## Scale & Performance Testing

Use `scripts/notes_scale_test.py` to insert many notes for a single author:

```bash
python scripts/notes_scale_test.py --num 1000
```

The script reports time to create and fetch notes, helping identify throughput bottlenecks.

## Sorting Support

`GET /notes/{object_type}/{object_id}` returns notes sorted by `created_at` (descending) by default. No parameters are required.

## Markdown Safety

All note content is sanitized via `sanitize_markdown` before storage. Downstream consumers may render markdown without additional filtering.
