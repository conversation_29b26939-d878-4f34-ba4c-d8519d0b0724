# Peeps App Documentation

This directory contains documentation for various features and components of the Peeps App.

## Authentication

- [Azure AD SSO Authentication](auth/sso-login.md) - Documentation for the Azure AD Single Sign-On implementation for internal employees

## Administration

- [Internal Admin Dashboard](dashboard/internal-dashboard.md) - Documentation for the internal admin dashboard

## Development

- [Logging System](platform/LOGGING.md) - Documentation for the logging system, including local development and server logging
- [<PERSON>rro<PERSON> Handling](platform/error_handling.md) - Documentation for the error handling system
- [Database Usage](platform/database_usage.md) - Documentation for database operations and dependency injection patterns

## Development

- [Logging System](../LOGGING.md) - Documentation for the logging system, including local development and server logging
- [Error Handling](error_handling.md) - Documentation for the error handling system
- [Database Usage](database_usage.md) - Documentation for database operations and dependency injection patterns
- [Notes Feature Testing](crud/Notes.md) - Manual and scale testing instructions

## Getting Started

To get started with the Peeps App, follow these steps:

1. Clone the repository
2. Install dependencies with `make build`
3. Set up your local environment with `make generate-certs`
4. Start the application with `make start`

## Development Workflow

1. Create a new feature branch from `main`
2. Implement your changes
3. Run tests with `make test`
4. Run linting with `make lint`
5. Commit your changes with `make commit`
6. Create a pull request

## Deployment

Deployment is a two-step process:

1. Deploy infrastructure resources:
   ```bash
   # Deploy infrastructure to dev environment
   make deploy-infra ENV=dev

   # Deploy infrastructure to stage environment
   make deploy-infra ENV=stage

   # Deploy infrastructure to api environment
   make deploy-infra ENV=api
   ```

2. Deploy the application:
   ```bash
   # Deploy application to dev environment
   make deploy-service ENV=dev

   # Deploy application to stage environment
   make deploy-service ENV=stage

   # Deploy application to api environment
   make deploy-service ENV=api
   ```

## Additional Resources

- [Internal Dashboard](https://($ENV).peepsapp.ai/dashboard) - Internal Dashboard: Account managements, API Docs, and stats
- [Azure Portal](https://portal.azure.com) - Azure management portal
- [Azure AD Documentation](https://docs.microsoft.com/en-us/azure/active-directory/) - Microsoft's Azure AD documentation
