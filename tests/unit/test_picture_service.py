import io
import os
import sys
import types
from unittest.mock import patch
from uuid import uuid4

# Provide a minimal pydantic stub so picture_service can be imported without the
# real dependency. Only BaseModel is required for the service constructor.
pydantic_stub = types.ModuleType("pydantic")


class _BaseModel(dict):
    pass


pydantic_stub.BaseModel = _BaseModel
pydantic_stub.Field = lambda default=None, **kwargs: default
sys.modules.setdefault("pydantic", pydantic_stub)

# Provide a minimal azure storage stub to avoid import errors from azure_blob.
azure_blob_stub = types.ModuleType("azure")
storage_stub = types.ModuleType("azure.storage")
storage_blob_stub = types.ModuleType("azure.storage.blob")
storage_blob_stub.BlobServiceClient = object
storage_stub.blob = storage_blob_stub
azure_blob_stub.storage = storage_stub
sys.modules.setdefault("azure", azure_blob_stub)
sys.modules.setdefault("azure.storage", storage_stub)
sys.modules.setdefault("azure.storage.blob", storage_blob_stub)
# Additional stubs used during configuration import
identity_stub = types.ModuleType("azure.identity")
identity_stub.DefaultAzureCredential = object
keyvault_stub = types.ModuleType("azure.keyvault")
keyvault_secrets_stub = types.ModuleType("azure.keyvault.secrets")
keyvault_secrets_stub.SecretClient = object
keyvault_stub.secrets = keyvault_secrets_stub
azure_blob_stub.identity = identity_stub
azure_blob_stub.keyvault = keyvault_stub
sys.modules.setdefault("azure.identity", identity_stub)
sys.modules.setdefault("azure.keyvault", keyvault_stub)
sys.modules.setdefault("azure.keyvault.secrets", keyvault_secrets_stub)

# Stub dotenv.load_dotenv
dotenv_stub = types.ModuleType("dotenv")
dotenv_stub.load_dotenv = lambda *a, **k: None
sys.modules.setdefault("dotenv", dotenv_stub)

# Stub fastapi.Request used by logging; minimal placeholder
fastapi_stub = types.ModuleType("fastapi")
fastapi_stub.Request = object
fastapi_stub.HTTPException = type("HTTPException", (), {})
fastapi_stub.status = types.SimpleNamespace(HTTP_204_NO_CONTENT=204)
sys.modules.setdefault("fastapi", fastapi_stub)

# Stub azure.cosmos used by CosmosContainer
cosmos_stub = types.ModuleType("azure.cosmos")
cosmos_stub.CosmosClient = object
cosmos_stub.DatabaseProxy = object
sys.modules.setdefault("azure.cosmos", cosmos_stub)

# Provide a minimal PIL stub so picture_service can be imported without Pillow.
pil_stub = types.ModuleType("PIL")
pil_stub.Image = types.SimpleNamespace(open=lambda *a, **k: None)
sys.modules.setdefault("PIL", pil_stub)

# Stub crud models used by constants to avoid heavy Pydantic dependencies.
models_stub = types.ModuleType("peepsapi.crud.models")


class _Person:
    pass


class _Post:
    pass


class _Comment:
    pass


class _Picture:
    pass


_Person.__name__ = "Person"
_Post.__name__ = "Post"
_Comment.__name__ = "Comment"
_Picture.__name__ = "Picture"
models_stub.Person = _Person
models_stub.Post = _Post
models_stub.Comment = _Comment
models_stub.Picture = _Picture
sys.modules.setdefault("peepsapi.crud.models", models_stub)
sys.modules.setdefault("peepsapi.crud.models.person", models_stub)
sys.modules.setdefault("peepsapi.crud.models.post", models_stub)
sys.modules.setdefault("peepsapi.crud.models.comment", models_stub)
sys.modules.setdefault("peepsapi.crud.models.picture", models_stub)

# Ensure repository root is on the path so peepsapi can be imported when tests
# are run from arbitrary locations.
REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
if REPO_ROOT not in sys.path:
    sys.path.insert(0, REPO_ROOT)

import peepsapi.crud.services.picture_service as picture_service
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.picture_service import PictureService
from peepsapi.crud.utils.constants import DEFAULT_PICTURE_PATH, PICTURE_CONTAINER_NAME


def create_test_image_bytes() -> bytes:
    return b"fake-image-bytes"


def test_extract_image_metadata_basic_fields(monkeypatch):
    service = PictureService()

    class DummyImage:
        format = "PNG"
        mode = "RGB"
        is_animated = False

        def __init__(self):
            self.size = (10, 20)

        def __enter__(self):
            return self

        def __exit__(self, exc_type, exc, tb):
            pass

        def getexif(self):
            return {}

        def getcolors(self, maxcolors=256):
            return []

    dummy_module = types.SimpleNamespace(open=lambda *_args, **_kwargs: DummyImage())
    monkeypatch.setattr(picture_service, "Image", dummy_module)

    img_bytes = create_test_image_bytes()
    metadata = service.extract_image_metadata(img_bytes)
    assert metadata["width"] == 10
    assert metadata["height"] == 20
    assert metadata["format"] == "PNG"
    assert metadata["mode"] == "RGB"
    assert metadata["is_animated"] is False


@patch.object(PictureService, "_get_latest", return_value=None)
@patch("peepsapi.crud.services.picture_service.download_blob")
def test_get_picture_returns_default_when_missing(mock_download, mock_get_latest):
    mock_download.return_value = b"default"
    service = PictureService()
    metadata, data = service.get_picture(Person, uuid4())
    assert metadata is None
    assert data == b"default"
    mock_download.assert_called_once_with(
        PICTURE_CONTAINER_NAME, DEFAULT_PICTURE_PATH["person"]["full"]
    )


@patch.object(PictureService, "_get_latest", return_value=None)
@patch(
    "peepsapi.crud.services.picture_service.download_blob",
    side_effect=Exception("missing"),
)
def test_get_picture_returns_empty_when_default_missing(mock_download, mock_get_latest):
    service = PictureService()
    metadata, data = service.get_picture(Person, uuid4())
    assert metadata is None
    assert data == b""
    mock_download.assert_called_once_with(
        PICTURE_CONTAINER_NAME, DEFAULT_PICTURE_PATH["person"]["full"]
    )
