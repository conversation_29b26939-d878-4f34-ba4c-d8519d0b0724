import os
import sys
import types
from unittest.mock import patch
from uuid import uuid4

from fastapi import FastAPI
from fastapi.testclient import TestClient

# Stub azure modules used during imports
azure_stub = types.ModuleType("azure")
identity_stub = types.ModuleType("azure.identity")
identity_stub.DefaultAzureCredential = object
keyvault_stub = types.ModuleType("azure.keyvault")
keyvault_secrets_stub = types.ModuleType("azure.keyvault.secrets")
keyvault_secrets_stub.SecretClient = object
keyvault_stub.secrets = keyvault_secrets_stub
azure_stub.identity = identity_stub
azure_stub.keyvault = keyvault_stub
sys.modules.setdefault("azure", azure_stub)
sys.modules.setdefault("azure.identity", identity_stub)
sys.modules.setdefault("azure.keyvault", keyvault_stub)
sys.modules.setdefault("azure.keyvault.secrets", keyvault_secrets_stub)

cosmos_stub = types.ModuleType("azure.cosmos")
cosmos_stub.CosmosClient = object
cosmos_stub.DatabaseProxy = object

# Stub azure.cosmos.exceptions
cosmos_exceptions_stub = types.ModuleType("azure.cosmos.exceptions")
cosmos_exceptions_stub.CosmosHttpResponseError = Exception
cosmos_exceptions_stub.CosmosResourceNotFoundError = Exception
cosmos_stub.exceptions = cosmos_exceptions_stub

sys.modules.setdefault("azure.cosmos", cosmos_stub)
sys.modules.setdefault("azure.cosmos.exceptions", cosmos_exceptions_stub)

dotenv_stub = types.ModuleType("dotenv")
dotenv_stub.load_dotenv = lambda *a, **k: None
sys.modules.setdefault("dotenv", dotenv_stub)

# Stub auth services to avoid FIDO2 configuration requirements
auth_service_stub = types.ModuleType("peepsapi.auth.services.auth_service")


class _AuthService:
    def get_current_person(self):
        pass


auth_service_stub.auth_service = _AuthService()
sys.modules.setdefault("peepsapi.auth.services.auth_service", auth_service_stub)

# Stub challenge service to prevent module-level instantiation
challenge_service_stub = types.ModuleType("peepsapi.auth.services.challenge_service")


class _ChallengeService:
    pass


challenge_service_stub.ChallengeService = _ChallengeService
challenge_service_stub.challenge_service = _ChallengeService()
sys.modules.setdefault(
    "peepsapi.auth.services.challenge_service", challenge_service_stub
)

REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if REPO_ROOT not in sys.path:
    sys.path.insert(0, REPO_ROOT)

from peepsapi.crud.models.note import Note, NoteObjectType
from peepsapi.crud.routes import notes as notes_route
from peepsapi.crud.services.notes_service import NotesService
from peepsapi.utils.markdown import sanitize_markdown


class DummyNote:
    def __init__(self, **kwargs):
        self.version = kwargs.get("version", 1)
        for k, v in kwargs.items():
            setattr(self, k, v)

    @staticmethod
    def soft_delete_fields(author_id, delete_reason):
        from peepsapi.models import now

        return {
            "deleted": True,
            "deleted_at": now(),
            "deleted_by": author_id,
            "delete_reason": delete_reason,
            "updated_at": now(),
        }


class DummyContainer:
    def __init__(self):
        self.created = []
        self.patched = []
        self.storage = {}

    def create_model(self, model):
        self.created.append(model)
        self.storage[model.id] = model
        return model

    def query_models(self, query, model_class, parameters=None, partition_key=None):
        if parameters and len(parameters) >= 2:
            aid = parameters[0]["value"]
            oid = parameters[1]["value"]
            return [
                m
                for m in self.storage.values()
                if m.author_id == aid
                and m.object_id == oid
                and not getattr(m, "deleted", False)
            ]
        elif partition_key:
            # For get_notes_for_person (no object_id filter)
            return [
                m
                for m in self.storage.values()
                if m.author_id == partition_key and not getattr(m, "deleted", False)
            ]
        return []

    def read_model(self, id, partition_key, model_class):
        return self.storage[id]

    def patch_model(self, item, partition_key, update_fields, model_class=None):
        obj = self.storage[item]
        # Simulate authorization: raise exception if partition_key doesn't match author_id
        if hasattr(obj, "author_id") and obj.author_id != partition_key:
            from azure.cosmos.exceptions import CosmosResourceNotFoundError

            raise CosmosResourceNotFoundError()
        for k, v in update_fields.items():
            setattr(obj, k, v)
        self.patched.append((item, update_fields))
        return obj


# Unit Tests ---------------------------------------------------------------


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_create_note_populates_fields():
    service = NotesService()
    container = DummyContainer()

    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author, obj, NoteObjectType.PERSON, "hello", notes_container=container
    )
    assert note.author_id == author
    assert note.object_id == obj
    assert note.content == "hello"
    assert note.version == 1
    assert note.note_group_id
    assert note.created_at is not None
    assert note.updated_at is None


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_create_note_requires_content():
    service = NotesService()
    container = DummyContainer()
    author = uuid4()
    obj = uuid4()
    import pytest

    with pytest.raises(Exception):
        service.create_note(
            author, obj, NoteObjectType.PERSON, "", notes_container=container
        )


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_update_delete_authorization():
    service = NotesService()
    container = DummyContainer()
    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author, obj, NoteObjectType.PERSON, "hi", notes_container=container
    )
    other = uuid4()
    import pytest

    with pytest.raises(Exception):
        service.update_note(
            note.id, other, {"content": "no"}, notes_container=container
        )
    with pytest.raises(Exception):
        service.delete_note(note.id, other, notes_container=container)


@patch("peepsapi.crud.services.notes_service.Note", DummyNote)
def test_deleted_notes_not_returned():
    service = NotesService()
    container = DummyContainer()
    author = uuid4()
    obj = uuid4()
    note = service.create_note(
        author, obj, NoteObjectType.PERSON, "hi", notes_container=container
    )
    service.delete_note(note.id, author, notes_container=container)
    notes = service.get_notes_for_object(
        author, obj, NoteObjectType.PERSON, notes_container=container
    )
    assert notes == []


# Integration Tests -------------------------------------------------------


@patch("peepsapi.crud.routes.notes.service", NotesService())
def test_full_crud_flow(monkeypatch):
    container = DummyContainer()

    current_person = {"id": uuid4()}

    async def get_person():
        return current_person["id"]

    def get_container():
        return container

    app = FastAPI()
    app.include_router(notes_route.router)
    app.dependency_overrides[notes_route.auth_service.get_current_person] = get_person
    app.dependency_overrides[notes_route.get_notes_container] = get_container

    client = TestClient(app)

    obj_id = uuid4()

    # Create
    response = client.post(
        f"/notes/{NoteObjectType.PERSON.value}/{obj_id}",
        json={
            "content": "hi <script>x</script>",
        },
    )

    assert response.status_code == 200
    data = response.json()
    note_id = data["id"]
    assert data["content"] == "hi"
    author_id = current_person["id"]

    # Unauthorized update
    other = uuid4()
    current_person["id"] = other
    response = client.patch(f"/notes/{note_id}", json={"content": "bad"})
    assert response.status_code == 404

    # Authorized update
    current_person["id"] = author_id
    response = client.patch(f"/notes/{note_id}", json={"content": "updated"})
    assert response.status_code == 200
    assert response.json()["content"] == "updated"

    # Unauthorized delete
    current_person["id"] = other
    response = client.delete(f"/notes/{note_id}")
    assert response.status_code == 404

    # Authorized delete
    current_person["id"] = author_id
    response = client.delete(f"/notes/{note_id}")
    assert response.status_code == 200

    # Ensure gone
    response = client.get(f"/notes/{NoteObjectType.PERSON.value}/{obj_id}")
    assert response.status_code == 200
    assert response.json() == []
