{"version": 1, "disable_existing_loggers": false, "formatters": {"minimal": {"format": "%(levelname) | %(name)s | %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "standard": {"format": "%(asctime)s | %(levelname)s | %(name)s | %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "detailed": {"format": "%(asctime)s | %(levelname)s | %(name)s:%(lineno)d | %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "link": {"format": "%(asctime)s | %(levelname)s | '%(pathname)s':%(lineno)d | %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}}, "handlers": {"console": {"class": "logging.StreamHandler", "formatter": "standard", "level": "DEBUG", "stream": "ext://sys.stdout"}, "minimal_console": {"class": "logging.StreamHandler", "formatter": "minimal", "level": "DEBUG", "stream": "ext://sys.stdout"}, "detailed_console": {"class": "logging.StreamHandler", "formatter": "detailed", "level": "DEBUG", "stream": "ext://sys.stdout"}, "link_console": {"class": "logging.StreamHandler", "formatter": "link", "level": "DEBUG", "stream": "ext://sys.stdout"}}, "loggers": {"peepsapi": {"handlers": ["console"], "level": "INFO", "propagate": false}, "peepsapi.auth": {"handlers": ["console"], "level": "INFO", "propagate": false}, "peepsapi.dashboard": {"handlers": ["console"], "level": "INFO", "propagate": false}, "peepsapi.middleware": {"handlers": ["console"], "level": "INFO", "propagate": false}, "peepsapi.utils": {"handlers": ["console"], "level": "INFO", "propagate": false}, "peepsapi.crud": {"handlers": ["console"], "level": "INFO", "propagate": false}, "uvicorn": {"handlers": ["console"], "level": "INFO", "propagate": false}, "uvicorn.error": {"handlers": ["console"], "level": "INFO", "propagate": false}, "uvicorn.access": {"handlers": ["console"], "level": "INFO", "propagate": false}, "azure.core.pipeline.policies.http_logging_policy": {"handlers": ["detailed_console"], "level": "WARNING", "propagate": false}}, "root": {"handlers": ["console"], "level": "INFO"}}